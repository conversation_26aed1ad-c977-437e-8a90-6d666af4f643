#!/usr/bin/env python3
"""
Demo test to verify classification module functionality.
This test demonstrates how the classification module works with sample data.
"""

import asyncio
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main, get_document_types_with_bedrock
from app.utils.textract import TextractProcessor
from app.llm.bedrock import BedrockProcessor


async def demo_classification_with_mocks():
    """Demonstrate classification functionality with mocked AWS services."""
    print("🧪 Demo: Classification with Mocked AWS Services")
    print("=" * 60)
    
    # Sample S3 URI
    s3_uri = "s3://test-bucket/sample-bol.pdf"
    
    # Mock Textract response
    mock_textract_response = {
        "DocumentMetadata": {"Pages": 1},
        "Blocks": [
            {
                "BlockType": "PAGE",
                "Id": "page-1",
                "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}}
            }
        ]
    }
    
    # <PERSON><PERSON> extracted text with page tags
    mock_extracted_text = """<page1>
BILL OF LADING
Shipper: ABC Logistics Inc.
123 Main Street
New York, NY 10001

Consignee: XYZ Distribution Corp.
456 Oak Avenue
Los Angeles, CA 90001

BOL Number: BOL-2024-001234
Date: 2024-01-15

Commodity: Electronics
Weight: 1,500 lbs
Pieces: 25 boxes
</page1>"""
    
    # Mock Bedrock response
    mock_bedrock_response = {
        "stopReason": "tool_use",
        "output": {
            "message": {
                "content": [
                    {"text": "I can see this is a Bill of Lading document based on the header and structure."},
                    {
                        "toolUse": {
                            "input": {
                                "documents": [
                                    {"page_no": 1, "doc_type": "bol"}
                                ]
                            }
                        }
                    }
                ]
            }
        }
    }
    
    # Expected classification result
    expected_classification = {
        "documents": [
            {"page_no": 1, "doc_type": "bol"}
        ]
    }
    
    # Create mocks
    mock_textract = Mock(spec=TextractProcessor)
    mock_textract.process_document_classification.return_value = (
        mock_extracted_text, 
        [mock_textract_response]
    )
    
    mock_bedrock = Mock(spec=BedrockProcessor)
    mock_bedrock.call_bedrock_converse.return_value = mock_bedrock_response
    mock_bedrock.extract_tool_response.return_value = expected_classification
    
    # Test the classification pipeline
    with patch('app.llm.classification.TextractProcessor', return_value=mock_textract), \
         patch('app.llm.classification.BedrockProcessor', return_value=mock_bedrock):
        
        print(f"📄 Processing document: {s3_uri}")
        print(f"📝 Extracted text preview: {mock_extracted_text[:100]}...")
        
        # Run classification
        result = await main(s3_uri)
        
        print("\n✅ Classification Results:")
        print(f"   Document Type: {result['classification_result']['documents'][0]['doc_type']}")
        print(f"   Page Number: {result['classification_result']['documents'][0]['page_no']}")
        print(f"   AWS Region (Textract): {result['execution_info']['textract']['aws_region']}")
        print(f"   AWS Region (Bedrock): {result['execution_info']['bedrock']['aws_region']}")
        print(f"   Model ID: {result['execution_info']['bedrock']['model_id']}")
        
        # Verify structure
        assert 'classification_result' in result
        assert 'textract_response_object' in result
        assert 'bedrock_response_object' in result
        assert 'execution_info' in result
        
        print("\n🎯 Test Results:")
        print("   ✓ Classification result structure is correct")
        print("   ✓ Document type correctly identified as 'bol'")
        print("   ✓ All required response fields present")
        print("   ✓ Execution completed successfully")
        
        return result


async def demo_bedrock_function():
    """Demonstrate the Bedrock classification function directly."""
    print("\n🤖 Demo: Direct Bedrock Classification Function")
    print("=" * 60)
    
    # Sample document text
    sample_text = """<page1>
COMMERCIAL INVOICE
Invoice Number: INV-2024-5678
Date: January 20, 2024

Seller: Global Exports Ltd.
789 Trade Street
Miami, FL 33101

Buyer: International Imports Inc.
321 Commerce Blvd
Houston, TX 77001

Description: Industrial Equipment
Quantity: 10 units
Unit Price: $2,500.00
Total Amount: $25,000.00
</page1>"""
    
    # Mock Bedrock processor
    mock_bedrock = Mock(spec=BedrockProcessor)
    mock_bedrock.call_bedrock_converse.return_value = {
        "stopReason": "tool_use",
        "output": {
            "message": {
                "content": [
                    {"text": "This appears to be a commercial invoice based on the format and content."},
                    {
                        "toolUse": {
                            "input": {
                                "documents": [
                                    {"page_no": 1, "doc_type": "invoice"}
                                ]
                            }
                        }
                    }
                ]
            }
        }
    }
    
    expected_result = {
        "documents": [
            {"page_no": 1, "doc_type": "invoice"}
        ]
    }
    
    mock_bedrock.extract_tool_response.return_value = expected_result
    
    print(f"📝 Document text preview: {sample_text[:100]}...")
    
    # Test Bedrock classification function
    classification_result, bedrock_response = await get_document_types_with_bedrock(
        mock_bedrock, sample_text
    )
    
    print("\n✅ Bedrock Classification Results:")
    print(f"   Document Type: {classification_result['documents'][0]['doc_type']}")
    print(f"   Page Number: {classification_result['documents'][0]['page_no']}")
    
    print("\n🎯 Test Results:")
    print("   ✓ Bedrock function executed successfully")
    print("   ✓ Document type correctly identified as 'invoice'")
    print("   ✓ Response structure is valid")
    
    return classification_result, bedrock_response


def print_test_summary():
    """Print test summary and next steps."""
    print("\n" + "=" * 60)
    print("🎉 DEMO COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\n📋 What was tested:")
    print("   • Main classification pipeline (main function)")
    print("   • Bedrock classification function")
    print("   • Mock AWS service integration")
    print("   • Response structure validation")
    print("   • Document type identification")
    
    print("\n🚀 Next Steps for Production:")
    print("   1. Run integration tests with real AWS services")
    print("   2. Test with actual document files from test data")
    print("   3. Validate performance under load")
    print("   4. Run full test suite: python tests/run_classification_tests.py --mode full")
    
    print("\n📁 Test Files Available:")
    print("   • Unit Tests: tests/test_classification.py")
    print("   • Test Runner: tests/run_classification_tests.py")
    print("   • Test Data: tests/input_data_extraction/")
    print("   • Expected Outputs: tests/output_data_extraction/")
    
    print("\n💡 Usage Examples:")
    print("   # Run unit tests only")
    print("   python tests/run_classification_tests.py --mode unit")
    print("   ")
    print("   # Run validation tests")
    print("   python tests/run_classification_tests.py --mode validation")
    print("   ")
    print("   # Run with coverage")
    print("   python tests/run_classification_tests.py --coverage")


async def main_demo():
    """Run the complete demo."""
    try:
        # Demo 1: Full classification pipeline
        result1 = await demo_classification_with_mocks()
        
        # Demo 2: Direct Bedrock function
        result2 = await demo_bedrock_function()
        
        # Print summary
        print_test_summary()
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 Classification Module Demo Test")
    print("This demo shows how the classification module works with sample data.")
    print("All AWS services are mocked for demonstration purposes.\n")
    
    # Run the demo
    success = asyncio.run(main_demo())
    
    if success:
        print("\n✅ Demo completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Demo failed!")
        sys.exit(1)
