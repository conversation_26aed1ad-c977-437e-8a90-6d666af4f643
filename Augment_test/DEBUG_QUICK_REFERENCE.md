# Debug Quick Reference Card

## 🚀 Quick Start
1. Open `app/llm/classification.py`
2. Set breakpoints (click left margin)
3. Press `Ctrl+Shift+D` → Select "Debug Classification.py" → Press F5

## 🎯 Debug Configurations

| Configuration | Purpose | When to Use |
|---------------|---------|-------------|
| **Debug Classification.py** | Standard debugging | Most common debugging scenarios |
| **Debug Classification.py (Detailed)** | Deep debugging with libraries | When you need to debug into AWS/boto3 code |
| **Debug Current Python File** | Debug any open file | Testing individual modules |
| **Debug with Arguments** | Custom arguments | When you need specific parameters |
| **Attach to Process** | Remote debugging | Debugging running processes |

## ⌨️ Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `F5` | Start/Continue debugging |
| `F10` | Step over (next line) |
| `F11` | Step into function |
| `Shift+F11` | Step out of function |
| `Ctrl+Shift+F5` | Restart debugging |
| `Shift+F5` | Stop debugging |
| `F9` | Toggle breakpoint |

## 📍 Best Breakpoint Locations

### In classification.py:
- **Line 149**: After processor initialization
- **Line 154**: Before Textract processing  
- **Line 160**: Before Bedrock analysis
- **Line 186**: Before returning results

### In debug_classification_demo.py:
- **Line 25**: Before processing starts
- **Line 32**: After getting sample file
- **Line 37**: After classification completes

## 🔧 Debug Panels

| Panel | Purpose |
|-------|---------|
| **Variables** | View current variable values |
| **Watch** | Monitor specific expressions |
| **Call Stack** | See function call hierarchy |
| **Debug Console** | Execute code during debugging |

## 💡 Pro Tips

1. **Conditional Breakpoints**: Right-click breakpoint → "Edit Breakpoint" → Add condition
2. **Log Points**: Right-click → "Add Logpoint" (prints without stopping)
3. **Debug Console**: Type expressions to evaluate them in current context
4. **Step Into Async**: Use F11 on `await` calls to debug async functions
5. **Exception Breakpoints**: Debug → "Add Exception Breakpoint"

## 🐛 Common Issues

| Problem | Solution |
|---------|----------|
| "Module not found" | Check PYTHONPATH in settings.json |
| "Python interpreter not found" | Verify venv path in settings.json |
| "Environment variables missing" | Ensure .env file exists |
| "Breakpoints not hit" | Check file path and ensure code is executed |

## 🏃‍♂️ Quick Commands

```bash
# Run without debugging
./venv/bin/python app/llm/classification.py

# Run debug demo
./venv/bin/python Augment_test/debug_classification_demo.py

# Run tests
./venv/bin/python -m pytest tests/test_classification.py -v
```

## 📋 Checklist Before Debugging

- [ ] Virtual environment activated
- [ ] Dependencies installed (`pip install -r requirements.txt`)
- [ ] `.env` file configured with AWS credentials
- [ ] Sample S3 file available in settings
- [ ] Breakpoints set at desired locations
- [ ] Correct debug configuration selected
