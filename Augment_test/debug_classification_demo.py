#!/usr/bin/env python3
"""
Debug Demo for Classification Module

This script demonstrates how to debug the classification.py module
with sample data and breakpoints for development and testing.
"""

import asyncio
import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.llm.classification import main, setup_logging
from app.core.configuration import settings


def debug_classification_demo():
    """
    Demo function to test classification with debugging capabilities.
    
    This function:
    1. Sets up logging for debugging
    2. Uses the sample S3 file from settings
    3. Calls the main classification function
    4. Prints formatted results
    
    Set breakpoints in this function or in classification.py to debug.
    """
    # Setup logging
    logger = setup_logging()
    logger.info("Starting classification debug demo")
    
    # Get sample file from settings
    sample_file = settings.S3_SAMPLE_FILE_CLASSIFICATION
    logger.info(f"Using sample file: {sample_file}")
    
    # Set a breakpoint here to inspect variables
    print(f"Debug: About to process file: {sample_file}")
    
    try:
        # Call the main classification function
        result = asyncio.run(main(sample_file))
        
        # Set another breakpoint here to inspect results
        print("Debug: Classification completed successfully")
        
        # Print formatted results
        print("\n" + "="*50)
        print("CLASSIFICATION RESULTS")
        print("="*50)
        print(json.dumps(result["classification_result"], indent=2))
        
        print("\n" + "="*50)
        print("EXECUTION INFO")
        print("="*50)
        print(json.dumps(result["execution_info"], indent=2))
        
        return result
        
    except Exception as e:
        logger.error(f"Classification failed: {e}")
        print(f"Error: {e}")
        raise


def test_with_custom_file(s3_uri: str):
    """
    Test classification with a custom S3 URI.
    
    Args:
        s3_uri: S3 URI to test with
    """
    logger = setup_logging()
    logger.info(f"Testing with custom file: {s3_uri}")
    
    try:
        result = asyncio.run(main(s3_uri))
        print(f"Success! Classification result: {result['classification_result']}")
        return result
    except Exception as e:
        logger.error(f"Failed to process {s3_uri}: {e}")
        raise


if __name__ == "__main__":
    # Main demo function
    debug_classification_demo()
    
    # Uncomment below to test with a custom file
    # custom_s3_uri = "s3://your-bucket/your-file.pdf"
    # test_with_custom_file(custom_s3_uri)
