# Debug Configuration Setup for Classification.py

This document explains how to use the VS Code debug configurations created for the document classification module.

## Files Created

1. **`.vscode/launch.json`** - Debug configurations
2. **`.vscode/settings.json`** - VS Code Python settings
3. **`.vscode/tasks.json`** - Quick tasks for running and testing
4. **`Augment_test/debug_classification_demo.py`** - Demo script for debugging

## Debug Configurations Available

### 1. Debug Classification.py
- **Purpose**: Debug the main classification.py file directly
- **Usage**: Select this configuration and press F5
- **Features**: 
  - Runs with the sample file from settings
  - Uses integrated terminal
  - Shows return values
  - Only debugs your code (justMyCode: true)

### 2. Debug Classification.py (Detailed)
- **Purpose**: Debug with more detailed information including library code
- **Features**:
  - Debugs all code including libraries (justMyCode: false)
  - Logs to file for detailed analysis
  - Redirects output for better debugging

### 3. Debug Current Python File
- **Purpose**: Debug whatever Python file is currently open
- **Usage**: Open any Python file and use this configuration
- **Features**: Generic configuration for any Python file

### 4. Debug with Arguments
- **Purpose**: Debug classification.py with custom command-line arguments
- **Usage**: Modify the args array in launch.json to add custom arguments

### 5. Attach to Process
- **Purpose**: Attach debugger to a running Python process
- **Usage**: For debugging running applications or remote debugging

## How to Use

### Quick Start
1. Open VS Code in the project root directory
2. Open `app/llm/classification.py`
3. Set breakpoints by clicking in the left margin (red dots will appear)
4. Press `Ctrl+Shift+D` (or `Cmd+Shift+D` on Mac) to open Debug view
5. Select "Debug Classification.py" from the dropdown
6. Press F5 or click the green play button

### Setting Breakpoints
- Click in the left margin next to line numbers to set breakpoints
- Red dots indicate active breakpoints
- Breakpoints will pause execution at that line

### Useful Breakpoint Locations in classification.py
- Line 149: After processor initialization
- Line 154: Before Textract processing
- Line 160: Before Bedrock analysis
- Line 162: After getting results
- Line 186: Before returning final result

### Debug Demo Script
Use `Augment_test/debug_classification_demo.py` for guided debugging:
1. Open the demo script
2. Set breakpoints in the demo function
3. Use "Debug Current Python File" configuration
4. Step through the code to understand the flow

## Debug Controls
- **F5**: Continue/Start debugging
- **F10**: Step over (next line)
- **F11**: Step into (enter function)
- **Shift+F11**: Step out (exit function)
- **Ctrl+Shift+F5**: Restart debugging
- **Shift+F5**: Stop debugging

## Debug Views
- **Variables**: Shows current variable values
- **Watch**: Monitor specific expressions
- **Call Stack**: Shows function call hierarchy
- **Breakpoints**: Manage all breakpoints

## Environment Setup
The configurations automatically:
- Use the virtual environment (`./venv/bin/python`)
- Set PYTHONPATH to project root
- Load environment variables from `.env` file
- Use integrated terminal for output

## Troubleshooting

### Common Issues
1. **Python interpreter not found**: Check that `./venv/bin/python` exists
2. **Module import errors**: Ensure PYTHONPATH is set correctly
3. **Environment variables not loaded**: Check `.env` file exists and is properly formatted

### Debug Console
Use the Debug Console (bottom panel) to:
- Evaluate expressions during debugging
- Check variable values
- Execute Python code in the current context

## Tasks Available
Use `Ctrl+Shift+P` and type "Tasks: Run Task" to access:
- **Run Classification.py**: Execute without debugging
- **Run Classification Tests**: Run pytest on classification tests
- **Install Dependencies**: Install requirements.txt
- **Activate Virtual Environment**: Activate the venv

## Tips for Effective Debugging
1. Start with breakpoints at key function entry points
2. Use the Variables panel to inspect object states
3. Use the Watch panel to monitor specific values
4. Check the Call Stack to understand execution flow
5. Use the Debug Console to test expressions
6. Step through async functions carefully (F11 to step into await calls)

## Advanced Debugging
- **Conditional Breakpoints**: Right-click breakpoint → Add condition
- **Log Points**: Right-click → Add logpoint (prints without stopping)
- **Exception Breakpoints**: Break when exceptions are raised
- **Remote Debugging**: Use "Attach to Process" for remote debugging
