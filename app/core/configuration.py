"""
Module for managing FastAPI application settings and configuration.
"""
import os
import logging
from typing import List
from pathlib import Path
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
parent_dir = Path(__file__).resolve().parent.parent.parent
dotenv_path = parent_dir / ".env"
load_dotenv(dotenv_path)

__all__ = ["settings"]


class Settings(BaseSettings):
    """
    Represents a configuration class for loading environment variables.
    This class loads environment variables.
    """

    # project configuration
    PROJECT_NAME: str = os.environ.get("PROJECT_NAME", "document-data-extraction")
    HOST: str = os.environ.get("HOST", "0.0.0.0")
    PORT: int = os.environ.get("PORT", 8000)
    ENVIRONMENT: str = os.environ.get("ENVIRONMENT", "local")
    API_V1_STR: str = "/v1"
    # SECRET_KEY: str = os.environ.get("SECRET_KEY")
    BACKEND_CORS_ORIGINS: List[str] = os.environ.get("BACKEND_CORS_ORIGINS", ["*"])
    SHOW_SWAGGER_DOC: bool = os.environ.get("SHOW_SWAGGER_DOC", True)
    # ACCESS_TOKEN_EXPIRE_MINUTES: int = os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
    # REFRESH_TOKEN_EXPIRE_MINUTES:int = os.environ.get("REFRESH_TOKEN_EXPIRE_MINUTES",900)

    # # opensearch resources
    OPENSEARCH_URL: str = os.environ["OPENSEARCH_URL"]
    OPENSEARCH_USERNAME: str = os.environ["OPENSEARCH_USERNAME"]
    OPENSEARCH_PASSWORD: str = os.environ["OPENSEARCH_PASSWORD"]
    OPENSEARCH_VECTOR_INDEX: str = os.environ["OPENSEARCH_VECTOR_INDEX"]

    # langchain resources
    LANGCHAIN_TRACING_V2: bool = os.environ.get("LANGCHAIN_TRACING_V2", True)
    LANGCHAIN_ENDPOINT: str = os.environ.get("LANGCHAIN_ENDPOINT")
    LANGCHAIN_API_KEY: str = os.environ.get("LANGCHAIN_API_KEY")
    LANGCHAIN_PROJECT: str = os.environ.get("LANGCHAIN_PROJECT")

    # openai resources
    # OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY")

    # # azure openai resources
    # AZURE_OPENAI_DEPLOYMENT_ENDPOINT: str = os.environ.get(
    #     "AZURE_OPENAI_DEPLOYMENT_ENDPOINT"
    # )
    # AZURE_OPENAI_DEPLOYMENT_VERSION: str = os.environ.get(
    #     "AZURE_OPENAI_DEPLOYMENT_VERSION"
    # )
    # AZURE_OPENAI_API_KEY: str = os.environ.get("AZURE_OPENAI_API_KEY")
    # AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME"
    # )
    # AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME"
    # )
    # AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME: str = os.environ.get(
    #     "AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"
    # )
    # loading langfuse keys from env
    LANGFUSE_PUBLIC_KEY: str = os.environ.get("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_SECRET_KEY: str = os.environ.get("LANGFUSE_SECRET_KEY")
    LANGFUSE_HOST: str = os.environ.get("LANGFUSE_HOST")
    LANGFUSE_ENABLED: bool = os.environ.get("LANGFUSE_ENABLED", False)
    LANGFUSE_FLUSH_AT: int = os.environ.get("LANGFUSE_FLUSH_AT", 1)
    LANGFUSE_FLUSH_INTERVAL: int = os.environ.get("LANGFUSE_FLUSH_INTERVAL", 1000)
    LANGFUSE_PROMPT_LABEL: str = os.environ.get("LANGFUSE_PROMPT_LABEL", "development")
    LANGFUSE_SHUTDOWN_TIMEOUT: int = os.environ.get("LANGFUSE_SHUTDOWN_TIMEOUT", 3.0)

    # AWS resources
    AWS_ACCESS_KEY_ID: str = os.environ.get("AWS_ACCESS_KEY_ID","")
    AWS_SECRET_ACCESS_KEY: str = os.environ.get("AWS_SECRET_ACCESS_KEY","")
    AWS_SESSION_TOKEN: str = os.environ.get("AWS_SESSION_TOKEN","")

    AWS_REGION: str = os.environ.get("AWS_REGION", "us-east-1")
    AWS_DEFAULT_REGION:str =os.environ.get("AWS_DEFAULT_REGION", "us-east-1")
    SQS_EXTRACTION_QUEUE_URL: str = os.environ.get("SQS_EXTRACTION_QUEUE_URL")
    SQS_CLASSIFICATION_QUEUE_URL: str = os.environ.get("SQS_CLASSIFICATION_QUEUE_URL")
    S3_BUCKET_NAME: str = os.environ.get("S3_BUCKET_NAME")
    EXTRACTION_FOLDER_INPUT_PREFIX: str = os.environ.get("EXTRACTION_FOLDER_INPUT_PREFIX", "")
    EXTRACTION_FOLDER_OUTPUT_PREFIX: str = os.environ.get("EXTRACTION_FOLDER_OUTPUT_PREFIX", "")
    S3_FOLDER_PREFIX: str = os.environ.get("S3_FOLDER_PREFIX", "")

    # Bedrock Configuration for Extraction
    AWS_REGION_EXTRACTION: str = os.environ.get("AWS_REGION_EXTRACTION", "us-east-1")
    MODEL_ID_EXTRACTION: str = os.environ.get("MODEL_ID_EXTRACTION", "amazon.nova-pro-v1:0")
    TEMPERATURE_EXTRACTION: float = float(os.environ.get("TEMPERATURE_EXTRACTION", "0.2"))
    TOP_P_EXTRACTION: float = float(os.environ.get("TOP_P_EXTRACTION", "0.5"))
    MAX_TOKENS_EXTRACTION: int = int(os.environ.get("MAX_TOKENS_EXTRACTION", "500"))
    S3_SAMPLE_FILE_EXTRACTION: str = os.environ.get("S3_SAMPLE_FILE_EXTRACTION", "s3://document-extraction-logistically/sample/sample_file_extraction.pdf")

    # Bedrock Configuration for Classification
    AWS_REGION_CLASSIFICATION: str = os.environ.get("AWS_REGION_CLASSIFICATION", "us-west-2")
    MODEL_ID_CLASSIFICATION: str = os.environ.get("MODEL_ID_CLASSIFICATION", "openai.gpt-oss-20b-1:0")
    TEMPERATURE_CLASSIFICATION: float = float(os.environ.get("TEMPERATURE_CLASSIFICATION", "0.7"))
    MAX_TOKENS_CLASSIFICATION: int = int(os.environ.get("MAX_TOKENS_CLASSIFICATION", "5000"))
    REASONING_EFFORT_CLASSIFICATION: str = os.environ.get("REASONING_EFFORT_CLASSIFICATION", "medium")
    S3_SAMPLE_FILE_CLASSIFICATION: str = os.environ.get("S3_SAMPLE_FILE_CLASSIFICATION", "s3://document-extraction-logistically/sample/sample_file_classification.pdf")

    # Textract Configuration
    TEXTRACT_MAX_WORKERS: int = int(os.environ.get("TEXTRACT_MAX_WORKERS", "6"))
    TEXTRACT_POLL_INTERVAL: int = int(os.environ.get("TEXTRACT_POLL_INTERVAL", "5"))
    TEXTRACT_MAX_WAIT_TIME: int = int(os.environ.get("TEXTRACT_MAX_WAIT_TIME", "300"))
    TEXTRACT_MAX_FILE_SIZE_MB: int = int(os.environ.get("TEXTRACT_MAX_FILE_SIZE_MB", "10"))
    TEXTRACT_TEMP_PREFIX: str = os.environ.get("TEXTRACT_TEMP_PREFIX", "temp-document-processing")
    TEXTRACT_MEMORY_LIMIT_MB: int = int(os.environ.get("TEXTRACT_MEMORY_LIMIT_MB", "512"))
    TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS: int = int(os.environ.get("TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS", "3"))
    TEXTRACT_S3_CLEANUP_RETRY_DELAY: int = int(os.environ.get("TEXTRACT_S3_CLEANUP_RETRY_DELAY", "3"))

    # AWS_CLOUDWATCH_LOG_GROUP: str = os.environ.get(
    #     "AWS_CLOUDWATCH_LOG_GROUP", "conversational-ai-api"
    # )
    # AWS_CLOUDWATCH_LOG_STREAM: str = os.environ.get(
    #     "AWS_CLOUDWATCH_LOG_STREAM", "conversational-ai-api-ls"
    # )

    # # Redis (Optional)
    # REDIS_URL: str = os.environ.get("REDIS_URL")
    # REDIS_PORT: str = os.environ.get("REDIS_PORT")
    # REDIS_TTL: int = os.environ.get("REDIS_TTL", 60 * 60 * 24 * 7)

    class Config:
        case_sensitive = True


settings: Settings = Settings()


def validate_configuration() -> None:
    """
    Validate critical configuration values at startup.
    Raises ValueError if required configuration is missing or invalid.
    """
    logger = logging.getLogger(__name__)

    # Critical AWS configuration validation
    if not settings.AWS_ACCESS_KEY_ID and not settings.AWS_SECRET_ACCESS_KEY:
        logger.warning("AWS credentials not provided - using default credential chain")

    # Validate required S3 configuration
    if not settings.S3_BUCKET_NAME:
        raise ValueError("S3_BUCKET_NAME is required but not configured")

    if settings.TEXTRACT_POLL_INTERVAL < 1:
        raise ValueError(f"TEXTRACT_POLL_INTERVAL must be >= 1, got {settings.TEXTRACT_POLL_INTERVAL}")

    if settings.TEXTRACT_MAX_WAIT_TIME < 1:
        raise ValueError(f"TEXTRACT_MAX_WAIT_TIME must be >= 1 seconds, got {settings.TEXTRACT_MAX_WAIT_TIME}")

    if settings.TEXTRACT_MAX_FILE_SIZE_MB < 1 or settings.TEXTRACT_MAX_FILE_SIZE_MB > 500:
        raise ValueError(f"TEXTRACT_MAX_FILE_SIZE_MB must be between 1 and 500, got {settings.TEXTRACT_MAX_FILE_SIZE_MB}")

    if settings.TEXTRACT_MEMORY_LIMIT_MB < 128:
        raise ValueError(f"TEXTRACT_MEMORY_LIMIT_MB must be >= 128, got {settings.TEXTRACT_MEMORY_LIMIT_MB}")

    if settings.TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS < 1 or settings.TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS > 10:
        raise ValueError(f"TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS must be between 1 and 10, got {settings.TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS}")

    # Validate Bedrock Extraction configuration
    if settings.TEMPERATURE_EXTRACTION < 0 or settings.TEMPERATURE_EXTRACTION > 2:
        raise ValueError(f"TEMPERATURE_EXTRACTION must be between 0 and 2 for Nova/OpenAI Models, got {settings.TEMPERATURE_EXTRACTION}")

    if settings.TOP_P_EXTRACTION < 0 or settings.TOP_P_EXTRACTION > 1:
        raise ValueError(f"TOP_P_EXTRACTION must be between 0 and 1, got {settings.TOP_P_EXTRACTION}")

    if settings.MAX_TOKENS_EXTRACTION < 1:
        raise ValueError(f"MAX_TOKENS_EXTRACTION must be > 0, got {settings.MAX_TOKENS_EXTRACTION}")

    # Validate Bedrock Classification configuration
    if settings.TEMPERATURE_CLASSIFICATION < 0 or settings.TEMPERATURE_CLASSIFICATION > 2:
        raise ValueError(f"TEMPERATURE_CLASSIFICATION must be between 0 and 2 for Nova/OpenAI Models, got {settings.TEMPERATURE_CLASSIFICATION}")

    if settings.MAX_TOKENS_CLASSIFICATION < 1:
        raise ValueError(f"MAX_TOKENS_CLASSIFICATION must be > 0, got {settings.MAX_TOKENS_CLASSIFICATION}")

    # Validate reasoning effort for classification
    valid_reasoning_efforts = ["low", "medium", "high"]
    if settings.REASONING_EFFORT_CLASSIFICATION.lower() not in valid_reasoning_efforts:
        raise ValueError(f"REASONING_EFFORT_CLASSIFICATION must be one of {valid_reasoning_efforts}, got {settings.REASONING_EFFORT_CLASSIFICATION}")

    # Validate AWS regions
    if not settings.AWS_REGION_EXTRACTION:
        raise ValueError("AWS_REGION_EXTRACTION is required but not configured")

    if not settings.AWS_REGION_CLASSIFICATION:
        raise ValueError("AWS_REGION_CLASSIFICATION is required but not configured")

    # Validate model IDs
    if not settings.MODEL_ID_EXTRACTION:
        raise ValueError("MODEL_ID_EXTRACTION is required but not configured")

    if not settings.MODEL_ID_CLASSIFICATION:
        raise ValueError("MODEL_ID_CLASSIFICATION is required but not configured")

    # Validate S3 sample files format
    if settings.S3_SAMPLE_FILE_EXTRACTION and not settings.S3_SAMPLE_FILE_EXTRACTION.startswith('s3://'):
        raise ValueError(f"S3_SAMPLE_FILE_EXTRACTION must be a valid S3 URI, got {settings.S3_SAMPLE_FILE_EXTRACTION}")

    if settings.S3_SAMPLE_FILE_CLASSIFICATION and not settings.S3_SAMPLE_FILE_CLASSIFICATION.startswith('s3://'):
        raise ValueError(f"S3_SAMPLE_FILE_CLASSIFICATION must be a valid S3 URI, got {settings.S3_SAMPLE_FILE_CLASSIFICATION}")

    logger.info("Configuration validation completed successfully")


# Validate configuration at startup
try:
    validate_configuration()
except Exception as e:
    logging.error(f"Configuration validation failed: {e}")
    raise
