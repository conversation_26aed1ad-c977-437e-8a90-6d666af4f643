# Classification Module Test Suite

This directory contains comprehensive tests for the `app.llm.classification` module, designed to ensure production readiness through thorough testing of all functionality.

## Test Structure

### Test Categories

1. **Unit Tests** (`TestClassificationModule`)
   - Individual function testing
   - Input validation
   - Error handling
   - Mocked dependencies

2. **Integration Tests** (`TestClassificationIntegration`)
   - End-to-end workflow testing
   - Real data processing
   - AWS service integration
   - Output format validation

3. **Performance Tests** (`TestClassificationPerformance`)
   - Response time validation
   - Concurrent request handling
   - Resource usage monitoring
   - Reliability testing

4. **Edge Case Tests** (`TestClassificationEdgeCases`)
   - Empty documents
   - Malformed responses
   - Multi-page documents
   - Concurrent processing

5. **Data Validation Tests** (`TestClassificationDataValidation`)
   - Output format consistency
   - Expected vs actual comparison
   - Document type distribution analysis

6. **Configuration Tests** (`TestClassificationConfiguration`)
   - Settings validation
   - Required configuration checks
   - Value range validation

## Running Tests

### Prerequisites

1. Install test dependencies:
   ```bash
   pip install -r tests/requirements-test.txt
   ```

2. Set up environment variables (for integration tests):
   ```bash
   export AWS_ACCESS_KEY_ID=your_access_key
   export AWS_SECRET_ACCESS_KEY=your_secret_key
   export AWS_REGION_CLASSIFICATION=us-east-1
   export RUN_INTEGRATION_TESTS=1
   ```

### Test Execution Options

#### 1. Using the Test Runner Script

```bash
# Run unit tests only (fast, no AWS required)
python tests/run_classification_tests.py --mode unit

# Run integration tests (requires AWS credentials)
python tests/run_classification_tests.py --mode integration

# Run performance tests
python tests/run_classification_tests.py --mode performance

# Run validation tests
python tests/run_classification_tests.py --mode validation

# Run full test suite
python tests/run_classification_tests.py --mode full

# Run specific test
python tests/run_classification_tests.py --test test_main_function_success

# Generate coverage report
python tests/run_classification_tests.py --coverage

# List all available tests
python tests/run_classification_tests.py --list

# Dry run (check test discovery)
python tests/run_classification_tests.py --dry-run
```

#### 2. Using pytest directly

```bash
# Run all tests
pytest tests/test_classification.py -v

# Run specific test categories
pytest tests/test_classification.py -m unit -v
pytest tests/test_classification.py -m integration -v
pytest tests/test_classification.py -m performance -v

# Run with coverage
pytest tests/test_classification.py --cov=app.llm.classification --cov-report=html

# Run specific test class
pytest tests/test_classification.py::TestClassificationModule -v

# Run specific test function
pytest tests/test_classification.py::TestClassificationModule::test_main_function_success -v
```

## Test Data

### Input Data
- Located in: `tests/input_data_extraction/`
- Contains sample documents of various types (PDF, JPG, PNG, TIFF)
- Organized by document type (bol, invoice, pod, etc.)

### Expected Output Data
- Located in: `tests/output_data_extraction/`
- Contains expected classification results in JSON format
- Used for validation and regression testing

### Test File Structure
```
tests/input_data_extraction/
├── 1_bol/
│   ├── 1_bol_1.pdf
│   ├── 1_bol_10.pdf
│   └── ...
├── 10_invoice/
│   ├── 10_invoice_1.pdf
│   ├── 10_invoice_2.pdf
│   └── ...
└── ...
```

## Test Markers

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.performance` - Performance tests
- `@pytest.mark.reliability` - Reliability tests
- `@pytest.mark.validation` - Data validation tests
- `@pytest.mark.slow` - Tests that take longer to run
- `@pytest.mark.aws` - Tests requiring AWS services

## Mocking Strategy

### AWS Services
- **Textract**: Mocked using `unittest.mock.Mock`
- **Bedrock**: Mocked with predefined responses
- **S3**: File operations mocked for unit tests

### External Dependencies
- Network calls are mocked to ensure test isolation
- File system operations use temporary directories
- Configuration settings can be overridden for testing

## Test Coverage Goals

- **Function Coverage**: 100% of public functions
- **Branch Coverage**: >90% of code branches
- **Error Path Coverage**: All error scenarios tested
- **Integration Coverage**: All major workflows tested

## Continuous Integration

### Pre-commit Checks
```bash
# Run fast tests before committing
python tests/run_classification_tests.py --mode unit
```

### CI Pipeline Tests
```bash
# Full test suite for CI/CD
python tests/run_classification_tests.py --mode full --coverage
```

## Troubleshooting

### Common Issues

1. **AWS Credentials Not Found**
   - Ensure AWS credentials are properly configured
   - For local testing, set `RUN_INTEGRATION_TESTS=1` to use mocked services

2. **Test Data Missing**
   - Verify test data exists in `tests/input_data_extraction/`
   - Check file permissions and accessibility

3. **Import Errors**
   - Ensure the project root is in Python path
   - Install all required dependencies

4. **Timeout Issues**
   - Increase timeout values in `pytest.ini`
   - Check network connectivity for integration tests

### Debug Mode
```bash
# Run tests with detailed output
pytest tests/test_classification.py -v -s --tb=long

# Run with PDB on failure
pytest tests/test_classification.py --pdb

# Run single test with maximum verbosity
pytest tests/test_classification.py::test_function_name -vvv -s
```

## Performance Benchmarks

### Expected Performance Metrics
- **Single Document Classification**: < 30 seconds
- **Multi-page Document**: < 60 seconds
- **Concurrent Requests**: 3+ simultaneous without degradation
- **Memory Usage**: < 512MB per request

### Performance Test Results
Run performance tests to get current metrics:
```bash
python tests/run_classification_tests.py --mode performance
```

## Contributing

### Adding New Tests

1. Follow the existing test structure and naming conventions
2. Add appropriate pytest markers
3. Include docstrings explaining test purpose
4. Mock external dependencies appropriately
5. Update this documentation if adding new test categories

### Test Data Management

1. Add new test files to appropriate subdirectories
2. Update expected output files when functionality changes
3. Ensure test data doesn't contain sensitive information
4. Keep test files reasonably sized (< 10MB each)

## Production Readiness Checklist

Before deploying to production, ensure all tests pass:

- [ ] All unit tests pass
- [ ] Integration tests pass with real AWS services
- [ ] Performance tests meet requirements
- [ ] Error handling tests pass
- [ ] Configuration validation passes
- [ ] Data validation tests pass
- [ ] Coverage meets minimum thresholds

Run the complete validation:
```bash
python tests/run_classification_tests.py --mode full
```
