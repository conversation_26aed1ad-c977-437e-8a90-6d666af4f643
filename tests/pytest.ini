[tool:pytest]
# Pytest configuration for classification tests

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers for different test categories
markers =
    unit: Unit tests for individual functions
    integration: Integration tests with external services
    performance: Performance and timing tests
    reliability: Reliability and consistency tests
    validation: Data validation and comparison tests
    slow: Tests that take a long time to run
    aws: Tests that require AWS credentials and services

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Coverage options (if pytest-cov is installed)
# addopts = --cov=app.llm.classification --cov-report=html --cov-report=term-missing

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:boto3.*
    ignore::UserWarning:botocore.*
