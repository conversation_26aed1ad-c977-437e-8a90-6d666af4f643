#!/usr/bin/env python3
"""
Test runner script for classification module tests.

This script provides different test execution modes:
1. Unit tests only (fast, no external dependencies)
2. Integration tests (requires AWS credentials)
3. Performance tests (timing and load testing)
4. Validation tests (compare with expected outputs)
5. Full test suite (all tests)

Usage:
    python tests/run_classification_tests.py --mode unit
    python tests/run_classification_tests.py --mode integration
    python tests/run_classification_tests.py --mode performance
    python tests/run_classification_tests.py --mode validation
    python tests/run_classification_tests.py --mode full
"""

import argparse
import os
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any


class ClassificationTestRunner:
    """Test runner for classification module."""

    def __init__(self):
        self.test_file = "tests/test_classification.py"
        self.base_dir = Path(__file__).parent.parent
        
    def run_unit_tests(self) -> int:
        """Run unit tests only."""
        print("🧪 Running unit tests...")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "-m", "not integration and not performance and not validation",
            "-v"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def run_integration_tests(self) -> int:
        """Run integration tests."""
        print("🔗 Running integration tests...")
        
        # Check for AWS credentials
        if not self._check_aws_credentials():
            print("❌ AWS credentials not found. Set RUN_INTEGRATION_TESTS=1 to run with mocked services.")
            return 1
        
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "-m", "integration",
            "-v"
        ]
        
        env = os.environ.copy()
        env["RUN_INTEGRATION_TESTS"] = "1"
        
        return subprocess.run(cmd, cwd=self.base_dir, env=env).returncode

    def run_performance_tests(self) -> int:
        """Run performance tests."""
        print("⚡ Running performance tests...")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "-m", "performance",
            "-v"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def run_validation_tests(self) -> int:
        """Run validation tests."""
        print("✅ Running validation tests...")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "-m", "validation",
            "-v"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def run_full_test_suite(self) -> int:
        """Run all tests."""
        print("🚀 Running full test suite...")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "-v",
            "--tb=short"
        ]
        
        env = os.environ.copy()
        env["RUN_INTEGRATION_TESTS"] = "1"
        
        return subprocess.run(cmd, cwd=self.base_dir, env=env).returncode

    def run_specific_test(self, test_name: str) -> int:
        """Run a specific test function."""
        print(f"🎯 Running specific test: {test_name}")
        cmd = [
            "python", "-m", "pytest", 
            f"{self.test_file}::{test_name}",
            "-v"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def run_coverage_report(self) -> int:
        """Run tests with coverage report."""
        print("📊 Running tests with coverage report...")
        
        # Check if pytest-cov is installed
        try:
            import pytest_cov
        except ImportError:
            print("❌ pytest-cov not installed. Install with: pip install pytest-cov")
            return 1
        
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "--cov=app.llm.classification",
            "--cov-report=html",
            "--cov-report=term-missing",
            "-v"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def _check_aws_credentials(self) -> bool:
        """Check if AWS credentials are available."""
        # Check environment variables
        if os.getenv("AWS_ACCESS_KEY_ID") and os.getenv("AWS_SECRET_ACCESS_KEY"):
            return True
        
        # Check AWS credentials file
        aws_creds_file = Path.home() / ".aws" / "credentials"
        if aws_creds_file.exists():
            return True
        
        # Check if running in AWS environment (EC2, Lambda, etc.)
        if os.getenv("AWS_EXECUTION_ENV") or os.getenv("AWS_LAMBDA_FUNCTION_NAME"):
            return True
        
        return False

    def list_available_tests(self) -> int:
        """List all available tests."""
        print("📋 Available tests:")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "--collect-only",
            "-q"
        ]
        return subprocess.run(cmd, cwd=self.base_dir).returncode

    def run_dry_run(self) -> int:
        """Perform a dry run to check test discovery."""
        print("🔍 Performing dry run...")
        cmd = [
            "python", "-m", "pytest", 
            self.test_file,
            "--collect-only",
            "--quiet"
        ]
        result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests discovered successfully")
            print(f"Output: {result.stdout}")
        else:
            print("❌ Test discovery failed")
            print(f"Error: {result.stderr}")
        
        return result.returncode


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for classification module",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/run_classification_tests.py --mode unit
  python tests/run_classification_tests.py --mode integration
  python tests/run_classification_tests.py --test test_main_function_success
  python tests/run_classification_tests.py --coverage
  python tests/run_classification_tests.py --list
        """
    )
    
    parser.add_argument(
        "--mode", 
        choices=["unit", "integration", "performance", "validation", "full"],
        help="Test mode to run"
    )
    
    parser.add_argument(
        "--test",
        help="Run a specific test function"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run tests with coverage report"
    )
    
    parser.add_argument(
        "--list",
        action="store_true",
        help="List all available tests"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run to check test discovery"
    )
    
    args = parser.parse_args()
    
    runner = ClassificationTestRunner()
    
    # Validate that test file exists
    if not Path(runner.test_file).exists():
        print(f"❌ Test file not found: {runner.test_file}")
        return 1
    
    try:
        if args.dry_run:
            return runner.run_dry_run()
        elif args.list:
            return runner.list_available_tests()
        elif args.coverage:
            return runner.run_coverage_report()
        elif args.test:
            return runner.run_specific_test(args.test)
        elif args.mode == "unit":
            return runner.run_unit_tests()
        elif args.mode == "integration":
            return runner.run_integration_tests()
        elif args.mode == "performance":
            return runner.run_performance_tests()
        elif args.mode == "validation":
            return runner.run_validation_tests()
        elif args.mode == "full":
            return runner.run_full_test_suite()
        else:
            print("❌ Please specify a test mode or action")
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
