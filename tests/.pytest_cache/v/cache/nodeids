["test_classification.py::TestClassificationConfiguration::test_required_settings_exist", "test_classification.py::TestClassificationConfiguration::test_settings_values_valid", "test_classification.py::TestClassificationEdgeCases::test_concurrent_classification_requests", "test_classification.py::TestClassificationEdgeCases::test_document_type_enum_coverage", "test_classification.py::TestClassificationEdgeCases::test_empty_document_content", "test_classification.py::TestClassificationEdgeCases::test_malformed_bedrock_response", "test_classification.py::TestClassificationEdgeCases::test_multi_page_document", "test_classification.py::TestClassificationIntegration::test_expected_output_structure", "test_classification.py::TestClassificationModule::test_get_document_types_with_bedrock_failure", "test_classification.py::TestClassificationModule::test_get_document_types_with_bedrock_success", "test_classification.py::TestClassificationModule::test_main_function_bedrock_failure", "test_classification.py::TestClassificationModule::test_main_function_invalid_input", "test_classification.py::TestClassificationModule::test_main_function_invalid_s3_uri", "test_classification.py::TestClassificationModule::test_main_function_success", "test_classification.py::TestClassificationModule::test_main_function_textract_failure", "test_classification.py::TestClassificationModule::test_setup_logging", "test_classification.py::TestClassificationPerformance::test_classification_reliability"]