# Testing requirements for classification module
# Install with: pip install -r tests/requirements-test.txt

# Core testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-timeout>=2.1.0

# Coverage reporting
pytest-cov>=4.0.0

# Performance testing
pytest-benchmark>=4.0.0

# Test data and fixtures
pytest-fixtures>=0.1.0

# Mocking and test utilities
responses>=0.23.0
freezegun>=1.2.0
factory-boy>=3.2.0

# AWS testing utilities
moto>=4.0.0  # For mocking AWS services
boto3-stubs>=1.26.0  # Type stubs for boto3

# Additional testing utilities
hypothesis>=6.0.0  # Property-based testing
faker>=18.0.0  # Generate fake test data
parameterized>=0.9.0  # Parameterized tests

# Test reporting and output
pytest-html>=3.1.0  # HTML test reports
pytest-json-report>=1.5.0  # JSON test reports
pytest-xdist>=3.0.0  # Parallel test execution

# Development and debugging
pytest-pdb>=0.2.0  # PDB integration
pytest-sugar>=0.9.0  # Better test output formatting
