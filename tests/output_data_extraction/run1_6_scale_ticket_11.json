{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.8867974877357483, "Height": 0.8775275945663452, "Left": 0.019867422059178352, "Top": 0.03701331093907356}, "Polygon": [{"X": 0.03830203041434288, "Y": 0.03701331093907356}, {"X": 0.8969070315361023, "Y": 0.08918356150388718}, {"X": 0.9066649079322815, "Y": 0.9126594066619873}, {"X": 0.019867422059178352, "Y": 0.9145408868789673}]}, "Id": "e9ceb03f-f1ac-4bef-9aea-0a99f0307ee4", "Relationships": [{"Type": "CHILD", "Ids": ["90d6fe8f-44ef-40cb-adcc-a43c6e6e9fcc", "beeedb76-7d9b-4a0d-822f-3ed735ff0959", "ea947c61-0cd0-4b59-960a-857f57b78fa3", "a957bff8-d825-41ba-a061-9f10b2e28865", "fb1c6219-73be-4100-900a-4b37a9a8eee2", "a5ca5b02-c37f-4da1-9b5c-ca129784e728", "1d4be142-5667-4934-867f-4c69e623f9a6", "f41db1d7-0635-46b6-85f1-47502e0f2aee", "6dab37af-d964-41a4-8cb9-25107867e1c8", "4e814bb2-8f1b-49f6-bc03-72c65f61f3c9", "ea95e27f-8eb3-4a6a-8a87-37762323d8f7", "d77d89c6-a520-40eb-9485-002aaea8d051", "113d9577-0f96-45c1-bb65-d1617f7c799b", "89bcc406-2b18-469d-aa47-a69cf8c42cd9", "15418c1d-cae0-4250-8c28-36f6a251f917", "8a578e9b-2787-40f1-a268-c4ce0b94c787", "5e29a96d-5465-44ee-b383-eca6d4d21de2", "b98901c9-0083-4f46-baab-7c7650578ba1", "a1bba808-1a54-45da-a967-4b0e9a1b3808", "f7884ad5-3de8-4fa7-89dd-31d278095400", "ec0e8a68-1a1d-4037-a698-39307fc6c045", "77ed6ce3-9039-46c4-af4b-e34e19735789", "bfa2a12a-b4b4-4a23-b76e-4c1dacec8c0e", "9191facb-a671-4dc3-9c82-f49401291f29", "6c40fb1c-af8f-4bdb-ac82-6bfacd09c673", "33209559-5136-4e2a-93f8-dea2bffe76ac", "f22e7a46-75f6-4cf3-bae0-b1ac5a468b3e", "218e8d8a-761f-4164-81bc-7961b10e2f79", "03fa2de1-7a7d-4a42-9ac0-3f4d6c11b852", "d42c584d-98a8-487b-adf3-2536a13387e4", "600954d7-99a5-48bb-bd07-f9f1d444e744", "8377792f-05c2-4c05-a840-0cc9f70f4a11"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "ND Paper", "Geometry": {"BoundingBox": {"Width": 0.07423651218414307, "Height": 0.015979807823896408, "Left": 0.43452948331832886, "Top": 0.10246113687753677}, "Polygon": [{"X": 0.434597909450531, "Y": 0.10246113687753677}, {"X": 0.5087659955024719, "Y": 0.10674141347408295}, {"X": 0.5087311267852783, "Y": 0.11844094842672348}, {"X": 0.43452948331832886, "Y": 0.11422313749790192}]}, "Id": "90d6fe8f-44ef-40cb-adcc-a43c6e6e9fcc", "Relationships": [{"Type": "CHILD", "Ids": ["246f81e3-e357-4be0-9f06-b8a2598aec09", "80dedf25-f500-4225-8f22-3a3faa08c95c"]}]}, {"BlockType": "LINE", "Confidence": 98.40740203857422, "Text": "ND", "Geometry": {"BoundingBox": {"Width": 0.02534719556570053, "Height": 0.012783447280526161, "Left": 0.6854881048202515, "Top": 0.11459143459796906}, "Polygon": [{"X": 0.6854881048202515, "Y": 0.11459143459796906}, {"X": 0.7107817530632019, "Y": 0.11605560779571533}, {"X": 0.710835337638855, "Y": 0.12737488746643066}, {"X": 0.6855308413505554, "Y": 0.12593165040016174}]}, "Id": "beeedb76-7d9b-4a0d-822f-3ed735ff0959", "Relationships": [{"Type": "CHILD", "Ids": ["8b90aa6f-ce7c-4215-9a1d-7e4d654d75d5"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "ND PAPER", "Geometry": {"BoundingBox": {"Width": 0.12345172464847565, "Height": 0.02765122428536415, "Left": 0.7288892865180969, "Top": 0.11300218105316162}, "Polygon": [{"X": 0.7288892865180969, "Y": 0.11300218105316162}, {"X": 0.8521336317062378, "Y": 0.12017452716827393}, {"X": 0.8523409962654114, "Y": 0.14065340161323547}, {"X": 0.7290013432502747, "Y": 0.13366761803627014}]}, "Id": "ea947c61-0cd0-4b59-960a-857f57b78fa3", "Relationships": [{"Type": "CHILD", "Ids": ["2e20dd8f-427b-4956-95e4-ecfd4ea9538a", "4f76a9b0-bb4b-4f2f-9df7-8debb0175dfb"]}]}, {"BlockType": "LINE", "Confidence": 99.56304168701172, "Text": "Biron Scale(Biron)", "Geometry": {"BoundingBox": {"Width": 0.14416295289993286, "Height": 0.022115180268883705, "Left": 0.39838242530822754, "Top": 0.11768478155136108}, "Polygon": [{"X": 0.3984840512275696, "Y": 0.11768478155136108}, {"X": 0.5425453782081604, "Y": 0.12581519782543182}, {"X": 0.5425217151641846, "Y": 0.13979995250701904}, {"X": 0.39838242530822754, "Y": 0.13181501626968384}]}, "Id": "a957bff8-d825-41ba-a061-9f10b2e28865", "Relationships": [{"Type": "CHILD", "Ids": ["ce06e09f-5fbd-49bb-9039-b312b8d6e238", "f9902f35-02e8-4c79-83a8-b73b1eecffe2"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "29210", "Geometry": {"BoundingBox": {"Width": 0.04831111058592796, "Height": 0.013186685740947723, "Left": 0.11325331777334213, "Top": 0.12163593620061874}, "Polygon": [{"X": 0.11344444006681442, "Y": 0.12163593620061874}, {"X": 0.1615644246339798, "Y": 0.12428221851587296}, {"X": 0.16139332950115204, "Y": 0.13482262194156647}, {"X": 0.11325331777334213, "Y": 0.13221195340156555}]}, "Id": "fb1c6219-73be-4100-900a-4b37a9a8eee2", "Relationships": [{"Type": "CHILD", "Ids": ["7a1d996a-1a19-4663-aec7-c9efb9a8ce09"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Source", "Geometry": {"BoundingBox": {"Width": 0.0461830236017704, "Height": 0.01109231635928154, "Left": 0.4676319658756256, "Top": 0.14745007455348969}, "Polygon": [{"X": 0.46767106652259827, "Y": 0.14745007455348969}, {"X": 0.5138149857521057, "Y": 0.14996609091758728}, {"X": 0.5137910842895508, "Y": 0.15854239463806152}, {"X": 0.4676319658756256, "Y": 0.15605494379997253}]}, "Id": "a5ca5b02-c37f-4da1-9b5c-ca129784e728", "Relationships": [{"Type": "CHILD", "Ids": ["34a5b615-e067-42ab-ad3b-858916fe97dd"]}]}, {"BlockType": "LINE", "Confidence": 60.105777740478516, "Text": "VIM_AURO_IL_RATHBONE(VIM RECYCLERSL P)", "Geometry": {"BoundingBox": {"Width": 0.314569354057312, "Height": 0.03880530223250389, "Left": 0.5538837313652039, "Top": 0.13808947801589966}, "Polygon": [{"X": 0.5539112091064453, "Y": 0.13808947801589966}, {"X": 0.8682242035865784, "Y": 0.15555636584758759}, {"X": 0.8684530854225159, "Y": 0.17689478397369385}, {"X": 0.5538837313652039, "Y": 0.15992482006549835}]}, "Id": "1d4be142-5667-4934-867f-4c69e623f9a6", "Relationships": [{"Type": "CHILD", "Ids": ["b87c1ded-6276-498a-8540-bcf0c470ea4d", "67b0ce82-b4d5-4378-b7a1-065b43379ee1"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Contract", "Geometry": {"BoundingBox": {"Width": 0.06306661665439606, "Height": 0.01468481682240963, "Left": 0.0724496990442276, "Top": 0.13722670078277588}, "Polygon": [{"X": 0.0726725235581398, "Y": 0.13722670078277588}, {"X": 0.13551631569862366, "Y": 0.1406022012233734}, {"X": 0.13532161712646484, "Y": 0.15191152691841125}, {"X": 0.0724496990442276, "Y": 0.14858588576316833}]}, "Id": "f41db1d7-0635-46b6-85f1-47502e0f2aee", "Relationships": [{"Type": "CHILD", "Ids": ["aab5f40e-ce7c-4e76-9995-43b0d6e5a824"]}]}, {"BlockType": "LINE", "Confidence": 99.78724670410156, "Text": "018539 (HARVESTER)", "Geometry": {"BoundingBox": {"Width": 0.1485840082168579, "Height": 0.021718839183449745, "Left": 0.1807393580675125, "Top": 0.14646676182746887}, "Polygon": [{"X": 0.18095500767230988, "Y": 0.14646676182746887}, {"X": 0.32932335138320923, "Y": 0.15439918637275696}, {"X": 0.3291879892349243, "Y": 0.16818559169769287}, {"X": 0.1807393580675125, "Y": 0.16039873659610748}]}, "Id": "6dab37af-d964-41a4-8cb9-25107867e1c8", "Relationships": [{"Type": "CHILD", "Ids": ["32849674-1dc7-48cc-9858-8f14052fafdf", "63cab99f-05d7-4f05-98a5-1d223f5d5c85"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Destination", "Geometry": {"BoundingBox": {"Width": 0.07361311465501785, "Height": 0.014335665851831436, "Left": 0.4676561653614044, "Top": 0.16074106097221375}, "Polygon": [{"X": 0.467703640460968, "Y": 0.16074106097221375}, {"X": 0.5412693023681641, "Y": 0.16467994451522827}, {"X": 0.5412511825561523, "Y": 0.17507672309875488}, {"X": 0.4676561653614044, "Y": 0.17119313776493073}]}, "Id": "4e814bb2-8f1b-49f6-bc03-72c65f61f3c9", "Relationships": [{"Type": "CHILD", "Ids": ["a7e46e96-89f9-4aaa-997b-9b2a44bdb832"]}]}, {"BlockType": "LINE", "Confidence": 96.3954849243164, "Text": "BIRON(BIRON MILL)", "Geometry": {"BoundingBox": {"Width": 0.12918375432491302, "Height": 0.02115754410624504, "Left": 0.553171694278717, "Top": 0.15978416800498962}, "Polygon": [{"X": 0.5531900525093079, "Y": 0.15978416800498962}, {"X": 0.6823038458824158, "Y": 0.16675041615962982}, {"X": 0.6823554635047913, "Y": 0.18094171583652496}, {"X": 0.553171694278717, "Y": 0.17410939931869507}]}, "Id": "ea95e27f-8eb3-4a6a-8a87-37762323d8f7", "Relationships": [{"Type": "CHILD", "Ids": ["480a8904-06cf-49df-882a-bbffd7ccb030", "9b38aaee-aeda-46cc-8897-66b052260fa3"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "Trailer AT26", "Geometry": {"BoundingBox": {"Width": 0.09377062320709229, "Height": 0.016530269756913185, "Left": 0.7396330833435059, "Top": 0.1934497356414795}, "Polygon": [{"X": 0.7396330833435059, "Y": 0.1934497356414795}, {"X": 0.8332945108413696, "Y": 0.19833634793758392}, {"X": 0.8334037065505981, "Y": 0.20998001098632812}, {"X": 0.7397012710571289, "Y": 0.20517413318157196}]}, "Id": "d77d89c6-a520-40eb-9485-002aaea8d051", "Relationships": [{"Type": "CHILD", "Ids": ["b628edfb-840a-4fba-8ff7-d57643aa6099", "2559f434-8fe0-4230-95a7-943f17ad737f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.03953065350651741, "Height": 0.01093638688325882, "Left": 0.4669767916202545, "Top": 0.19175946712493896}, "Polygon": [{"X": 0.4670175611972809, "Y": 0.19175946712493896}, {"X": 0.5065074563026428, "Y": 0.19378319382667542}, {"X": 0.5064801573753357, "Y": 0.20269586145877838}, {"X": 0.4669767916202545, "Y": 0.20069755613803864}]}, "Id": "113d9577-0f96-45c1-bb65-d1617f7c799b", "Relationships": [{"Type": "CHILD", "Ids": ["cb276d84-a759-4e1b-a9a0-2f8b1c9c07bc"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "ZACH", "Geometry": {"BoundingBox": {"Width": 0.037287645041942596, "Height": 0.011582777835428715, "Left": 0.5518341660499573, "Top": 0.19258533418178558}, "Polygon": [{"X": 0.5518471002578735, "Y": 0.19258533418178558}, {"X": 0.5891209840774536, "Y": 0.1945052593946457}, {"X": 0.5891218185424805, "Y": 0.20416811108589172}, {"X": 0.5518341660499573, "Y": 0.2022743672132492}]}, "Id": "89bcc406-2b18-469d-aa47-a69cf8c42cd9", "Relationships": [{"Type": "CHILD", "Ids": ["9d8e09df-e0cd-4a77-8ac5-7d5a1c95233e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BOL", "Geometry": {"BoundingBox": {"Width": 0.0318727008998394, "Height": 0.01108391024172306, "Left": 0.07185383141040802, "Top": 0.18583805859088898}, "Polygon": [{"X": 0.07204034179449081, "Y": 0.18583805859088898}, {"X": 0.10372653603553772, "Y": 0.1874292641878128}, {"X": 0.10355190932750702, "Y": 0.1969219595193863}, {"X": 0.07185383141040802, "Y": 0.19535183906555176}]}, "Id": "15418c1d-cae0-4250-8c28-36f6a251f917", "Relationships": [{"Type": "CHILD", "Ids": ["39baa2cc-b1cf-4711-841a-cb20e392f99b"]}]}, {"BlockType": "LINE", "Confidence": 99.91077423095703, "Text": "Trip Ticket BIR0029912", "Geometry": {"BoundingBox": {"Width": 0.17235781252384186, "Height": 0.020067404955625534, "Left": 0.07053522765636444, "Top": 0.20313788950443268}, "Polygon": [{"X": 0.07076658308506012, "Y": 0.20313788950443268}, {"X": 0.2428930401802063, "Y": 0.21156702935695648}, {"X": 0.24274075031280518, "Y": 0.2232052981853485}, {"X": 0.07053522765636444, "Y": 0.21491815149784088}]}, "Id": "8a578e9b-2787-40f1-a268-c4ce0b94c787", "Relationships": [{"Type": "CHILD", "Ids": ["6c2b9bc5-d8db-426c-9046-93a09579464b", "0a614390-cc73-4312-9f10-158a7a3171b1", "929a73d1-640b-49f5-a47b-c5ad07076cb3"]}]}, {"BlockType": "LINE", "Confidence": 99.6834487915039, "Text": "Product OCC 11D", "Geometry": {"BoundingBox": {"Width": 0.13654102385044098, "Height": 0.019607676193118095, "Left": 0.46371403336524963, "Top": 0.2204982042312622}, "Polygon": [{"X": 0.46377503871917725, "Y": 0.2204982042312622}, {"X": 0.6002484560012817, "Y": 0.22720041871070862}, {"X": 0.600255012512207, "Y": 0.24010588228702545}, {"X": 0.46371403336524963, "Y": 0.23353193700313568}]}, "Id": "5e29a96d-5465-44ee-b383-eca6d4d21de2", "Relationships": [{"Type": "CHILD", "Ids": ["cb473880-c179-4f25-905d-7816a5484ee3", "012010ef-402b-49fb-ad46-30b72522855f", "55529445-7d7a-46b6-8fb5-7a8d9c0868d0"]}]}, {"BlockType": "LINE", "Confidence": 99.5179672241211, "Text": "Load # BIR-29210", "Geometry": {"BoundingBox": {"Width": 0.1690688133239746, "Height": 0.02063271962106228, "Left": 0.07089979946613312, "Top": 0.2207895815372467}, "Polygon": [{"X": 0.07114945352077484, "Y": 0.2207895815372467}, {"X": 0.23996861279010773, "Y": 0.22884292900562286}, {"X": 0.23980271816253662, "Y": 0.24142229557037354}, {"X": 0.07089979946613312, "Y": 0.2335195392370224}]}, "Id": "b98901c9-0083-4f46-baab-7c7650578ba1", "Relationships": [{"Type": "CHILD", "Ids": ["e3adfbbb-6a20-451c-8e6c-db2929d333fd", "53709955-54f5-410f-9327-1d6961dede2a", "212702e5-5330-42c2-ba72-ab77fa7dea04"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tons", "Geometry": {"BoundingBox": {"Width": 0.03479186072945595, "Height": 0.010224103927612305, "Left": 0.18484605848789215, "Top": 0.3172008991241455}, "Polygon": [{"X": 0.1849803626537323, "Y": 0.3172008991241455}, {"X": 0.2196379154920578, "Y": 0.3186258375644684}, {"X": 0.21951548755168915, "Y": 0.3274250030517578}, {"X": 0.18484605848789215, "Y": 0.3260217308998108}]}, "Id": "a1bba808-1a54-45da-a967-4b0e9a1b3808", "Relationships": [{"Type": "CHILD", "Ids": ["4be6cc8f-7988-4007-96fc-27a94d9a196c"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "LB", "Geometry": {"BoundingBox": {"Width": 0.018349122256040573, "Height": 0.009492579847574234, "Left": 0.27276042103767395, "Top": 0.321911096572876}, "Polygon": [{"X": 0.27286458015441895, "Y": 0.321911096572876}, {"X": 0.291109561920166, "Y": 0.32265979051589966}, {"X": 0.2910115718841553, "Y": 0.3314036726951599}, {"X": 0.27276042103767395, "Y": 0.3306663930416107}]}, "Id": "f7884ad5-3de8-4fa7-89dd-31d278095400", "Relationships": [{"Type": "CHILD", "Ids": ["be30acfc-23f6-4756-8794-4d46e0f1e6b3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Gross", "Geometry": {"BoundingBox": {"Width": 0.04350621625781059, "Height": 0.010831925086677074, "Left": 0.06832753866910934, "Top": 0.326516330242157}, "Polygon": [{"X": 0.0685066357254982, "Y": 0.326516330242157}, {"X": 0.11183375120162964, "Y": 0.328253835439682}, {"X": 0.11167013645172119, "Y": 0.33734825253486633}, {"X": 0.06832753866910934, "Y": 0.33563855290412903}]}, "Id": "ec0e8a68-1a1d-4037-a698-39307fc6c045", "Relationships": [{"Type": "CHILD", "Ids": ["b10987ea-4321-4ea1-964b-b60ced8b5fc0"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "36.67", "Geometry": {"BoundingBox": {"Width": 0.035758018493652344, "Height": 0.010406378656625748, "Left": 0.18296101689338684, "Top": 0.33157312870025635}, "Polygon": [{"X": 0.1830986589193344, "Y": 0.33157312870025635}, {"X": 0.21871903538703918, "Y": 0.3330003619194031}, {"X": 0.21859386563301086, "Y": 0.3419795036315918}, {"X": 0.18296101689338684, "Y": 0.3405749797821045}]}, "Id": "77ed6ce3-9039-46c4-af4b-e34e19735789", "Relationships": [{"Type": "CHILD", "Ids": ["a550ff63-3b31-4a34-8d4f-7961bba7d1e0"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "73,340", "Geometry": {"BoundingBox": {"Width": 0.04313262552022934, "Height": 0.01164830569177866, "Left": 0.24963611364364624, "Top": 0.33459728956222534}, "Polygon": [{"X": 0.24976320564746857, "Y": 0.33459728956222534}, {"X": 0.2927687466144562, "Y": 0.33631935715675354}, {"X": 0.2926582098007202, "Y": 0.34624558687210083}, {"X": 0.24963611364364624, "Y": 0.34455406665802}]}, "Id": "bfa2a12a-b4b4-4a23-b76e-4c1dacec8c0e", "Relationships": [{"Type": "CHILD", "Ids": ["210ccf4d-764c-4544-b89b-ba56bd998414"]}]}, {"BlockType": "LINE", "Confidence": 99.16265106201172, "Text": "Date In 11/11/22 4:49:27", "Geometry": {"BoundingBox": {"Width": 0.1692066788673401, "Height": 0.018061326816678047, "Left": 0.3174854815006256, "Top": 0.3350638449192047}, "Polygon": [{"X": 0.3176017105579376, "Y": 0.3350638449192047}, {"X": 0.4866921603679657, "Y": 0.3418624997138977}, {"X": 0.48664945363998413, "Y": 0.3531251549720764}, {"X": 0.3174854815006256, "Y": 0.3464646339416504}]}, "Id": "9191facb-a671-4dc3-9c82-f49401291f29", "Relationships": [{"Type": "CHILD", "Ids": ["2c941904-fe8b-4680-a74d-1665bd256f71", "89f82dca-f2d7-4e2f-a8de-5c387d3fff4f", "5b1a1b35-5d88-41ba-b38b-1a605ab52bda", "977b2675-0f4f-4eae-98ef-eb3afb72163d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.032564740628004074, "Height": 0.010362623259425163, "Left": 0.06769140809774399, "Top": 0.3403594195842743}, "Polygon": [{"X": 0.06787052750587463, "Y": 0.3403594195842743}, {"X": 0.10025614500045776, "Y": 0.3416258692741394}, {"X": 0.10008859634399414, "Y": 0.3507220447063446}, {"X": 0.06769140809774399, "Y": 0.34947633743286133}]}, "Id": "6c40fb1c-af8f-4bdb-ac82-6bfacd09c673", "Relationships": [{"Type": "CHILD", "Ids": ["b38628cb-5f19-4bff-a28f-8f6eea86a1f2"]}]}, {"BlockType": "LINE", "Confidence": 99.60140228271484, "Text": "15.30", "Geometry": {"BoundingBox": {"Width": 0.03524325042963028, "Height": 0.01012217253446579, "Left": 0.18350127339363098, "Top": 0.3454247713088989}, "Polygon": [{"X": 0.1836351454257965, "Y": 0.3454247713088989}, {"X": 0.21874451637268066, "Y": 0.3467963635921478}, {"X": 0.2186226099729538, "Y": 0.3555469214916229}, {"X": 0.18350127339363098, "Y": 0.3541971743106842}]}, "Id": "33209559-5136-4e2a-93f8-dea2bffe76ac", "Relationships": [{"Type": "CHILD", "Ids": ["3cdf0e99-a3d1-4a59-914b-9fe81fe917f1"]}]}, {"BlockType": "LINE", "Confidence": 99.59163665771484, "Text": "30,600", "Geometry": {"BoundingBox": {"Width": 0.042908672243356705, "Height": 0.011480916291475296, "Left": 0.24950359761714935, "Top": 0.3484209179878235}, "Polygon": [{"X": 0.2496291846036911, "Y": 0.3484209179878235}, {"X": 0.29241225123405457, "Y": 0.35009101033210754}, {"X": 0.2923029363155365, "Y": 0.3599018156528473}, {"X": 0.24950359761714935, "Y": 0.35826173424720764}]}, "Id": "f22e7a46-75f6-4cf3-bae0-b1ac5a468b3e", "Relationships": [{"Type": "CHILD", "Ids": ["e0335233-c8b0-401e-bcbb-d70ac6981f4c"]}]}, {"BlockType": "LINE", "Confidence": 99.68002319335938, "Text": "Date Out 11/11/22 5:32:40", "Geometry": {"BoundingBox": {"Width": 0.16895774006843567, "Height": 0.018160879611968994, "Left": 0.3173535168170929, "Top": 0.3489113450050354}, "Polygon": [{"X": 0.3174726366996765, "Y": 0.3489113450050354}, {"X": 0.48631125688552856, "Y": 0.3555288314819336}, {"X": 0.4862673282623291, "Y": 0.3670722246170044}, {"X": 0.3173535168170929, "Y": 0.3605961501598358}]}, "Id": "218e8d8a-761f-4164-81bc-7961b10e2f79", "Relationships": [{"Type": "CHILD", "Ids": ["b9bf933a-d55f-4b34-8361-a08c2932f27a", "c9ff2dbb-a205-4e59-a812-f3a3730af158", "54d5ab16-ddd5-4737-bc59-c03fdb4ae219", "32bcf6f4-8cfc-4cf4-84fb-96598af43cc3"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Net", "Geometry": {"BoundingBox": {"Width": 0.024831296876072884, "Height": 0.010065760463476181, "Left": 0.06927561014890671, "Top": 0.3550315797328949}, "Polygon": [{"X": 0.0694546028971672, "Y": 0.3550315797328949}, {"X": 0.09410690516233444, "Y": 0.35596975684165955}, {"X": 0.0939367413520813, "Y": 0.3650973439216614}, {"X": 0.06927561014890671, "Y": 0.364175021648407}]}, "Id": "03fa2de1-7a7d-4a42-9ac0-3f4d6c11b852", "Relationships": [{"Type": "CHILD", "Ids": ["f2c58432-1d85-4ae2-8a5a-913ebfe00d08"]}]}, {"BlockType": "LINE", "Confidence": 99.32318115234375, "Text": "21.37", "Geometry": {"BoundingBox": {"Width": 0.03584318980574608, "Height": 0.010149598121643066, "Left": 0.1821271926164627, "Top": 0.35976719856262207}, "Polygon": [{"X": 0.1822620928287506, "Y": 0.35976719856262207}, {"X": 0.2179703712463379, "Y": 0.36112499237060547}, {"X": 0.21784770488739014, "Y": 0.36991679668426514}, {"X": 0.1821271926164627, "Y": 0.3685813546180725}]}, "Id": "d42c584d-98a8-487b-adf3-2536a13387e4", "Relationships": [{"Type": "CHILD", "Ids": ["ffe55a09-1ca9-461c-976c-7f0371d45c7f"]}]}, {"BlockType": "LINE", "Confidence": 98.77490997314453, "Text": "42,740", "Geometry": {"BoundingBox": {"Width": 0.043745700269937515, "Height": 0.011544073931872845, "Left": 0.24982568621635437, "Top": 0.3625621497631073}, "Polygon": [{"X": 0.2499520629644394, "Y": 0.3625621497631073}, {"X": 0.2935713827610016, "Y": 0.3642200231552124}, {"X": 0.29346171021461487, "Y": 0.37410619854927063}, {"X": 0.24982568621635437, "Y": 0.3724791705608368}]}, "Id": "600954d7-99a5-48bb-bd07-f9f1d444e744", "Relationships": [{"Type": "CHILD", "Ids": ["21eeb5fa-8250-4735-a2af-f13d7e9d80ff"]}]}, {"BlockType": "LINE", "Confidence": 99.87104797363281, "Text": "Buyer: 018539", "Geometry": {"BoundingBox": {"Width": 0.11180038005113602, "Height": 0.012203041464090347, "Left": 0.06391336023807526, "Top": 0.8455177545547485}, "Polygon": [{"X": 0.06414603441953659, "Y": 0.8455177545547485}, {"X": 0.17571374773979187, "Y": 0.8458331823348999}, {"X": 0.17553219199180603, "Y": 0.8577207922935486}, {"X": 0.06391336023807526, "Y": 0.8575012683868408}]}, "Id": "8377792f-05c2-4c05-a840-0cc9f70f4a11", "Relationships": [{"Type": "CHILD", "Ids": ["35fdf55a-7551-438d-a006-642d301810a1", "7e65951c-d729-4927-9e7d-ae082fd4680d"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02303648181259632, "Height": 0.011502865701913834, "Left": 0.43453511595726013, "Top": 0.10306371748447418}, "Polygon": [{"X": 0.43459439277648926, "Y": 0.10306371748447418}, {"X": 0.4575715959072113, "Y": 0.10438871383666992}, {"X": 0.45752131938934326, "Y": 0.11456657946109772}, {"X": 0.43453511595726013, "Y": 0.11325834691524506}]}, "Id": "246f81e3-e357-4be0-9f06-b8a2598aec09"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Paper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04625142365694046, "Height": 0.014365507289767265, "Left": 0.46251457929611206, "Top": 0.10407543927431107}, "Polygon": [{"X": 0.46257030963897705, "Y": 0.10407543927431107}, {"X": 0.5087659955024719, "Y": 0.10674141347408295}, {"X": 0.5087311267852783, "Y": 0.11844094842672348}, {"X": 0.46251457929611206, "Y": 0.11581388115882874}]}, "Id": "80dedf25-f500-4225-8f22-3a3faa08c95c"}, {"BlockType": "WORD", "Confidence": 98.40740203857422, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02534719556570053, "Height": 0.012783447280526161, "Left": 0.6854881048202515, "Top": 0.11459143459796906}, "Polygon": [{"X": 0.6854881048202515, "Y": 0.11459143459796906}, {"X": 0.7107817530632019, "Y": 0.11605560779571533}, {"X": 0.710835337638855, "Y": 0.12737488746643066}, {"X": 0.6855308413505554, "Y": 0.12593165040016174}]}, "Id": "8b90aa6f-ce7c-4215-9a1d-7e4d654d75d5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037064213305711746, "Height": 0.019600829109549522, "Left": 0.7289064526557922, "Top": 0.11616068333387375}, "Polygon": [{"X": 0.7289064526557922, "Y": 0.11616068333387375}, {"X": 0.7658513188362122, "Y": 0.11830193549394608}, {"X": 0.7659706473350525, "Y": 0.13576151430606842}, {"X": 0.7290013432502747, "Y": 0.13366761803627014}]}, "Id": "2e20dd8f-427b-4956-95e4-ecfd4ea9538a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PAPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07873455435037613, "Height": 0.02363426238298416, "Left": 0.7735921144485474, "Top": 0.11560371518135071}, "Polygon": [{"X": 0.7735921144485474, "Y": 0.11560371518135071}, {"X": 0.8521336317062378, "Y": 0.12017452716827393}, {"X": 0.8523266911506653, "Y": 0.13923798501491547}, {"X": 0.7737288475036621, "Y": 0.13477784395217896}]}, "Id": "4f76a9b0-bb4b-4f2f-9df7-8debb0175dfb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Biron", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04243960604071617, "Height": 0.012543072924017906, "Left": 0.3983845114707947, "Top": 0.12132956087589264}, "Polygon": [{"X": 0.3984578549861908, "Y": 0.12132956087589264}, {"X": 0.44082412123680115, "Y": 0.12370923161506653}, {"X": 0.44076746702194214, "Y": 0.1338726282119751}, {"X": 0.3983845114707947, "Y": 0.13152381777763367}]}, "Id": "ce06e09f-5fbd-49bb-9039-b312b8d6e238"}, {"BlockType": "WORD", "Confidence": 99.1260757446289, "Text": "Scale(Biron)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09634093940258026, "Height": 0.01941770873963833, "Left": 0.44620445370674133, "Top": 0.12038224935531616}, "Polygon": [{"X": 0.44628003239631653, "Y": 0.12038224935531616}, {"X": 0.5425453782081604, "Y": 0.12581519782543182}, {"X": 0.5425217151641846, "Y": 0.13979995250701904}, {"X": 0.44620445370674133, "Y": 0.13446423411369324}]}, "Id": "f9902f35-02e8-4c79-83a8-b73b1eecffe2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "29210", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04831111058592796, "Height": 0.013186685740947723, "Left": 0.11325331777334213, "Top": 0.12163593620061874}, "Polygon": [{"X": 0.11344444006681442, "Y": 0.12163593620061874}, {"X": 0.1615644246339798, "Y": 0.12428221851587296}, {"X": 0.16139332950115204, "Y": 0.13482262194156647}, {"X": 0.11325331777334213, "Y": 0.13221195340156555}]}, "Id": "7a1d996a-1a19-4663-aec7-c9efb9a8ce09"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Source", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0461830236017704, "Height": 0.01109231635928154, "Left": 0.4676319658756256, "Top": 0.14745007455348969}, "Polygon": [{"X": 0.46767106652259827, "Y": 0.14745007455348969}, {"X": 0.5138149857521057, "Y": 0.14996609091758728}, {"X": 0.5137910842895508, "Y": 0.15854239463806152}, {"X": 0.4676319658756256, "Y": 0.15605494379997253}]}, "Id": "34a5b615-e067-42ab-ad3b-858916fe97dd"}, {"BlockType": "WORD", "Confidence": 21.44660758972168, "Text": "VIM_AURO_IL_RATHBONE(VIM RECYCLERSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2976658046245575, "Height": 0.03789341449737549, "Left": 0.5538837313652039, "Top": 0.13808947801589966}, "Polygon": [{"X": 0.5539112091064453, "Y": 0.13808947801589966}, {"X": 0.8513341546058655, "Y": 0.15461775660514832}, {"X": 0.8515495657920837, "Y": 0.17598289251327515}, {"X": 0.5538837313652039, "Y": 0.15992482006549835}]}, "Id": "b87c1ded-6276-498a-8540-bcf0c470ea4d"}, {"BlockType": "WORD", "Confidence": 98.76495361328125, "Text": "P)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01279358472675085, "Height": 0.011127179488539696, "Left": 0.8555433750152588, "Top": 0.15493690967559814}, "Polygon": [{"X": 0.8555433750152588, "Y": 0.15493690967559814}, {"X": 0.8682251572608948, "Y": 0.155641570687294}, {"X": 0.8683369159698486, "Y": 0.1660640984773636}, {"X": 0.8556501865386963, "Y": 0.16536922752857208}]}, "Id": "67b0ce82-b4d5-4378-b7a1-065b43379ee1"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06306661665439606, "Height": 0.01468481682240963, "Left": 0.0724496990442276, "Top": 0.13722670078277588}, "Polygon": [{"X": 0.0726725235581398, "Y": 0.13722670078277588}, {"X": 0.13551631569862366, "Y": 0.1406022012233734}, {"X": 0.13532161712646484, "Y": 0.15191152691841125}, {"X": 0.0724496990442276, "Y": 0.14858588576316833}]}, "Id": "aab5f40e-ce7c-4e76-9995-43b0d6e5a824"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04824904352426529, "Height": 0.01246823463588953, "Left": 0.18080130219459534, "Top": 0.14646676182746887}, "Polygon": [{"X": 0.18095500767230988, "Y": 0.14646676182746887}, {"X": 0.22905033826828003, "Y": 0.1490381509065628}, {"X": 0.22891530394554138, "Y": 0.15893499553203583}, {"X": 0.18080130219459534, "Y": 0.15639722347259521}]}, "Id": "32849674-1dc7-48cc-9858-8f14052fafdf"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "(HARVESTER)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09644284099340439, "Height": 0.017852887511253357, "Left": 0.2328699827194214, "Top": 0.15033270418643951}, "Polygon": [{"X": 0.23304268717765808, "Y": 0.15033270418643951}, {"X": 0.32931283116340637, "Y": 0.15547217428684235}, {"X": 0.3291879892349243, "Y": 0.16818559169769287}, {"X": 0.2328699827194214, "Y": 0.16313324868679047}]}, "Id": "63cab99f-05d7-4f05-98a5-1d223f5d5c85"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07361311465501785, "Height": 0.014335665851831436, "Left": 0.4676561653614044, "Top": 0.16074106097221375}, "Polygon": [{"X": 0.467703640460968, "Y": 0.16074106097221375}, {"X": 0.5412693023681641, "Y": 0.16467994451522827}, {"X": 0.5412511825561523, "Y": 0.17507672309875488}, {"X": 0.4676561653614044, "Y": 0.17119313776493073}]}, "Id": "a7e46e96-89f9-4aaa-997b-9b2a44bdb832"}, {"BlockType": "WORD", "Confidence": 95.91757202148438, "Text": "BIRON(BIRON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09057942032814026, "Height": 0.018105806782841682, "Left": 0.553171694278717, "Top": 0.1607941836118698}, "Polygon": [{"X": 0.5531888008117676, "Y": 0.1607941836118698}, {"X": 0.643722414970398, "Y": 0.16567206382751465}, {"X": 0.6437510848045349, "Y": 0.17889998853206635}, {"X": 0.553171694278717, "Y": 0.17410939931869507}]}, "Id": "480a8904-06cf-49df-882a-bbffd7ccb030"}, {"BlockType": "WORD", "Confidence": 96.87340545654297, "Text": "MILL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03480115532875061, "Height": 0.012240788899362087, "Left": 0.6475403904914856, "Top": 0.16487477719783783}, "Polygon": [{"X": 0.6475403904914856, "Y": 0.16487477719783783}, {"X": 0.6823038458824158, "Y": 0.16675041615962982}, {"X": 0.6823415160179138, "Y": 0.1771155744791031}, {"X": 0.6475644111633301, "Y": 0.17526626586914062}]}, "Id": "9b38aaee-aeda-46cc-8897-66b052260fa3"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041886042803525925, "Height": 0.011855510994791985, "Left": 0.7396447658538818, "Top": 0.19546400010585785}, "Polygon": [{"X": 0.7396447658538818, "Y": 0.19546400010585785}, {"X": 0.7814591526985168, "Y": 0.19763924181461334}, {"X": 0.7815308570861816, "Y": 0.20731951296329498}, {"X": 0.7397012710571289, "Y": 0.20517413318157196}]}, "Id": "b628edfb-840a-4fba-8ff7-d57643aa6099"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "AT26", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0333498939871788, "Height": 0.011552166193723679, "Left": 0.8000366687774658, "Top": 0.19660118222236633}, "Polygon": [{"X": 0.8000366687774658, "Y": 0.19660118222236633}, {"X": 0.8332945108413696, "Y": 0.19833634793758392}, {"X": 0.8333866000175476, "Y": 0.2081533521413803}, {"X": 0.8001165390014648, "Y": 0.20644235610961914}]}, "Id": "2559f434-8fe0-4230-95a7-943f17ad737f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03953065350651741, "Height": 0.01093638688325882, "Left": 0.4669767916202545, "Top": 0.19175946712493896}, "Polygon": [{"X": 0.4670175611972809, "Y": 0.19175946712493896}, {"X": 0.5065074563026428, "Y": 0.19378319382667542}, {"X": 0.5064801573753357, "Y": 0.20269586145877838}, {"X": 0.4669767916202545, "Y": 0.20069755613803864}]}, "Id": "cb276d84-a759-4e1b-a9a0-2f8b1c9c07bc"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "ZACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037287645041942596, "Height": 0.011582777835428715, "Left": 0.5518341660499573, "Top": 0.19258533418178558}, "Polygon": [{"X": 0.5518471002578735, "Y": 0.19258533418178558}, {"X": 0.5891209840774536, "Y": 0.1945052593946457}, {"X": 0.5891218185424805, "Y": 0.20416811108589172}, {"X": 0.5518341660499573, "Y": 0.2022743672132492}]}, "Id": "9d8e09df-e0cd-4a77-8ac5-7d5a1c95233e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0318727008998394, "Height": 0.01108391024172306, "Left": 0.07185383141040802, "Top": 0.18583805859088898}, "Polygon": [{"X": 0.07204034179449081, "Y": 0.18583805859088898}, {"X": 0.10372653603553772, "Y": 0.1874292641878128}, {"X": 0.10355190932750702, "Y": 0.1969219595193863}, {"X": 0.07185383141040802, "Y": 0.19535183906555176}]}, "Id": "39baa2cc-b1cf-4711-841a-cb20e392f99b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029393751174211502, "Height": 0.013052918016910553, "Left": 0.07053781300783157, "Top": 0.20313788950443268}, "Polygon": [{"X": 0.07076658308506012, "Y": 0.20313788950443268}, {"X": 0.09993156790733337, "Y": 0.2045661211013794}, {"X": 0.0997161790728569, "Y": 0.21619081497192383}, {"X": 0.07053781300783157, "Y": 0.2147863656282425}]}, "Id": "6c2b9bc5-d8db-426c-9046-93a09579464b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04412739351391792, "Height": 0.0118827223777771, "Left": 0.10367673635482788, "Top": 0.20529618859291077}, "Polygon": [{"X": 0.103856161236763, "Y": 0.20529618859291077}, {"X": 0.1478041261434555, "Y": 0.2074466496706009}, {"X": 0.14764155447483063, "Y": 0.21717891097068787}, {"X": 0.10367673635482788, "Y": 0.2150585651397705}]}, "Id": "0a614390-cc73-4312-9f10-158a7a3171b1"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "BIR0029912", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07964839041233063, "Height": 0.014256478287279606, "Left": 0.16322806477546692, "Top": 0.20894882082939148}, "Polygon": [{"X": 0.16339610517024994, "Y": 0.20894882082939148}, {"X": 0.24287645518779755, "Y": 0.2128337025642395}, {"X": 0.24274075031280518, "Y": 0.2232052981853485}, {"X": 0.16322806477546692, "Y": 0.21937885880470276}]}, "Id": "929a73d1-640b-49f5-a47b-c5ad07076cb3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05204581096768379, "Height": 0.012009023688733578, "Left": 0.46371403336524963, "Top": 0.22402749955654144}, "Polygon": [{"X": 0.4637584984302521, "Y": 0.22402749955654144}, {"X": 0.5157598257064819, "Y": 0.22656771540641785}, {"X": 0.5157342553138733, "Y": 0.2360365241765976}, {"X": 0.46371403336524963, "Y": 0.23353193700313568}]}, "Id": "cb473880-c179-4f25-905d-7816a5484ee3"}, {"BlockType": "WORD", "Confidence": 99.68590545654297, "Text": "OCC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030197203159332275, "Height": 0.01096768956631422, "Left": 0.5412454605102539, "Top": 0.22518888115882874}, "Polygon": [{"X": 0.5412619709968567, "Y": 0.22518888115882874}, {"X": 0.5714426636695862, "Y": 0.2266690731048584}, {"X": 0.5714371204376221, "Y": 0.23615656793117523}, {"X": 0.5412454605102539, "Y": 0.23469719290733337}]}, "Id": "012010ef-402b-49fb-ad46-30b72522855f"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "11D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024471506476402283, "Height": 0.010535159148275852, "Left": 0.5757817029953003, "Top": 0.22599905729293823}, "Polygon": [{"X": 0.5757856369018555, "Y": 0.22599905729293823}, {"X": 0.6002484560012817, "Y": 0.22720041871070862}, {"X": 0.6002532243728638, "Y": 0.2365342080593109}, {"X": 0.5757817029953003, "Y": 0.23534947633743286}]}, "Id": "55529445-7d7a-46b6-8fb5-7a8d9c0868d0"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03927716612815857, "Height": 0.012242043390870094, "Left": 0.0709453672170639, "Top": 0.2207895815372467}, "Polygon": [{"X": 0.07114945352077484, "Y": 0.2207895815372467}, {"X": 0.11022252589464188, "Y": 0.22265352308750153}, {"X": 0.11003443598747253, "Y": 0.23303161561489105}, {"X": 0.0709453672170639, "Y": 0.23119616508483887}]}, "Id": "e3adfbbb-6a20-451c-8e6c-db2929d333fd"}, {"BlockType": "WORD", "Confidence": 98.798828125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010462954640388489, "Height": 0.010016856715083122, "Left": 0.11445949226617813, "Top": 0.22370648384094238}, "Polygon": [{"X": 0.11463067680597305, "Y": 0.22370648384094238}, {"X": 0.12492244690656662, "Y": 0.22419682145118713}, {"X": 0.1247551217675209, "Y": 0.23372334241867065}, {"X": 0.11445949226617813, "Y": 0.23323990404605865}]}, "Id": "53709955-54f5-410f-9327-1d6961dede2a"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "BIR-29210", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07712502032518387, "Height": 0.014598649926483631, "Left": 0.1628219336271286, "Top": 0.22682364284992218}, "Polygon": [{"X": 0.16299913823604584, "Y": 0.22682364284992218}, {"X": 0.23994694650173187, "Y": 0.23048517107963562}, {"X": 0.23980271816253662, "Y": 0.24142229557037354}, {"X": 0.1628219336271286, "Y": 0.23782046139240265}]}, "Id": "212702e5-5330-42c2-ba72-ab77fa7dea04"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tons", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03479186072945595, "Height": 0.010224103927612305, "Left": 0.18484605848789215, "Top": 0.3172008991241455}, "Polygon": [{"X": 0.1849803626537323, "Y": 0.3172008991241455}, {"X": 0.2196379154920578, "Y": 0.3186258375644684}, {"X": 0.21951548755168915, "Y": 0.3274250030517578}, {"X": 0.18484605848789215, "Y": 0.3260217308998108}]}, "Id": "4be6cc8f-7988-4007-96fc-27a94d9a196c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018349122256040573, "Height": 0.009492579847574234, "Left": 0.27276042103767395, "Top": 0.321911096572876}, "Polygon": [{"X": 0.27286458015441895, "Y": 0.321911096572876}, {"X": 0.291109561920166, "Y": 0.32265979051589966}, {"X": 0.2910115718841553, "Y": 0.3314036726951599}, {"X": 0.27276042103767395, "Y": 0.3306663930416107}]}, "Id": "be30acfc-23f6-4756-8794-4d46e0f1e6b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04350621625781059, "Height": 0.010831925086677074, "Left": 0.06832753866910934, "Top": 0.326516330242157}, "Polygon": [{"X": 0.0685066357254982, "Y": 0.326516330242157}, {"X": 0.11183375120162964, "Y": 0.328253835439682}, {"X": 0.11167013645172119, "Y": 0.33734825253486633}, {"X": 0.06832753866910934, "Y": 0.33563855290412903}]}, "Id": "b10987ea-4321-4ea1-964b-b60ced8b5fc0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "36.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035758018493652344, "Height": 0.010406378656625748, "Left": 0.18296101689338684, "Top": 0.33157312870025635}, "Polygon": [{"X": 0.1830986589193344, "Y": 0.33157312870025635}, {"X": 0.21871903538703918, "Y": 0.3330003619194031}, {"X": 0.21859386563301086, "Y": 0.3419795036315918}, {"X": 0.18296101689338684, "Y": 0.3405749797821045}]}, "Id": "a550ff63-3b31-4a34-8d4f-7961bba7d1e0"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "73,340", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313262552022934, "Height": 0.01164830569177866, "Left": 0.24963611364364624, "Top": 0.33459728956222534}, "Polygon": [{"X": 0.24976320564746857, "Y": 0.33459728956222534}, {"X": 0.2927687466144562, "Y": 0.33631935715675354}, {"X": 0.2926582098007202, "Y": 0.34624558687210083}, {"X": 0.24963611364364624, "Y": 0.34455406665802}]}, "Id": "210ccf4d-764c-4544-b89b-ba56bd998414"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029839908704161644, "Height": 0.009625136852264404, "Left": 0.3174854815006256, "Top": 0.33801138401031494}, "Polygon": [{"X": 0.3175716698169708, "Y": 0.33801138401031494}, {"X": 0.3473253846168518, "Y": 0.33920127153396606}, {"X": 0.347248911857605, "Y": 0.34763652086257935}, {"X": 0.3174854815006256, "Y": 0.3464646339416504}]}, "Id": "2c941904-fe8b-4680-a74d-1665bd256f71"}, {"BlockType": "WORD", "Confidence": 97.91832733154297, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011558234691619873, "Height": 0.008902370929718018, "Left": 0.351659893989563, "Top": 0.33919307589530945}, "Polygon": [{"X": 0.35173511505126953, "Y": 0.33919307589530945}, {"X": 0.36321812868118286, "Y": 0.33965247869491577}, {"X": 0.3631466329097748, "Y": 0.34809544682502747}, {"X": 0.351659893989563, "Y": 0.34764304757118225}]}, "Id": "89f82dca-f2d7-4e2f-a8de-5c387d3fff4f"}, {"BlockType": "WORD", "Confidence": 99.02523040771484, "Text": "11/11/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05208694562315941, "Height": 0.011130745522677898, "Left": 0.385672390460968, "Top": 0.33888164162635803}, "Polygon": [{"X": 0.3857415020465851, "Y": 0.33888164162635803}, {"X": 0.4377593398094177, "Y": 0.3409690260887146}, {"X": 0.4377082884311676, "Y": 0.3500123918056488}, {"X": 0.385672390460968, "Y": 0.3479590117931366}]}, "Id": "5b1a1b35-5d88-41ba-b38b-1a605ab52bda"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "4:49:27", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04588759317994118, "Height": 0.011061602272093296, "Left": 0.4408045709133148, "Top": 0.340019553899765}, "Polygon": [{"X": 0.4408556818962097, "Y": 0.340019553899765}, {"X": 0.4866921603679657, "Y": 0.3418624997138977}, {"X": 0.48665720224380493, "Y": 0.35108113288879395}, {"X": 0.4408045709133148, "Y": 0.3492688238620758}]}, "Id": "977b2675-0f4f-4eae-98ef-eb3afb72163d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032564740628004074, "Height": 0.010362623259425163, "Left": 0.06769140809774399, "Top": 0.3403594195842743}, "Polygon": [{"X": 0.06787052750587463, "Y": 0.3403594195842743}, {"X": 0.10025614500045776, "Y": 0.3416258692741394}, {"X": 0.10008859634399414, "Y": 0.3507220447063446}, {"X": 0.06769140809774399, "Y": 0.34947633743286133}]}, "Id": "b38628cb-5f19-4bff-a28f-8f6eea86a1f2"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "15.30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03524325042963028, "Height": 0.01012217253446579, "Left": 0.18350127339363098, "Top": 0.3454247713088989}, "Polygon": [{"X": 0.1836351454257965, "Y": 0.3454247713088989}, {"X": 0.21874451637268066, "Y": 0.3467963635921478}, {"X": 0.2186226099729538, "Y": 0.3555469214916229}, {"X": 0.18350127339363098, "Y": 0.3541971743106842}]}, "Id": "3cdf0e99-a3d1-4a59-914b-9fe81fe917f1"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "30,600", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042908672243356705, "Height": 0.011480916291475296, "Left": 0.24950359761714935, "Top": 0.3484209179878235}, "Polygon": [{"X": 0.2496291846036911, "Y": 0.3484209179878235}, {"X": 0.29241225123405457, "Y": 0.35009101033210754}, {"X": 0.2923029363155365, "Y": 0.3599018156528473}, {"X": 0.24950359761714935, "Y": 0.35826173424720764}]}, "Id": "e0335233-c8b0-401e-bcbb-d70ac6981f4c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029668033123016357, "Height": 0.00973564200103283, "Left": 0.3173535168170929, "Top": 0.35199496150016785}, "Polygon": [{"X": 0.3174411952495575, "Y": 0.35199496150016785}, {"X": 0.34702154994010925, "Y": 0.3531476855278015}, {"X": 0.3469436764717102, "Y": 0.3617306053638458}, {"X": 0.3173535168170929, "Y": 0.3605961501598358}]}, "Id": "b9bf933a-d55f-4b34-8361-a08c2932f27a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02425566129386425, "Height": 0.0095891822129488, "Left": 0.3510790467262268, "Top": 0.3529113829135895}, "Polygon": [{"X": 0.3511562943458557, "Y": 0.3529113829135895}, {"X": 0.3753347098827362, "Y": 0.35385429859161377}, {"X": 0.375265508890152, "Y": 0.3625005781650543}, {"X": 0.3510790467262268, "Y": 0.3615727126598358}]}, "Id": "c9ff2dbb-a205-4e59-a812-f3a3730af158"}, {"BlockType": "WORD", "Confidence": 98.88612365722656, "Text": "11/11/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0517602302134037, "Height": 0.011139686219394207, "Left": 0.3854284882545471, "Top": 0.35277697443962097}, "Polygon": [{"X": 0.38549819588661194, "Y": 0.35277697443962097}, {"X": 0.4371887147426605, "Y": 0.35479840636253357}, {"X": 0.43713706731796265, "Y": 0.36391666531562805}, {"X": 0.3854284882545471, "Y": 0.361929327249527}]}, "Id": "54d5ab16-ddd5-4737-bc59-c03fdb4ae219"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "5:32:40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04497740790247917, "Height": 0.011073242872953415, "Left": 0.4413338601589203, "Top": 0.353767991065979}, "Polygon": [{"X": 0.44138526916503906, "Y": 0.353767991065979}, {"X": 0.48631125688552856, "Y": 0.3555288314819336}, {"X": 0.4862758219242096, "Y": 0.3648412525653839}, {"X": 0.4413338601589203, "Y": 0.36311075091362}]}, "Id": "32bcf6f4-8cfc-4cf4-84fb-96598af43cc3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024831296876072884, "Height": 0.010065760463476181, "Left": 0.06927561014890671, "Top": 0.3550315797328949}, "Polygon": [{"X": 0.0694546028971672, "Y": 0.3550315797328949}, {"X": 0.09410690516233444, "Y": 0.35596975684165955}, {"X": 0.0939367413520813, "Y": 0.3650973439216614}, {"X": 0.06927561014890671, "Y": 0.364175021648407}]}, "Id": "f2c58432-1d85-4ae2-8a5a-913ebfe00d08"}, {"BlockType": "WORD", "Confidence": 99.32318115234375, "Text": "21.37", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03584318980574608, "Height": 0.010149598121643066, "Left": 0.1821271926164627, "Top": 0.35976719856262207}, "Polygon": [{"X": 0.1822620928287506, "Y": 0.35976719856262207}, {"X": 0.2179703712463379, "Y": 0.36112499237060547}, {"X": 0.21784770488739014, "Y": 0.36991679668426514}, {"X": 0.1821271926164627, "Y": 0.3685813546180725}]}, "Id": "ffe55a09-1ca9-461c-976c-7f0371d45c7f"}, {"BlockType": "WORD", "Confidence": 98.77490997314453, "Text": "42,740", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043745700269937515, "Height": 0.011544073931872845, "Left": 0.24982568621635437, "Top": 0.3625621497631073}, "Polygon": [{"X": 0.2499520629644394, "Y": 0.3625621497631073}, {"X": 0.2935713827610016, "Y": 0.3642200231552124}, {"X": 0.29346171021461487, "Y": 0.37410619854927063}, {"X": 0.24982568621635437, "Y": 0.3724791705608368}]}, "Id": "21eeb5fa-8250-4735-a2af-f13d7e9d80ff"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Buyer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045221924781799316, "Height": 0.01109800860285759, "Left": 0.06391336023807526, "Top": 0.846491813659668}, "Polygon": [{"X": 0.0641271248459816, "Y": 0.846491813659668}, {"X": 0.10913528501987457, "Y": 0.8466159105300903}, {"X": 0.10894055664539337, "Y": 0.8575897812843323}, {"X": 0.06391336023807526, "Y": 0.8575012683868408}]}, "Id": "35fdf55a-7551-438d-a006-642d301810a1"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047770172357559204, "Height": 0.009151441976428032, "Left": 0.12794357538223267, "Top": 0.8456985354423523}, "Polygon": [{"X": 0.1280977576971054, "Y": 0.8456985354423523}, {"X": 0.17571374773979187, "Y": 0.8458331823348999}, {"X": 0.17557603120803833, "Y": 0.8548499941825867}, {"X": 0.12794357538223267, "Y": 0.854746401309967}]}, "Id": "7e65951c-d729-4927-9e7d-ae082fd4680d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "77e8dd4e-d460-4c37-b666-99af3fe63851", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "77e8dd4e-d460-4c37-b666-99af3fe63851", "content-type": "application/x-amz-json-1.1", "content-length": "44668", "date": "Thu, 18 Sep 2025 18:07:23 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "4c8a4759-8e34-43e5-b4be-0f9aa751ee17", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 18:07:26 GMT", "content-type": "application/json", "content-length": "1546", "connection": "keep-alive", "x-amzn-requestid": "4c8a4759-8e34-43e5-b4be-0f9aa751ee17"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page with details: \"ND Paper\", \"Biron Scale\", \"BOL\", \"Trip Ticket\", \"Gross\", \"Tare\", \"Net\". This looks like a scale ticket (weight record). However also contains \"BOL\" and \"Trip Ticket\" referencing BOL. The definitions: scale ticket keywords: Gross, Tare, Net, Weighed At, Truck, Driver, weight, etc. It's a scale ticket. It may also be a BOL. But the presence of gross, tare, net strongly indicates scale ticket. It also has \"BOL\" header, but the content is weight. The description: \"Trip Ticket BIR0029912\" maybe the BOL number. But the content is weight. The question: classify as scale_ticket. The rule: \"Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\" Yes. So doc_type = scale_ticket.\n\nWe need to output an array of objects with page_no and doc_type. The page number is 1. The format: the tool call with JSON: {documents: [{page_no: 1, doc_type: \"scale_ticket\"}]}. Use function call.\n\n"}}}, {"toolUse": {"toolUseId": "tooluse_dofacKdBQY-DTYuu60nR_g", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2311, "outputTokens": 328, "totalTokens": 2639}, "metrics": {"latencyMs": 1477}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/16f952dc_6_scale_ticket_11.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nND Paper\nND\nND PAPER\nBiron Scale(Biron)\n29210\nSource\nVIM_AURO_IL_RATHBONE(VIM RECYCLERSL P)\nContract\n018539 (HARVESTER)\nDestination\nBIRON(BIRON MILL)\nTrailer AT26\nDriver\nZACH\nBOL\nTrip Ticket BIR0029912\nProduct OCC 11D\nLoad # BIR-29210\nTons\nLB\nGross\n36.67\n73,340\nDate In 11/11/22 4:49:27\nTare\n15.30\n30,600\nDate Out 11/11/22 5:32:40\nNet\n21.37\n42,740\nBuyer: 018539\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}