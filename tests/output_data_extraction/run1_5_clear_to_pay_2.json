{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 1.4989719602453988e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "edcce58f-9581-4341-ba12-bd01b1fa56ed", "Relationships": [{"Type": "CHILD", "Ids": ["4f167e57-c091-4cb3-b3a7-7021966c66b3", "ebf1ee73-3e7a-4965-b481-b18e04495c5d", "e08e6b31-84de-49da-8e5d-ed27482c4381", "de285897-e43a-4e4e-b60b-788558295acc", "93b90dfb-4ad2-4e61-a5bf-57a5472263f7", "67f7f280-5fc5-48c5-93be-05ee7509fc77", "b75cc0a9-1065-4ec8-9c91-5d504baa9c65", "7b8ade66-9bff-42d8-b094-8f32f347c68b", "8e8bce47-76d8-452a-996e-8822735ae70a", "0dc48c0d-1604-49e3-b273-ad529af8ec4f", "ae42eff8-9115-41d6-aa13-7a350e83a173", "a0d4274c-f47a-4eff-8cbf-21a2f6f047fc", "d0d9d7cf-6275-4ff9-839d-1485a5ad0b5a", "dc337419-e008-4d78-80aa-0463db45dc99", "b26bda6e-d652-4078-a32f-07e8442e7602", "c8181695-904c-4158-8b55-067e510aa547", "70a00928-afe3-4ee3-86de-d9ca759821b2", "d7b486fa-4c6e-4acf-a8ae-8971b4de45f4", "5c338012-675e-480a-9fd3-80a06e18cbf0", "bdf348f9-8255-491a-a9cc-119cfe0131ec", "924b5630-c52f-44bf-8bc8-7985a0c63f81", "1ad0b579-0294-4a41-833e-130b8c230525", "37b6c972-79c0-492c-a971-6a3e93da09c5", "aac347b6-2d37-445a-98ad-01adef2874db", "b897b463-d0b7-493e-80e1-d1ce56c52ef4", "9dd832e7-501e-4f9a-9bd9-db1df3ad66c3", "84f5fee0-8614-4194-b9c7-8824d2<PERSON>baa4", "35376dfc-6799-468d-805e-d8b0ef396b78", "395b1d12-206f-4315-bd5d-c8c6140d5926", "fa97d06f-461b-4da1-bb3c-6d757f517f9e", "a2479160-b644-4ef8-bd49-1a7cbea82833", "3378956a-1eaf-49f5-96b2-21a983e61308", "c37f12d5-5b13-426a-8dcf-40fc11516881", "310d13ba-00f3-44d8-8eea-63cf33394e02", "46ddc789-b4f3-4600-8501-451c46b10fc5"]}]}, {"BlockType": "LINE", "Confidence": 94.87942504882812, "Text": "Saw 7056563", "Geometry": {"BoundingBox": {"Width": 0.30521300435066223, "Height": 0.06624899059534073, "Left": 0.5896632671356201, "Top": 0.07072446495294571}, "Polygon": [{"X": 0.5896651148796082, "Y": 0.07072446495294571}, {"X": 0.89487624168396, "Y": 0.07076777517795563}, {"X": 0.8948742151260376, "Y": 0.13697345554828644}, {"X": 0.5896632671356201, "Y": 0.1369301825761795}]}, "Id": "4f167e57-c091-4cb3-b3a7-7021966c66b3", "Relationships": [{"Type": "CHILD", "Ids": ["98662dbe-efb5-4458-834f-2d8bab66f734", "b0fc2bd7-0c0b-4e32-9bc0-bbedd2cfff28"]}]}, {"BlockType": "LINE", "Confidence": 20.76201820373535, "Text": "AMBVD", "Geometry": {"BoundingBox": {"Width": 0.22545401751995087, "Height": 0.03536486625671387, "Left": 0.4016489088535309, "Top": 0.1331961750984192}, "Polygon": [{"X": 0.4016498625278473, "Y": 0.1331961750984192}, {"X": 0.6271029114723206, "Y": 0.13322813808918}, {"X": 0.6271018981933594, "Y": 0.16856104135513306}, {"X": 0.4016489088535309, "Y": 0.16852909326553345}]}, "Id": "ebf1ee73-3e7a-4965-b481-b18e04495c5d", "Relationships": [{"Type": "CHILD", "Ids": ["3f243273-e358-4cdc-ae3c-f600b4523ac4"]}]}, {"BlockType": "LINE", "Confidence": 96.95153045654297, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.43205714225769043, "Height": 0.02347600646317005, "Left": 0.300431489944458, "Top": 0.18134063482284546}, "Polygon": [{"X": 0.30043211579322815, "Y": 0.18134063482284546}, {"X": 0.7324886322021484, "Y": 0.18140184879302979}, {"X": 0.7324879169464111, "Y": 0.20481663942337036}, {"X": 0.300431489944458, "Y": 0.20475544035434723}]}, "Id": "e08e6b31-84de-49da-8e5d-ed27482c4381", "Relationships": [{"Type": "CHILD", "Ids": ["9b85ace4-862c-4f96-a9fd-42c700d6cfb8", "3e56585e-0c2d-46a2-b5db-820e40ff3cfa"]}]}, {"BlockType": "LINE", "Confidence": 99.86573791503906, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.34514355659484863, "Height": 0.019788047298789024, "Left": 0.3518599271774292, "Top": 0.2139761745929718}, "Polygon": [{"X": 0.3518604636192322, "Y": 0.2139761745929718}, {"X": 0.6970034837722778, "Y": 0.21402505040168762}, {"X": 0.6970028877258301, "Y": 0.23376421630382538}, {"X": 0.3518599271774292, "Y": 0.23371534049510956}]}, "Id": "de285897-e43a-4e4e-b60b-788558295acc", "Relationships": [{"Type": "CHILD", "Ids": ["ac1bff74-a93e-4d12-8fea-6834d0f1e601", "45169032-4284-467c-91ef-76987c0e23bc", "1863cf46-0fa7-4235-8574-fd00c24828e0"]}]}, {"BlockType": "LINE", "Confidence": 99.56791687011719, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.2923758924007416, "Height": 0.02381926216185093, "Left": 0.37930452823638916, "Top": 0.24309197068214417}, "Polygon": [{"X": 0.3793051540851593, "Y": 0.24309197068214417}, {"X": 0.6716804504394531, "Y": 0.24313335120677948}, {"X": 0.6716797351837158, "Y": 0.26691123843193054}, {"X": 0.37930452823638916, "Y": 0.26686984300613403}]}, "Id": "93b90dfb-4ad2-4e61-a5bf-57a5472263f7", "Relationships": [{"Type": "CHILD", "Ids": ["c02cd5e0-68b7-4d3f-a98d-34c60f19ab4b", "7d2dd2f0-be44-4a82-be81-6f54ec6b52e5"]}]}, {"BlockType": "LINE", "Confidence": 98.12207794189453, "Text": "Load # 20081", "Geometry": {"BoundingBox": {"Width": 0.14595557749271393, "Height": 0.021700123324990273, "Left": 0.11647119373083115, "Top": 0.28144410252571106}, "Polygon": [{"X": 0.11647173017263412, "Y": 0.28144410252571106}, {"X": 0.2624267637729645, "Y": 0.28146475553512573}, {"X": 0.2624261975288391, "Y": 0.3031442165374756}, {"X": 0.11647119373083115, "Y": 0.3031235933303833}]}, "Id": "67f7f280-5fc5-48c5-93be-05ee7509fc77", "Relationships": [{"Type": "CHILD", "Ids": ["af5d20a4-e853-40d3-9d39-75feb3299b64", "4c5fe3fb-e7a4-45fd-8c35-db2424ff6a44", "238c98af-61d7-4a5a-8abd-e1c0593cc615"]}]}, {"BlockType": "LINE", "Confidence": 99.7608413696289, "Text": "Broker", "Geometry": {"BoundingBox": {"Width": 0.05547386035323143, "Height": 0.011601464822888374, "Left": 0.5692568421363831, "Top": 0.286699503660202}, "Polygon": [{"X": 0.5692571401596069, "Y": 0.286699503660202}, {"X": 0.6247307062149048, "Y": 0.2867073714733124}, {"X": 0.6247303485870361, "Y": 0.29830098152160645}, {"X": 0.5692568421363831, "Y": 0.2982931435108185}]}, "Id": "b75cc0a9-1065-4ec8-9c91-5d504baa9c65", "Relationships": [{"Type": "CHILD", "Ids": ["6850cce4-4fa4-4afa-8aa7-b6e5caa8a68d"]}]}, {"BlockType": "LINE", "Confidence": 98.9674301147461, "Text": "PE", "Geometry": {"BoundingBox": {"Width": 0.07020014524459839, "Height": 0.03418971225619316, "Left": 0.6797472834587097, "Top": 0.2667059600353241}, "Polygon": [{"X": 0.6797482967376709, "Y": 0.2667059600353241}, {"X": 0.7499474287033081, "Y": 0.2667158842086792}, {"X": 0.7499464154243469, "Y": 0.30089566111564636}, {"X": 0.6797472834587097, "Y": 0.30088573694229126}]}, "Id": "7b8ade66-9bff-42d8-b094-8f32f347c68b", "Relationships": [{"Type": "CHILD", "Ids": ["21616252-3ada-489b-bd51-24fc3ac10c62"]}]}, {"BlockType": "LINE", "Confidence": 99.85736846923828, "Text": "Date Processed 5/26/2023", "Geometry": {"BoundingBox": {"Width": 0.29935935139656067, "Height": 0.027756407856941223, "Left": 0.11656401306390762, "Top": 0.3263808786869049}, "Polygon": [{"X": 0.11656470596790314, "Y": 0.3263808786869049}, {"X": 0.4159233570098877, "Y": 0.32642319798469543}, {"X": 0.415922611951828, "Y": 0.3541373014450073}, {"X": 0.11656401306390762, "Y": 0.3540949821472168}]}, "Id": "8e8bce47-76d8-452a-996e-8822735ae70a", "Relationships": [{"Type": "CHILD", "Ids": ["b41c7fdb-ddbe-48ad-ab52-9e05146407af", "4f9dffbe-f5a7-45a7-88fc-746f13fa1c65", "5c072f16-b91b-49bd-b7d7-c6fc8b9ecc10"]}]}, {"BlockType": "LINE", "Confidence": 93.41092681884766, "Text": "Load Cost $1,811.50", "Geometry": {"BoundingBox": {"Width": 0.2618899941444397, "Height": 0.030339840799570084, "Left": 0.5735875964164734, "Top": 0.33014822006225586}, "Polygon": [{"X": 0.5735884308815002, "Y": 0.33014822006225586}, {"X": 0.8354775905609131, "Y": 0.33018526434898376}, {"X": 0.8354766368865967, "Y": 0.36048805713653564}, {"X": 0.5735875964164734, "Y": 0.3604510426521301}]}, "Id": "0dc48c0d-1604-49e3-b273-ad529af8ec4f", "Relationships": [{"Type": "CHILD", "Ids": ["f9b4ffc2-486c-4e43-84c4-96b5a9232b06", "3f23c66c-a540-4289-acb2-8d6235a67eb0", "b2faea47-94be-49e9-b26a-1cb8413fbb01"]}]}, {"BlockType": "LINE", "Confidence": 99.93463134765625, "Text": "<PERSON><PERSON> Returned 58", "Geometry": {"BoundingBox": {"Width": 0.22270430624485016, "Height": 0.019123701378703117, "Left": 0.12101997435092926, "Top": 0.36618831753730774}, "Polygon": [{"X": 0.12102044373750687, "Y": 0.36618831753730774}, {"X": 0.3437242805957794, "Y": 0.36621978878974915}, {"X": 0.34372377395629883, "Y": 0.385312020778656}, {"X": 0.12101997435092926, "Y": 0.3852805495262146}]}, "Id": "ae42eff8-9115-41d6-aa13-7a350e83a173", "Relationships": [{"Type": "CHILD", "Ids": ["5772da9c-b9fc-4105-9063-1650b73dd6b2", "83c856e4-2c4f-41e3-8a7d-2f8780051e3a", "1556873f-6942-45c0-a23f-39ddaadfc10d"]}]}, {"BlockType": "LINE", "Confidence": 98.70064544677734, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.12142102420330048, "Height": 0.015464779920876026, "Left": 0.5784105658531189, "Top": 0.38868266344070435}, "Polygon": [{"X": 0.5784109830856323, "Y": 0.38868266344070435}, {"X": 0.6998315453529358, "Y": 0.38869979977607727}, {"X": 0.6998311281204224, "Y": 0.4041474163532257}, {"X": 0.5784105658531189, "Y": 0.4041302800178528}]}, "Id": "a0d4274c-f47a-4eff-8cbf-21a2f6f047fc", "Relationships": [{"Type": "CHILD", "Ids": ["0dc5b584-f1f0-4b0f-9f97-8f5682293411", "d4f15b2e-3246-4224-af43-ff72b28f155d", "f581d16f-5338-478f-820d-8ae72fecaf80"]}]}, {"BlockType": "LINE", "Confidence": 82.40931701660156, "Text": "/", "Geometry": {"BoundingBox": {"Width": 0.03604384511709213, "Height": 0.007077441085129976, "Left": 0.739342212677002, "Top": 0.3872559368610382}, "Polygon": [{"X": 0.739342451095581, "Y": 0.3872559368610382}, {"X": 0.7753860354423523, "Y": 0.3872610330581665}, {"X": 0.775385856628418, "Y": 0.3943333923816681}, {"X": 0.739342212677002, "Y": 0.3943282961845398}]}, "Id": "d0d9d7cf-6275-4ff9-839d-1485a5ad0b5a", "Relationships": [{"Type": "CHILD", "Ids": ["b1996308-e14e-4981-9291-76e62a8efcd5"]}]}, {"BlockType": "LINE", "Confidence": 96.5987777709961, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.12572361528873444, "Height": 0.012501686811447144, "Left": 0.5792409181594849, "Top": 0.4233436584472656}, "Polygon": [{"X": 0.5792412757873535, "Y": 0.4233436584472656}, {"X": 0.7049645185470581, "Y": 0.4233614206314087}, {"X": 0.7049641609191895, "Y": 0.43584534525871277}, {"X": 0.5792409181594849, "Y": 0.4358276128768921}]}, "Id": "dc337419-e008-4d78-80aa-0463db45dc99", "Relationships": [{"Type": "CHILD", "Ids": ["1dcda735-64ea-428b-9dbf-30ff7ad7b29d", "de6ee098-ca88-4fbf-be95-d4f205276577"]}]}, {"BlockType": "LINE", "Confidence": 94.48660278320312, "Text": "\\", "Geometry": {"BoundingBox": {"Width": 0.029602937400341034, "Height": 0.008931504562497139, "Left": 0.7403873801231384, "Top": 0.4204845726490021}, "Polygon": [{"X": 0.7403876185417175, "Y": 0.4204845726490021}, {"X": 0.7699902653694153, "Y": 0.42048874497413635}, {"X": 0.7699900269508362, "Y": 0.42941606044769287}, {"X": 0.7403873801231384, "Y": 0.4294118881225586}]}, "Id": "b26bda6e-d652-4078-a32f-07e8442e7602", "Relationships": [{"Type": "CHILD", "Ids": ["754d21d7-40bb-4743-9504-e4416f357b7b"]}]}, {"BlockType": "LINE", "Confidence": 87.01582336425781, "Text": "58 XS 4.- = $232,-", "Geometry": {"BoundingBox": {"Width": 0.33475369215011597, "Height": 0.024369128048419952, "Left": 0.13731786608695984, "Top": 0.43149495124816895}, "Polygon": [{"X": 0.1373184770345688, "Y": 0.43149495124816895}, {"X": 0.4720715880393982, "Y": 0.43154218792915344}, {"X": 0.4720709025859833, "Y": 0.4558640718460083}, {"X": 0.13731786608695984, "Y": 0.4558168053627014}]}, "Id": "c8181695-904c-4158-8b55-067e510aa547", "Relationships": [{"Type": "CHILD", "Ids": ["523c774d-f4ac-433c-a042-581e488093cb", "0acfaff5-2ab6-4ef8-b3a0-7c0c601051d3", "f46e3390-9867-456a-be1a-0658b9740fb4", "b98d5ae9-2a5f-4417-83c6-c115694f0351", "3bc547bf-b389-404e-b18e-4f2a89b1e4cf"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "RACKS AMOUNT TOTAL", "Geometry": {"BoundingBox": {"Width": 0.3177521824836731, "Height": 0.011901987716555595, "Left": 0.11665182560682297, "Top": 0.45716094970703125}, "Polygon": [{"X": 0.11665212363004684, "Y": 0.45716094970703125}, {"X": 0.43440401554107666, "Y": 0.45720580220222473}, {"X": 0.4344036877155304, "Y": 0.4690629541873932}, {"X": 0.11665182560682297, "Y": 0.4690181016921997}]}, "Id": "70a00928-afe3-4ee3-86de-d9ca759821b2", "Relationships": [{"Type": "CHILD", "Ids": ["cf0d1c5e-d08e-424c-b186-a542294c508c", "edc24d98-4314-4707-b9e8-cc195470e3e2", "593ae6e2-54e1-4932-8392-161150efa975"]}]}, {"BlockType": "LINE", "Confidence": 74.80265808105469, "Text": "Racks + $232,-", "Geometry": {"BoundingBox": {"Width": 0.24389666318893433, "Height": 0.02032937854528427, "Left": 0.5825254917144775, "Top": 0.46969568729400635}, "Polygon": [{"X": 0.5825260877609253, "Y": 0.46969568729400635}, {"X": 0.8264221549034119, "Y": 0.46973010897636414}, {"X": 0.8264215588569641, "Y": 0.4900250732898712}, {"X": 0.5825254917144775, "Y": 0.4899906814098358}]}, "Id": "d7b486fa-4c6e-4acf-a8ae-8971b4de45f4", "Relationships": [{"Type": "CHILD", "Ids": ["136cb445-b527-404b-ab40-8464ed98f8fd", "79338fae-31b3-481a-8619-d0956e9fc25a", "c90240e5-1cc2-44f1-9ea5-18c272973ff6"]}]}, {"BlockType": "LINE", "Confidence": 99.92434692382812, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.29254835844039917, "Height": 0.014317430555820465, "Left": 0.5824187994003296, "Top": 0.4914499819278717}, "Polygon": [{"X": 0.5824191570281982, "Y": 0.4914499819278717}, {"X": 0.8749671578407288, "Y": 0.49149125814437866}, {"X": 0.8749666810035706, "Y": 0.5057674050331116}, {"X": 0.5824187994003296, "Y": 0.505726158618927}]}, "Id": "5c338012-675e-480a-9fd3-80a06e18cbf0", "Relationships": [{"Type": "CHILD", "Ids": ["094edfa0-7357-442a-b6db-56ed6fcf0c39", "e34b6fcd-5248-46de-88fb-fdd7931dc672", "f4d15d10-d638-4f15-8c1c-e243385e4894", "3cba6d23-e1d8-4f1c-863b-682bbbb9b6ac", "292f3f9f-bdb8-46f4-b492-723a7a0d71e9", "bd6af52e-5ca5-4d0f-a922-0baf7d4e63d4", "65136a3c-e1db-4add-8335-8e8e856cdb11", "5b726a48-1c75-4b9a-9d2b-1ed8bf7e0565"]}]}, {"BlockType": "LINE", "Confidence": 99.48262023925781, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06332949548959732, "Height": 0.01425098069012165, "Left": 0.5937391519546509, "Top": 0.5080398917198181}, "Polygon": [{"X": 0.5937395691871643, "Y": 0.5080398917198181}, {"X": 0.65706866979599, "Y": 0.5080488324165344}, {"X": 0.6570682525634766, "Y": 0.5222908854484558}, {"X": 0.5937391519546509, "Y": 0.5222819447517395}]}, "Id": "bdf348f9-8255-491a-a9cc-119cfe0131ec", "Relationships": [{"Type": "CHILD", "Ids": ["b6b8ad0e-0042-4958-ab0f-164eefa2db6a"]}]}, {"BlockType": "LINE", "Confidence": 97.79057312011719, "Text": "Net Cost $2,043.50", "Geometry": {"BoundingBox": {"Width": 0.25439634919166565, "Height": 0.026055634021759033, "Left": 0.5835145115852356, "Top": 0.5375574231147766}, "Polygon": [{"X": 0.5835152268409729, "Y": 0.5375574231147766}, {"X": 0.8379108905792236, "Y": 0.5375933051109314}, {"X": 0.8379100561141968, "Y": 0.5636130571365356}, {"X": 0.5835145115852356, "Y": 0.5635772347450256}]}, "Id": "924b5630-c52f-44bf-8bc8-7985a0c63f81", "Relationships": [{"Type": "CHILD", "Ids": ["42002a43-d1ce-4a9d-8800-1ef7138f2d6f", "f3aba980-9eaf-498a-9965-d99d853449e1", "0ceb549b-3ff3-440a-8ed0-bb0dff3beae5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.2506729066371918, "Height": 0.022772829979658127, "Left": 0.11781423538923264, "Top": 0.5796347856521606}, "Polygon": [{"X": 0.1178147941827774, "Y": 0.5796347856521606}, {"X": 0.3684871196746826, "Y": 0.5796701312065125}, {"X": 0.36848652362823486, "Y": 0.6024076342582703}, {"X": 0.11781423538923264, "Y": 0.6023723483085632}]}, "Id": "1ad0b579-0294-4a41-833e-130b8c230525", "Relationships": [{"Type": "CHILD", "Ids": ["0cbdf86c-7311-41a7-bf62-ddd1debe5447", "6ebcb563-a13f-44dd-8414-c2700bcefdc7"]}]}, {"BlockType": "LINE", "Confidence": 87.95926666259766, "Text": "requipment missing with insufficient documentation", "Geometry": {"BoundingBox": {"Width": 0.2841707170009613, "Height": 0.011054752394557, "Left": 0.11641670018434525, "Top": 0.603716254234314}, "Polygon": [{"X": 0.11641696840524673, "Y": 0.603716254234314}, {"X": 0.40058740973472595, "Y": 0.6037562489509583}, {"X": 0.4005871117115021, "Y": 0.6147710084915161}, {"X": 0.11641670018434525, "Y": 0.6147310137748718}]}, "Id": "37b6c972-79c0-492c-a971-6a3e93da09c5", "Relationships": [{"Type": "CHILD", "Ids": ["34599584-fdfe-443b-97d7-739fac33baf0", "60af053a-79d4-4415-b9fa-ab7df62910e3", "96a7f19b-8366-4e4c-91a7-55f39c000a5f", "67025f2d-170a-41f5-a3c4-1c83f276c184", "e77bac3c-28bc-40d2-b942-aafed069c388"]}]}, {"BlockType": "LINE", "Confidence": 94.10102844238281, "Text": "Racks: ( ) X $250.00 $", "Geometry": {"BoundingBox": {"Width": 0.202506422996521, "Height": 0.0137887978926301, "Left": 0.1173315942287445, "Top": 0.6283055543899536}, "Polygon": [{"X": 0.11733193695545197, "Y": 0.6283055543899536}, {"X": 0.3198380172252655, "Y": 0.628334105014801}, {"X": 0.31983765959739685, "Y": 0.6420943737030029}, {"X": 0.1173315942287445, "Y": 0.6420658826828003}]}, "Id": "aac347b6-2d37-445a-98ad-01adef2874db", "Relationships": [{"Type": "CHILD", "Ids": ["923e3dce-3237-43c3-be82-29d0e11bf384", "649bdb12-fbc2-4273-b6d6-f1eeaa94fc4f", "387b822c-7aad-4675-b211-80d71571ec96", "3892136e-c739-485a-9656-5537ae739017", "2f36083d-1f16-4fda-bbbc-9b734a19866e", "1a56cbed-3d03-4b01-864a-a7b55927e207"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.1384696364402771, "Height": 0.011604785919189453, "Left": 0.6024941802024841, "Top": 0.6283608078956604}, "Polygon": [{"X": 0.602494478225708, "Y": 0.6283608078956604}, {"X": 0.7409638166427612, "Y": 0.628380298614502}, {"X": 0.7409634590148926, "Y": 0.6399655938148499}, {"X": 0.6024941802024841, "Y": 0.6399461030960083}]}, "Id": "b897b463-d0b7-493e-80e1-d1ce56c52ef4", "Relationships": [{"Type": "CHILD", "Ids": ["836b877c-65d1-4812-9c0e-f67437d8cefb", "d6935f07-091d-4a20-ab9d-3cb106cf8c73"]}]}, {"BlockType": "LINE", "Confidence": 99.73114776611328, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07765011489391327, "Height": 0.011426055803894997, "Left": 0.6047047972679138, "Top": 0.6456210613250732}, "Polygon": [{"X": 0.6047050952911377, "Y": 0.6456210613250732}, {"X": 0.6823548674583435, "Y": 0.6456320285797119}, {"X": 0.6823545694351196, "Y": 0.6570471525192261}, {"X": 0.6047047972679138, "Y": 0.6570362448692322}]}, "Id": "9dd832e7-501e-4f9a-9bd9-db1df3ad66c3", "Relationships": [{"Type": "CHILD", "Ids": ["046c5146-448c-42df-a66c-eddb242a4519", "10e56817-6cef-42b3-950e-2cf4bb831f0d"]}]}, {"BlockType": "LINE", "Confidence": 88.97608947753906, "Text": "Load Locks: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2385191172361374, "Height": 0.014859232120215893, "Left": 0.117476686835289, "Top": 0.6616898775100708}, "Polygon": [{"X": 0.11747705191373825, "Y": 0.6616898775100708}, {"X": 0.3559958040714264, "Y": 0.6617234349250793}, {"X": 0.35599541664123535, "Y": 0.6765490770339966}, {"X": 0.117476686835289, "Y": 0.676515519618988}]}, "Id": "84f5fee0-8614-4194-b9c7-8824d2<PERSON>baa4", "Relationships": [{"Type": "CHILD", "Ids": ["d1cfe41d-af0f-4228-ab46-6e001425c4aa", "74ce0460-922d-49f7-a2d6-27a9f19ea3a5", "8b1ea7f7-b13e-48c7-8255-28e1b479180c", "86bbbf82-e80e-4c9a-9c40-d080d1f1171f", "ced5cfed-3d8b-41ce-85cc-b6fc9929502a", "2d5fba5a-8f64-46a3-8d49-0aff46db90b1", "c6df785d-5a27-455a-8cf3-5059573f546c"]}]}, {"BlockType": "LINE", "Confidence": 95.77567291259766, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.010098937898874283, "Height": 0.01212747860699892, "Left": 0.603948712348938, "Top": 0.6791166067123413}, "Polygon": [{"X": 0.6039490699768066, "Y": 0.6791166067123413}, {"X": 0.614047646522522, "Y": 0.6791180372238159}, {"X": 0.6140472888946533, "Y": 0.6912440657615662}, {"X": 0.603948712348938, "Y": 0.6912426352500916}]}, "Id": "35376dfc-6799-468d-805e-d8b0ef396b78", "Relationships": [{"Type": "CHILD", "Ids": ["3003bc3b-d9a2-4b98-8b25-b454d0a0ab04"]}]}, {"BlockType": "LINE", "Confidence": 91.79894256591797, "Text": "Pallet Jack: ( ) X $500.00 - $", "Geometry": {"BoundingBox": {"Width": 0.23895935714244843, "Height": 0.013947353698313236, "Left": 0.1181296557188034, "Top": 0.6963074207305908}, "Polygon": [{"X": 0.11812999844551086, "Y": 0.6963074207305908}, {"X": 0.35708901286125183, "Y": 0.6963410377502441}, {"X": 0.3570886552333832, "Y": 0.7102547883987427}, {"X": 0.1181296557188034, "Y": 0.7102211713790894}]}, "Id": "395b1d12-206f-4315-bd5d-c8c6140d5926", "Relationships": [{"Type": "CHILD", "Ids": ["4226da2b-f384-4cd3-b60f-bc58886ce0d0", "c5b615bc-0b9b-4253-82a2-064a8ff6d781", "b6c40a86-493a-4f25-a2a7-3c1430edec21", "460904c5-2383-44b6-91f9-8d93d90d5471", "0d5ecb49-8ace-4d21-b006-84a95f4f255d", "45ede67b-c142-425c-8bd1-fcfdef1d934c", "4dff074a-1d68-4db2-80a0-38e9ca67c2db", "64fb9f09-66fc-453d-a5a8-069c5d3ffdd0"]}]}, {"BlockType": "LINE", "Confidence": 91.82164001464844, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2388901710510254, "Height": 0.015205707401037216, "Left": 0.11816539615392685, "Top": 0.7303385138511658}, "Polygon": [{"X": 0.1181657686829567, "Y": 0.7303385138511658}, {"X": 0.35705557465553284, "Y": 0.7303721308708191}, {"X": 0.3570551574230194, "Y": 0.7455442547798157}, {"X": 0.11816539615392685, "Y": 0.7455106377601624}]}, "Id": "fa97d06f-461b-4da1-bb3c-6d757f517f9e", "Relationships": [{"Type": "CHILD", "Ids": ["5613ff14-0272-48e7-aabb-45a2f1d5d7fa", "4510639f-072c-4932-b11c-afe7c1ff0474", "206cd09a-5ad8-4b30-99cb-3d4214701ad2", "7ff0fcac-0d34-4135-b73e-2e5ae7240df0", "ca11c68f-b36a-45ef-bb1f-afa97841742d", "86ea18b1-b53a-402a-b525-c5f0bdc0f16e", "4f484646-14f5-4d08-af05-992687573032", "d6df5984-9bf2-4d9d-852e-af577a0bac37"]}]}, {"BlockType": "LINE", "Confidence": 99.73851776123047, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21507374942302704, "Height": 0.014641941525042057, "Left": 0.11782500147819519, "Top": 0.7648339867591858}, "Polygon": [{"X": 0.11782536655664444, "Y": 0.7648339867591858}, {"X": 0.33289873600006104, "Y": 0.7648642063140869}, {"X": 0.33289834856987, "Y": 0.7794759273529053}, {"X": 0.11782500147819519, "Y": 0.7794457077980042}]}, "Id": "a2479160-b644-4ef8-bd49-1a7cbea82833", "Relationships": [{"Type": "CHILD", "Ids": ["4868b51f-5b42-4280-9c93-54d4b07c51a1", "ed3807da-bcf5-4d5a-b944-9452a1c1e2cf", "65a973b1-3da1-412a-bfa8-c303907fa10c", "3f677c7e-17d8-48d5-be16-1d484d736801", "d49c983b-d951-4218-a052-52569836e4b8"]}]}, {"BlockType": "LINE", "Confidence": 99.05366516113281, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.23336835205554962, "Height": 0.014204194769263268, "Left": 0.1175626888871193, "Top": 0.782246470451355}, "Polygon": [{"X": 0.11756303906440735, "Y": 0.782246470451355}, {"X": 0.3509310483932495, "Y": 0.7822792530059814}, {"X": 0.35093066096305847, "Y": 0.796450674533844}, {"X": 0.1175626888871193, "Y": 0.7964178919792175}]}, "Id": "3378956a-1eaf-49f5-96b2-21a983e61308", "Relationships": [{"Type": "CHILD", "Ids": ["a25bec4a-8d16-492b-84c5-e1a566427413", "54d546bc-1f7f-4ded-8866-d5885b077066", "195abca7-8f60-4b29-9370-5b5781969574"]}]}, {"BlockType": "LINE", "Confidence": 99.62133026123047, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.15500731766223907, "Height": 0.014326130039989948, "Left": 0.5494952201843262, "Top": 0.7830010652542114}, "Polygon": [{"X": 0.5494956374168396, "Y": 0.7830010652542114}, {"X": 0.704502522945404, "Y": 0.7830228805541992}, {"X": 0.7045021057128906, "Y": 0.7973272204399109}, {"X": 0.5494952201843262, "Y": 0.7973054647445679}]}, "Id": "c37f12d5-5b13-426a-8dcf-40fc11516881", "Relationships": [{"Type": "CHILD", "Ids": ["7043cd5b-9251-4c66-b22c-b6ca1a18c669", "e7a38df1-0471-486d-aa6c-5a9ce711fccf"]}]}, {"BlockType": "LINE", "Confidence": 99.48181915283203, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.05159206688404083, "Height": 0.012029643170535564, "Left": 0.11761604994535446, "Top": 0.8164384365081787}, "Polygon": [{"X": 0.11761634796857834, "Y": 0.8164384365081787}, {"X": 0.1692081242799759, "Y": 0.8164456486701965}, {"X": 0.16920781135559082, "Y": 0.8284680843353271}, {"X": 0.11761604994535446, "Y": 0.8284608125686646}]}, "Id": "310d13ba-00f3-44d8-8eea-63cf33394e02", "Relationships": [{"Type": "CHILD", "Ids": ["2c713da8-5a96-4c7a-bf07-1ad95a6bfeeb"]}]}, {"BlockType": "LINE", "Confidence": 97.44171142578125, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468", "Geometry": {"BoundingBox": {"Width": 0.7673737406730652, "Height": 0.013018960133194923, "Left": 0.11811070144176483, "Top": 0.8672084212303162}, "Polygon": [{"X": 0.1181110218167305, "Y": 0.8672084212303162}, {"X": 0.8854844570159912, "Y": 0.8673160672187805}, {"X": 0.8854840993881226, "Y": 0.8802273869514465}, {"X": 0.11811070144176483, "Y": 0.8801197409629822}]}, "Id": "46ddc789-b4f3-4600-8501-451c46b10fc5", "Relationships": [{"Type": "CHILD", "Ids": ["afec50b9-73ee-49ea-ab1e-d2bd23a5e5f2", "ecea61b9-a336-4b47-ac5a-30832400bd41", "f399b909-bf5a-4fc4-ae29-91f307f365fb", "7335db53-6564-40c2-98fb-28d826ca3af3", "881fb6ae-ad42-4a26-a2c2-51ee270308f4", "0d814767-c93c-4d1a-9af7-bb4f32a99eff", "2ce3225e-72cf-49bd-b8c7-404b2335481d", "8e3e317c-9444-4906-abf0-bae1035d2f16", "9ae77bb1-0449-4c9f-ac55-8b8717a2f44e", "9bf3f372-ff37-418f-8aba-924303bac070", "07a15a6e-8cbc-405c-a0d2-6e29a0db363b", "7d5d46ee-3063-43ab-9879-f3f78bf87f45", "e0a5fdb6-99d6-477c-aa3a-2b791ff22adb", "8fb868be-7871-4623-88a6-8562e7fbf84b", "5f11df04-2b47-4378-8cc9-6119e0625c66", "005071ff-90a1-4f5d-9e5d-719038037810", "8469beb2-5645-4a48-b184-f99eff14fd7e", "111bc13f-938e-42a9-9d13-582cb28a1f60", "d6d6e431-dea6-4337-affc-989c0921fbdb", "f8f9854f-8f9a-44ee-976e-78959156be71"]}]}, {"BlockType": "WORD", "Confidence": 92.23353576660156, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10540323704481125, "Height": 0.04917736351490021, "Left": 0.5896637439727783, "Top": 0.07072446495294571}, "Polygon": [{"X": 0.5896651148796082, "Y": 0.07072446495294571}, {"X": 0.6950669884681702, "Y": 0.07073942571878433}, {"X": 0.6950655579566956, "Y": 0.11990182846784592}, {"X": 0.5896637439727783, "Y": 0.11988688260316849}]}, "Id": "98662dbe-efb5-4458-834f-2d8bab66f734"}, {"BlockType": "WORD", "Confidence": 97.52531433105469, "Text": "7056563", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.19257330894470215, "Height": 0.060884229838848114, "Left": 0.7023028135299683, "Top": 0.07608922570943832}, "Polygon": [{"X": 0.7023046016693115, "Y": 0.07608922570943832}, {"X": 0.8948761224746704, "Y": 0.07611655443906784}, {"X": 0.8948742151260376, "Y": 0.13697345554828644}, {"X": 0.7023028135299683, "Y": 0.1369461566209793}]}, "Id": "b0fc2bd7-0c0b-4e32-9bc0-bbedd2cfff28"}, {"BlockType": "WORD", "Confidence": 20.76201820373535, "Text": "AMBVD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22545401751995087, "Height": 0.03536486625671387, "Left": 0.4016489088535309, "Top": 0.1331961750984192}, "Polygon": [{"X": 0.4016498625278473, "Y": 0.1331961750984192}, {"X": 0.6271029114723206, "Y": 0.13322813808918}, {"X": 0.6271018981933594, "Y": 0.16856104135513306}, {"X": 0.4016489088535309, "Y": 0.16852909326553345}]}, "Id": "3f243273-e358-4cdc-ae3c-f600b4523ac4"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18591628968715668, "Height": 0.023441141471266747, "Left": 0.300431489944458, "Top": 0.18134063482284546}, "Polygon": [{"X": 0.30043211579322815, "Y": 0.18134063482284546}, {"X": 0.4863477945327759, "Y": 0.18136698007583618}, {"X": 0.48634713888168335, "Y": 0.20478177070617676}, {"X": 0.300431489944458, "Y": 0.20475544035434723}]}, "Id": "9b85ace4-862c-4f96-a9fd-42c700d6cfb8"}, {"BlockType": "WORD", "Confidence": 93.95188903808594, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2246761918067932, "Height": 0.02256779558956623, "Left": 0.5078124403953552, "Top": 0.18164871633052826}, "Polygon": [{"X": 0.5078130960464478, "Y": 0.18164871633052826}, {"X": 0.7324886322021484, "Y": 0.18168054521083832}, {"X": 0.7324879765510559, "Y": 0.20421651005744934}, {"X": 0.5078124403953552, "Y": 0.20418468117713928}]}, "Id": "3e56585e-0c2d-46a2-b5db-820e40ff3cfa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13653694093227386, "Height": 0.01950434409081936, "Left": 0.3518599271774292, "Top": 0.2139761745929718}, "Polygon": [{"X": 0.3518604636192322, "Y": 0.2139761745929718}, {"X": 0.48839688301086426, "Y": 0.21399550139904022}, {"X": 0.4883963465690613, "Y": 0.2334805130958557}, {"X": 0.3518599271774292, "Y": 0.2334611862897873}]}, "Id": "ac1bff74-a93e-4d12-8fea-6834d0f1e601"}, {"BlockType": "WORD", "Confidence": 99.67613983154297, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1211833506822586, "Height": 0.019593311473727226, "Left": 0.4968012273311615, "Top": 0.21411915123462677}, "Polygon": [{"X": 0.4968017637729645, "Y": 0.21411915123462677}, {"X": 0.6179845929145813, "Y": 0.21413631737232208}, {"X": 0.6179839968681335, "Y": 0.23371246457099915}, {"X": 0.4968012273311615, "Y": 0.23369529843330383}]}, "Id": "45169032-4284-467c-91ef-76987c0e23bc"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07053661346435547, "Height": 0.018371663987636566, "Left": 0.6264668107032776, "Top": 0.21539254486560822}, "Polygon": [{"X": 0.6264673471450806, "Y": 0.21539254486560822}, {"X": 0.6970034241676331, "Y": 0.2154025435447693}, {"X": 0.6970028877258301, "Y": 0.23376421630382538}, {"X": 0.6264668107032776, "Y": 0.2337542325258255}]}, "Id": "1863cf46-0fa7-4235-8574-fd00c24828e0"}, {"BlockType": "WORD", "Confidence": 99.29528045654297, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1697853058576584, "Height": 0.023801911622285843, "Left": 0.37930452823638916, "Top": 0.24309197068214417}, "Polygon": [{"X": 0.3793051540851593, "Y": 0.24309197068214417}, {"X": 0.5490898489952087, "Y": 0.24311600625514984}, {"X": 0.5490891337394714, "Y": 0.2668938636779785}, {"X": 0.37930452823638916, "Y": 0.26686984300613403}]}, "Id": "c02cd5e0-68b7-4d3f-a98d-34c60f19ab4b"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11440407484769821, "Height": 0.018674347549676895, "Left": 0.5572763085365295, "Top": 0.24373221397399902}, "Polygon": [{"X": 0.5572768449783325, "Y": 0.24373221397399902}, {"X": 0.6716803908348083, "Y": 0.24374841153621674}, {"X": 0.6716798543930054, "Y": 0.2624065577983856}, {"X": 0.5572763085365295, "Y": 0.2623903751373291}]}, "Id": "7d2dd2f0-be44-4a82-be81-6f54ec6b52e5"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040198516100645065, "Height": 0.011720404028892517, "Left": 0.11647132784128189, "Top": 0.2858521640300751}, "Polygon": [{"X": 0.11647161841392517, "Y": 0.2858521640300751}, {"X": 0.15666984021663666, "Y": 0.2858578562736511}, {"X": 0.15666955709457397, "Y": 0.2975725829601288}, {"X": 0.11647132784128189, "Y": 0.29756689071655273}]}, "Id": "af5d20a4-e853-40d3-9d39-75feb3299b64"}, {"BlockType": "WORD", "Confidence": 95.14568328857422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010951509699225426, "Height": 0.011172923259437084, "Left": 0.1608695089817047, "Top": 0.2865135967731476}, "Polygon": [{"X": 0.1608697921037674, "Y": 0.2865135967731476}, {"X": 0.17182102799415588, "Y": 0.28651514649391174}, {"X": 0.1718207448720932, "Y": 0.29768651723861694}, {"X": 0.1608695089817047, "Y": 0.2976849675178528}]}, "Id": "4c5fe3fb-e7a4-45fd-8c35-db2424ff6a44"}, {"BlockType": "WORD", "Confidence": 99.33773040771484, "Text": "20081", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09039538353681564, "Height": 0.021692262962460518, "Left": 0.17203138768672943, "Top": 0.2814519703388214}, "Polygon": [{"X": 0.1720319390296936, "Y": 0.2814519703388214}, {"X": 0.2624267637729645, "Y": 0.28146475553512573}, {"X": 0.2624261975288391, "Y": 0.3031442165374756}, {"X": 0.17203138768672943, "Y": 0.30313143134117126}]}, "Id": "238c98af-61d7-4a5a-8abd-e1c0593cc615"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05547386035323143, "Height": 0.011601464822888374, "Left": 0.5692568421363831, "Top": 0.286699503660202}, "Polygon": [{"X": 0.5692571401596069, "Y": 0.286699503660202}, {"X": 0.6247307062149048, "Y": 0.2867073714733124}, {"X": 0.6247303485870361, "Y": 0.29830098152160645}, {"X": 0.5692568421363831, "Y": 0.2982931435108185}]}, "Id": "6850cce4-4fa4-4afa-8aa7-b6e5caa8a68d"}, {"BlockType": "WORD", "Confidence": 98.9674301147461, "Text": "PE", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07020014524459839, "Height": 0.03418971225619316, "Left": 0.6797472834587097, "Top": 0.2667059600353241}, "Polygon": [{"X": 0.6797482967376709, "Y": 0.2667059600353241}, {"X": 0.7499474287033081, "Y": 0.2667158842086792}, {"X": 0.7499464154243469, "Y": 0.30089566111564636}, {"X": 0.6797472834587097, "Y": 0.30088573694229126}]}, "Id": "21616252-3ada-489b-bd51-24fc3ac10c62"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03751755878329277, "Height": 0.011081227101385593, "Left": 0.11656415462493896, "Top": 0.33742454648017883}, "Polygon": [{"X": 0.11656443029642105, "Y": 0.33742454648017883}, {"X": 0.15408171713352203, "Y": 0.33742985129356384}, {"X": 0.15408143401145935, "Y": 0.34850576519966125}, {"X": 0.11656415462493896, "Y": 0.34850046038627625}]}, "Id": "b41c7fdb-ddbe-48ad-ab52-9e05146407af"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07984647899866104, "Height": 0.01142625417560339, "Left": 0.15821559727191925, "Top": 0.3372431993484497}, "Polygon": [{"X": 0.15821588039398193, "Y": 0.3372431993484497}, {"X": 0.2380620837211609, "Y": 0.33725449442863464}, {"X": 0.238061785697937, "Y": 0.3486694395542145}, {"X": 0.15821559727191925, "Y": 0.34865817427635193}]}, "Id": "4f9dffbe-f5a7-45a7-88fc-746f13fa1c65"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "5/26/2023", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16433002054691315, "Height": 0.027737313881516457, "Left": 0.25159332156181335, "Top": 0.3263999819755554}, "Polygon": [{"X": 0.25159403681755066, "Y": 0.3263999819755554}, {"X": 0.4159233570098877, "Y": 0.32642319798469543}, {"X": 0.415922611951828, "Y": 0.3541373014450073}, {"X": 0.25159332156181335, "Y": 0.3541140556335449}]}, "Id": "5c072f16-b91b-49bd-b7d7-c6fc8b9ecc10"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04009363427758217, "Height": 0.01228137407451868, "Left": 0.5735878944396973, "Top": 0.33741599321365356}, "Polygon": [{"X": 0.5735882520675659, "Y": 0.33741599321365356}, {"X": 0.6136815547943115, "Y": 0.3374216556549072}, {"X": 0.6136811971664429, "Y": 0.3496973514556885}, {"X": 0.5735878944396973, "Y": 0.3496916890144348}]}, "Id": "f9b4ffc2-486c-4e43-84c4-96b5a9232b06"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036674950271844864, "Height": 0.011521099135279655, "Left": 0.6189399361610413, "Top": 0.33795201778411865}, "Polygon": [{"X": 0.6189402937889099, "Y": 0.33795201778411865}, {"X": 0.6556149125099182, "Y": 0.3379572033882141}, {"X": 0.6556145548820496, "Y": 0.34947311878204346}, {"X": 0.6189399361610413, "Y": 0.349467933177948}]}, "Id": "3f23c66c-a540-4289-acb2-8d6235a67eb0"}, {"BlockType": "WORD", "Confidence": 80.361328125, "Text": "$1,811.50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15128836035728455, "Height": 0.03032420203089714, "Left": 0.6841892004013062, "Top": 0.3301638662815094}, "Polygon": [{"X": 0.6841900944709778, "Y": 0.3301638662815094}, {"X": 0.8354775905609131, "Y": 0.33018526434898376}, {"X": 0.8354766368865967, "Y": 0.36048805713653564}, {"X": 0.6841892004013062, "Y": 0.36046668887138367}]}, "Id": "b2faea47-94be-49e9-b26a-1cb8413fbb01"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048991139978170395, "Height": 0.012163417413830757, "Left": 0.12102002650499344, "Top": 0.3709349036216736}, "Polygon": [{"X": 0.12102033197879791, "Y": 0.3709349036216736}, {"X": 0.17001116275787354, "Y": 0.37094181776046753}, {"X": 0.17001086473464966, "Y": 0.383098304271698}, {"X": 0.12102002650499344, "Y": 0.38309139013290405}]}, "Id": "5772da9c-b9fc-4105-9063-1650b73dd6b2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07255759090185165, "Height": 0.012376274913549423, "Left": 0.17482176423072815, "Top": 0.37095099687576294}, "Polygon": [{"X": 0.17482207715511322, "Y": 0.37095099687576294}, {"X": 0.2473793625831604, "Y": 0.3709612488746643}, {"X": 0.24737903475761414, "Y": 0.38332727551460266}, {"X": 0.17482176423072815, "Y": 0.3833170235157013}]}, "Id": "83c856e4-2c4f-41e3-8a7d-2f8780051e3a"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "58", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05173654481768608, "Height": 0.019099539145827293, "Left": 0.29198774695396423, "Top": 0.36621248722076416}, "Polygon": [{"X": 0.29198822379112244, "Y": 0.36621248722076416}, {"X": 0.3437242805957794, "Y": 0.36621978878974915}, {"X": 0.34372377395629883, "Y": 0.385312020778656}, {"X": 0.29198774695396423, "Y": 0.385304719209671}]}, "Id": "1556873f-6942-45c0-a23f-39ddaadfc10d"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08112683147192001, "Height": 0.015305086970329285, "Left": 0.5784105658531189, "Top": 0.38883665204048157}, "Polygon": [{"X": 0.5784109830856323, "Y": 0.38883665204048157}, {"X": 0.6595373749732971, "Y": 0.38884812593460083}, {"X": 0.6595369577407837, "Y": 0.40414172410964966}, {"X": 0.5784105658531189, "Y": 0.4041302800178528}]}, "Id": "0dc5b584-f1f0-4b0f-9f97-8f5682293411"}, {"BlockType": "WORD", "Confidence": 96.56409454345703, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010465393774211407, "Height": 0.008246120996773243, "Left": 0.6688497066497803, "Top": 0.3911568522453308}, "Polygon": [{"X": 0.6688500046730042, "Y": 0.3911568522453308}, {"X": 0.6793151497840881, "Y": 0.3911583125591278}, {"X": 0.679314911365509, "Y": 0.3994029760360718}, {"X": 0.6688497066497803, "Y": 0.3994014859199524}]}, "Id": "d4f15b2e-3246-4224-af43-ff72b28f155d"}, {"BlockType": "WORD", "Confidence": 99.68669891357422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010881456546485424, "Height": 0.013133440166711807, "Left": 0.6889501214027405, "Top": 0.3886982798576355}, "Polygon": [{"X": 0.6889504790306091, "Y": 0.3886982798576355}, {"X": 0.6998315453529358, "Y": 0.38869979977607727}, {"X": 0.6998311877250671, "Y": 0.401831716299057}, {"X": 0.6889501214027405, "Y": 0.40183016657829285}]}, "Id": "f581d16f-5338-478f-820d-8ae72fecaf80"}, {"BlockType": "WORD", "Confidence": 82.40931701660156, "Text": "/", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03604384511709213, "Height": 0.007077441085129976, "Left": 0.739342212677002, "Top": 0.3872559368610382}, "Polygon": [{"X": 0.7753860354423523, "Y": 0.3872610330581665}, {"X": 0.775385856628418, "Y": 0.3943333923816681}, {"X": 0.739342212677002, "Y": 0.3943282961845398}, {"X": 0.739342451095581, "Y": 0.3872559368610382}]}, "Id": "b1996308-e14e-4981-9291-76e62a8efcd5"}, {"BlockType": "WORD", "Confidence": 96.18403625488281, "Text": "Deductions -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10682931542396545, "Height": 0.012188736349344254, "Left": 0.5792409181594849, "Top": 0.42345160245895386}, "Polygon": [{"X": 0.5792412757873535, "Y": 0.42345160245895386}, {"X": 0.6860702037811279, "Y": 0.42346668243408203}, {"X": 0.6860698461532593, "Y": 0.4356403350830078}, {"X": 0.5792409181594849, "Y": 0.43562525510787964}]}, "Id": "1dcda735-64ea-428b-9dbf-30ff7ad7b29d"}, {"BlockType": "WORD", "Confidence": 97.01351165771484, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010185823775827885, "Height": 0.01248537190258503, "Left": 0.6947786808013916, "Top": 0.4233599901199341}, "Polygon": [{"X": 0.6947790384292603, "Y": 0.4233599901199341}, {"X": 0.7049645185470581, "Y": 0.4233614206314087}, {"X": 0.7049641609191895, "Y": 0.43584534525871277}, {"X": 0.6947786808013916, "Y": 0.43584391474723816}]}, "Id": "de6ee098-ca88-4fbf-be95-d4f205276577"}, {"BlockType": "WORD", "Confidence": 94.48660278320312, "Text": "\\", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029602937400341034, "Height": 0.008931504562497139, "Left": 0.7403873801231384, "Top": 0.4204845726490021}, "Polygon": [{"X": 0.7699902653694153, "Y": 0.42048874497413635}, {"X": 0.7699900269508362, "Y": 0.42941606044769287}, {"X": 0.7403873801231384, "Y": 0.4294118881225586}, {"X": 0.7403876185417175, "Y": 0.4204845726490021}]}, "Id": "754d21d7-40bb-4743-9504-e4416f357b7b"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "58", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0425306037068367, "Height": 0.021793918684124947, "Left": 0.13731786608695984, "Top": 0.43402889370918274}, "Polygon": [{"X": 0.137318417429924, "Y": 0.43402889370918274}, {"X": 0.17984847724437714, "Y": 0.43403491377830505}, {"X": 0.17984792590141296, "Y": 0.45582282543182373}, {"X": 0.13731786608695984, "Y": 0.4558168053627014}]}, "Id": "523c774d-f4ac-433c-a042-581e488093cb"}, {"BlockType": "WORD", "Confidence": 91.98819732666016, "Text": "XS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02960963547229767, "Height": 0.012119299732148647, "Left": 0.20099274814128876, "Top": 0.4401569366455078}, "Polygon": [{"X": 0.20099306106567383, "Y": 0.4401569366455078}, {"X": 0.23060238361358643, "Y": 0.4401611089706421}, {"X": 0.23060207068920135, "Y": 0.45227622985839844}, {"X": 0.20099274814128876, "Y": 0.45227205753326416}]}, "Id": "0acfaff5-2ab6-4ef8-b3a0-7c0c601051d3"}, {"BlockType": "WORD", "Confidence": 77.44239807128906, "Text": "4.-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05556721240282059, "Height": 0.022199831902980804, "Left": 0.24093540012836456, "Top": 0.4315095543861389}, "Polygon": [{"X": 0.24093596637248993, "Y": 0.4315095543861389}, {"X": 0.29650261998176575, "Y": 0.43151742219924927}, {"X": 0.296502023935318, "Y": 0.4537093937397003}, {"X": 0.24093540012836456, "Y": 0.45370155572891235}]}, "Id": "f46e3390-9867-456a-be1a-0658b9740fb4"}, {"BlockType": "WORD", "Confidence": 95.53472137451172, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011496406979858875, "Height": 0.00488631846383214, "Left": 0.3447898030281067, "Top": 0.4437050521373749}, "Polygon": [{"X": 0.34478992223739624, "Y": 0.4437050521373749}, {"X": 0.3562861979007721, "Y": 0.4437066614627838}, {"X": 0.35628607869148254, "Y": 0.4485913813114166}, {"X": 0.3447898030281067, "Y": 0.4485897421836853}]}, "Id": "b98d5ae9-2a5f-4417-83c6-c115694f0351"}, {"BlockType": "WORD", "Confidence": 70.3896255493164, "Text": "$232,-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11089339852333069, "Height": 0.020582130178809166, "Left": 0.36117810010910034, "Top": 0.43400660157203674}, "Polygon": [{"X": 0.3611786663532257, "Y": 0.43400660157203674}, {"X": 0.47207149863243103, "Y": 0.43402227759361267}, {"X": 0.47207093238830566, "Y": 0.45458874106407166}, {"X": 0.36117810010910034, "Y": 0.4545730948448181}]}, "Id": "3bc547bf-b389-404e-b18e-4f2a89b1e4cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0659169927239418, "Height": 0.011822321452200413, "Left": 0.11665183305740356, "Top": 0.45716094970703125}, "Polygon": [{"X": 0.11665212363004684, "Y": 0.45716094970703125}, {"X": 0.18256881833076477, "Y": 0.4571702480316162}, {"X": 0.1825685203075409, "Y": 0.4689832925796509}, {"X": 0.11665183305740356, "Y": 0.46897396445274353}]}, "Id": "cf0d1c5e-d08e-424c-b186-a542294c508c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08708254992961884, "Height": 0.01104178186506033, "Left": 0.23298074305057526, "Top": 0.4578242897987366}, "Polygon": [{"X": 0.23298102617263794, "Y": 0.4578242897987366}, {"X": 0.3200632929801941, "Y": 0.4578365683555603}, {"X": 0.3200629949569702, "Y": 0.4688660502433777}, {"X": 0.23298074305057526, "Y": 0.46885377168655396}]}, "Id": "edc24d98-4314-4707-b9e8-cc195470e3e2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06492549180984497, "Height": 0.011266661807894707, "Left": 0.3694784939289093, "Top": 0.45779627561569214}, "Polygon": [{"X": 0.3694787919521332, "Y": 0.45779627561569214}, {"X": 0.4344039857387543, "Y": 0.45780545473098755}, {"X": 0.4344036877155304, "Y": 0.4690629541873932}, {"X": 0.3694784939289093, "Y": 0.4690537750720978}]}, "Id": "593ae6e2-54e1-4932-8392-161150efa975"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04903475567698479, "Height": 0.011652568355202675, "Left": 0.5825256109237671, "Top": 0.4748230576515198}, "Polygon": [{"X": 0.582525908946991, "Y": 0.4748230576515198}, {"X": 0.6315603256225586, "Y": 0.4748299717903137}, {"X": 0.6315600275993347, "Y": 0.4864756166934967}, {"X": 0.5825256109237671, "Y": 0.48646870255470276}]}, "Id": "136cb445-b527-404b-ab40-8464ed98f8fd"}, {"BlockType": "WORD", "Confidence": 86.97195434570312, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010675839148461819, "Height": 0.0072976225055754185, "Left": 0.685575544834137, "Top": 0.47717827558517456}, "Polygon": [{"X": 0.6855757236480713, "Y": 0.47717827558517456}, {"X": 0.696251392364502, "Y": 0.47717976570129395}, {"X": 0.6962511539459229, "Y": 0.48447591066360474}, {"X": 0.685575544834137, "Y": 0.48447439074516296}]}, "Id": "79338fae-31b3-481a-8619-d0956e9fc25a"}, {"BlockType": "WORD", "Confidence": 37.43602752685547, "Text": "$232,-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13124403357505798, "Height": 0.020313480868935585, "Left": 0.6951780915260315, "Top": 0.4697116017341614}, "Polygon": [{"X": 0.6951786875724792, "Y": 0.4697116017341614}, {"X": 0.8264221549034119, "Y": 0.46973010897636414}, {"X": 0.8264215588569641, "Y": 0.4900250732898712}, {"X": 0.6951780915260315, "Y": 0.49000656604766846}]}, "Id": "c90240e5-1cc2-44f1-9ea5-18c272973ff6"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04753455892205238, "Height": 0.014282872900366783, "Left": 0.5824187994003296, "Top": 0.4914499819278717}, "Polygon": [{"X": 0.5824191570281982, "Y": 0.4914499819278717}, {"X": 0.6299533247947693, "Y": 0.49145668745040894}, {"X": 0.6299529075622559, "Y": 0.5057328343391418}, {"X": 0.5824187994003296, "Y": 0.505726158618927}]}, "Id": "094edfa0-7357-442a-b6db-56ed6fcf0c39"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05475565418601036, "Height": 0.012177536264061928, "Left": 0.6342670917510986, "Top": 0.49162039160728455}, "Polygon": [{"X": 0.6342674493789673, "Y": 0.49162039160728455}, {"X": 0.6890227794647217, "Y": 0.49162811040878296}, {"X": 0.689022421836853, "Y": 0.5037978887557983}, {"X": 0.6342670917510986, "Y": 0.5037901997566223}]}, "Id": "e34b6fcd-5248-46de-88fb-fdd7931dc672"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03135434165596962, "Height": 0.01193633209913969, "Left": 0.6933801770210266, "Top": 0.4916652739048004}, "Polygon": [{"X": 0.6933805346488953, "Y": 0.4916652739048004}, {"X": 0.7247345447540283, "Y": 0.4916696846485138}, {"X": 0.7247341871261597, "Y": 0.503601610660553}, {"X": 0.6933801770210266, "Y": 0.5035971999168396}]}, "Id": "f4d15d10-d638-4f15-8c1c-e243385e4894"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03455721214413643, "Height": 0.008605338633060455, "Left": 0.7295389175415039, "Top": 0.49499258399009705}, "Polygon": [{"X": 0.7295392155647278, "Y": 0.49499258399009705}, {"X": 0.7640961408615112, "Y": 0.49499744176864624}, {"X": 0.7640959024429321, "Y": 0.5035979151725769}, {"X": 0.7295389175415039, "Y": 0.5035930275917053}]}, "Id": "3cba6d23-e1d8-4f1c-863b-682bbbb9b6ac"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019423341378569603, "Height": 0.011654733680188656, "Left": 0.7689269781112671, "Top": 0.4918619394302368}, "Polygon": [{"X": 0.7689273357391357, "Y": 0.4918619394302368}, {"X": 0.7883503437042236, "Y": 0.4918646812438965}, {"X": 0.788349986076355, "Y": 0.503516674041748}, {"X": 0.7689269781112671, "Y": 0.5035139322280884}]}, "Id": "292f3f9f-bdb8-46f4-b492-723a7a0d71e9"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032880451530218124, "Height": 0.01037353090941906, "Left": 0.7925770878791809, "Top": 0.4934443533420563}, "Polygon": [{"X": 0.7925773859024048, "Y": 0.4934443533420563}, {"X": 0.8254575729370117, "Y": 0.49344900250434875}, {"X": 0.8254572153091431, "Y": 0.5038179159164429}, {"X": 0.7925770878791809, "Y": 0.5038132667541504}]}, "Id": "bd6af52e-5ca5-4d0f-a922-0baf7d4e63d4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01619839482009411, "Height": 0.009988473728299141, "Left": 0.829407811164856, "Top": 0.4936181306838989}, "Polygon": [{"X": 0.8294081091880798, "Y": 0.4936181306838989}, {"X": 0.8456062078475952, "Y": 0.4936204254627228}, {"X": 0.8456059098243713, "Y": 0.5036066174507141}, {"X": 0.829407811164856, "Y": 0.5036042928695679}]}, "Id": "65136a3c-e1db-4add-8335-8e8e856cdb11"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025166315957903862, "Height": 0.01152397133409977, "Left": 0.8498008251190186, "Top": 0.4920193552970886}, "Polygon": [{"X": 0.8498011827468872, "Y": 0.4920193552970886}, {"X": 0.874967098236084, "Y": 0.49202290177345276}, {"X": 0.8749667406082153, "Y": 0.5035433173179626}, {"X": 0.8498008251190186, "Y": 0.5035398006439209}]}, "Id": "5b726a48-1c75-4b9a-9d2b-1ed8bf7e0565"}, {"BlockType": "WORD", "Confidence": 99.48262023925781, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06332949548959732, "Height": 0.01425098069012165, "Left": 0.5937391519546509, "Top": 0.5080398917198181}, "Polygon": [{"X": 0.5937395691871643, "Y": 0.5080398917198181}, {"X": 0.65706866979599, "Y": 0.5080488324165344}, {"X": 0.6570682525634766, "Y": 0.5222908854484558}, {"X": 0.5937391519546509, "Y": 0.5222819447517395}]}, "Id": "b6b8ad0e-0042-4958-ab0f-164eefa2db6a"}, {"BlockType": "WORD", "Confidence": 98.69519805908203, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029091598466038704, "Height": 0.011797674931585789, "Left": 0.5835147500038147, "Top": 0.5428829789161682}, "Polygon": [{"X": 0.5835151076316833, "Y": 0.5428829789161682}, {"X": 0.6126063466072083, "Y": 0.5428870916366577}, {"X": 0.6126060485839844, "Y": 0.5546807050704956}, {"X": 0.5835147500038147, "Y": 0.5546765923500061}]}, "Id": "42002a43-d1ce-4a9d-8800-1ef7138f2d6f"}, {"BlockType": "WORD", "Confidence": 99.64764404296875, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03693893551826477, "Height": 0.011645348742604256, "Left": 0.6172640919685364, "Top": 0.5431277751922607}, "Polygon": [{"X": 0.617264449596405, "Y": 0.5431277751922607}, {"X": 0.6542030572891235, "Y": 0.543133020401001}, {"X": 0.6542026996612549, "Y": 0.5547731518745422}, {"X": 0.6172640919685364, "Y": 0.554767906665802}]}, "Id": "f3aba980-9eaf-498a-9965-d99d853449e1"}, {"BlockType": "WORD", "Confidence": 95.02890014648438, "Text": "$2,043.50", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17840102314949036, "Height": 0.026044920086860657, "Left": 0.6595098376274109, "Top": 0.5375681519508362}, "Polygon": [{"X": 0.659510612487793, "Y": 0.5375681519508362}, {"X": 0.8379108905792236, "Y": 0.5375933051109314}, {"X": 0.8379100561141968, "Y": 0.5636130571365356}, {"X": 0.6595098376274109, "Y": 0.5635879635810852}]}, "Id": "0ceb549b-3ff3-440a-8ed0-bb0dff3beae5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10409253090620041, "Height": 0.01852380484342575, "Left": 0.11781433969736099, "Top": 0.5796347856521606}, "Polygon": [{"X": 0.1178147941827774, "Y": 0.5796347856521606}, {"X": 0.2219068706035614, "Y": 0.5796494483947754}, {"X": 0.2219063937664032, "Y": 0.598158597946167}, {"X": 0.11781433969736099, "Y": 0.5981439352035522}]}, "Id": "0cbdf86c-7311-41a7-bf62-ddd1debe5447"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1369275450706482, "Height": 0.022716624662280083, "Left": 0.23155957460403442, "Top": 0.5796909928321838}, "Polygon": [{"X": 0.23156015574932098, "Y": 0.5796909928321838}, {"X": 0.3684871196746826, "Y": 0.5797103047370911}, {"X": 0.36848652362823486, "Y": 0.6024076342582703}, {"X": 0.23155957460403442, "Y": 0.602388322353363}]}, "Id": "6ebcb563-a13f-44dd-8414-c2700bcefdc7"}, {"BlockType": "WORD", "Confidence": 79.84156036376953, "Text": "requipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06127878651022911, "Height": 0.009871997870504856, "Left": 0.11641670018434525, "Top": 0.6048676371574402}, "Polygon": [{"X": 0.11641694605350494, "Y": 0.6048676371574402}, {"X": 0.17769548296928406, "Y": 0.6048762202262878}, {"X": 0.17769522964954376, "Y": 0.6147395968437195}, {"X": 0.11641670018434525, "Y": 0.6147310137748718}]}, "Id": "34599584-fdfe-443b-97d7-739fac33baf0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04202340543270111, "Height": 0.010577251203358173, "Left": 0.18039287626743317, "Top": 0.6040776968002319}, "Polygon": [{"X": 0.18039314448833466, "Y": 0.6040776968002319}, {"X": 0.22241628170013428, "Y": 0.6040836572647095}, {"X": 0.2224159985780716, "Y": 0.6146549582481384}, {"X": 0.18039287626743317, "Y": 0.6146490573883057}]}, "Id": "60af053a-79d4-4415-b9fa-ab7df62910e3"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024317173287272453, "Height": 0.008349860087037086, "Left": 0.22517673671245575, "Top": 0.6040480732917786}, "Polygon": [{"X": 0.22517696022987366, "Y": 0.6040480732917786}, {"X": 0.24949391186237335, "Y": 0.6040515303611755}, {"X": 0.24949370324611664, "Y": 0.6123979687690735}, {"X": 0.22517673671245575, "Y": 0.6123945116996765}]}, "Id": "96a7f19b-8366-4e4c-91a7-55f39c000a5f"}, {"BlockType": "WORD", "Confidence": 96.652587890625, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060892704874277115, "Height": 0.008753020316362381, "Left": 0.2520337700843811, "Top": 0.6037749648094177}, "Polygon": [{"X": 0.2520339787006378, "Y": 0.6037749648094177}, {"X": 0.3129264712333679, "Y": 0.6037835478782654}, {"X": 0.3129262328147888, "Y": 0.6125279664993286}, {"X": 0.2520337700843811, "Y": 0.612519383430481}]}, "Id": "67025f2d-170a-41f5-a3c4-1c83f276c184"}, {"BlockType": "WORD", "Confidence": 63.48931884765625, "Text": "documentation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08533047884702682, "Height": 0.008737897500395775, "Left": 0.31525692343711853, "Top": 0.6037442684173584}, "Polygon": [{"X": 0.31525716185569763, "Y": 0.6037442684173584}, {"X": 0.40058740973472595, "Y": 0.6037562489509583}, {"X": 0.40058717131614685, "Y": 0.6124821305274963}, {"X": 0.31525692343711853, "Y": 0.6124701499938965}]}, "Id": "e77bac3c-28bc-40d2-b942-aafed069c388"}, {"BlockType": "WORD", "Confidence": 98.87496185302734, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053650036454200745, "Height": 0.011379420757293701, "Left": 0.11733164638280869, "Top": 0.6286805868148804}, "Polygon": [{"X": 0.11733192950487137, "Y": 0.6286805868148804}, {"X": 0.17098169028759003, "Y": 0.6286881566047668}, {"X": 0.17098139226436615, "Y": 0.6400600075721741}, {"X": 0.11733164638280869, "Y": 0.6400524377822876}]}, "Id": "923e3dce-3237-43c3-be82-29d0e11bf384"}, {"BlockType": "WORD", "Confidence": 89.15995788574219, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005627519451081753, "Height": 0.012094262056052685, "Left": 0.17592543363571167, "Top": 0.6299233436584473}, "Polygon": [{"X": 0.17592574656009674, "Y": 0.6299233436584473}, {"X": 0.1815529614686966, "Y": 0.6299241781234741}, {"X": 0.18155264854431152, "Y": 0.6420176029205322}, {"X": 0.17592543363571167, "Y": 0.6420168280601501}]}, "Id": "649bdb12-fbc2-4273-b6d6-f1eeaa94fc4f"}, {"BlockType": "WORD", "Confidence": 96.41282653808594, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005820860154926777, "Height": 0.012711962684988976, "Left": 0.19829022884368896, "Top": 0.6293660998344421}, "Polygon": [{"X": 0.19829055666923523, "Y": 0.6293660998344421}, {"X": 0.20411109924316406, "Y": 0.629366934299469}, {"X": 0.2041107714176178, "Y": 0.6420781016349792}, {"X": 0.19829022884368896, "Y": 0.6420772671699524}]}, "Id": "387b822c-7aad-4675-b211-80d71571ec96"}, {"BlockType": "WORD", "Confidence": 92.89601135253906, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009872401133179665, "Height": 0.00855213962495327, "Left": 0.20914724469184875, "Top": 0.631386935710907}, "Polygon": [{"X": 0.20914746820926666, "Y": 0.631386935710907}, {"X": 0.21901965141296387, "Y": 0.6313883066177368}, {"X": 0.21901942789554596, "Y": 0.6399390697479248}, {"X": 0.20914724469184875, "Y": 0.639937698841095}]}, "Id": "3892136e-c739-485a-9656-5537ae739017"}, {"BlockType": "WORD", "Confidence": 98.6328125, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06593728065490723, "Height": 0.01258140429854393, "Left": 0.2237004041671753, "Top": 0.628320574760437}, "Polygon": [{"X": 0.22370073199272156, "Y": 0.628320574760437}, {"X": 0.2896376848220825, "Y": 0.6283298134803772}, {"X": 0.28963735699653625, "Y": 0.6409019827842712}, {"X": 0.2237004041671753, "Y": 0.6408926844596863}]}, "Id": "2f36083d-1f16-4fda-bbbc-9b734a19866e"}, {"BlockType": "WORD", "Confidence": 88.62962341308594, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010121982544660568, "Height": 0.012218808755278587, "Left": 0.30971604585647583, "Top": 0.6285836696624756}, "Polygon": [{"X": 0.3097163438796997, "Y": 0.6285836696624756}, {"X": 0.3198380172252655, "Y": 0.6285851001739502}, {"X": 0.31983768939971924, "Y": 0.6408025026321411}, {"X": 0.30971604585647583, "Y": 0.6408010721206665}]}, "Id": "1a56cbed-3d03-4b01-864a-a7b55927e207"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04240143671631813, "Height": 0.011343386024236679, "Left": 0.6024941802024841, "Top": 0.6285437345504761}, "Polygon": [{"X": 0.602494478225708, "Y": 0.6285437345504761}, {"X": 0.644895613193512, "Y": 0.6285496950149536}, {"X": 0.6448952555656433, "Y": 0.6398870944976807}, {"X": 0.6024941802024841, "Y": 0.6398811340332031}]}, "Id": "836b877c-65d1-4812-9c0e-f67437d8cefb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09105204045772552, "Height": 0.011598109267652035, "Left": 0.6499117612838745, "Top": 0.6283674836158752}, "Polygon": [{"X": 0.6499120593070984, "Y": 0.6283674836158752}, {"X": 0.7409638166427612, "Y": 0.628380298614502}, {"X": 0.7409634590148926, "Y": 0.6399655938148499}, {"X": 0.6499117612838745, "Y": 0.6399527788162231}]}, "Id": "d6935f07-091d-4a20-ab9d-3cb106cf8c73"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027909638360142708, "Height": 0.011229475028812885, "Left": 0.6047047972679138, "Top": 0.645686686038971}, "Polygon": [{"X": 0.6047050952911377, "Y": 0.645686686038971}, {"X": 0.6326144337654114, "Y": 0.6456906199455261}, {"X": 0.6326140761375427, "Y": 0.6569162011146545}, {"X": 0.6047047972679138, "Y": 0.6569122672080994}]}, "Id": "046c5146-448c-42df-a66c-eddb242a4519"}, {"BlockType": "WORD", "Confidence": 99.51191711425781, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04565737023949623, "Height": 0.011421551927924156, "Left": 0.63669753074646, "Top": 0.6456255912780762}, "Polygon": [{"X": 0.6366978287696838, "Y": 0.6456255912780762}, {"X": 0.6823548674583435, "Y": 0.6456320285797119}, {"X": 0.6823545694351196, "Y": 0.6570471525192261}, {"X": 0.63669753074646, "Y": 0.6570407152175903}]}, "Id": "10e56817-6cef-42b3-950e-2cf4bb831f0d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040737736970186234, "Height": 0.011813991703093052, "Left": 0.11747674643993378, "Top": 0.6623064875602722}, "Polygon": [{"X": 0.11747703701257706, "Y": 0.6623064875602722}, {"X": 0.1582144796848297, "Y": 0.6623122692108154}, {"X": 0.15821418166160583, "Y": 0.6741204857826233}, {"X": 0.11747674643993378, "Y": 0.6741147637367249}]}, "Id": "d1cfe41d-af0f-4228-ab46-6e001425c4aa"}, {"BlockType": "WORD", "Confidence": 98.79524230957031, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0539955273270607, "Height": 0.012356875464320183, "Left": 0.16298943758010864, "Top": 0.662104070186615}, "Polygon": [{"X": 0.16298973560333252, "Y": 0.662104070186615}, {"X": 0.21698495745658875, "Y": 0.6621116995811462}, {"X": 0.21698464453220367, "Y": 0.6744609475135803}, {"X": 0.16298943758010864, "Y": 0.6744533777236938}]}, "Id": "74ce0460-922d-49f7-a2d6-27a9f19ea3a5"}, {"BlockType": "WORD", "Confidence": 76.20376586914062, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027942171320319176, "Height": 0.014829600229859352, "Left": 0.22228194773197174, "Top": 0.6617045998573303}, "Polygon": [{"X": 0.2222823202610016, "Y": 0.6617045998573303}, {"X": 0.25022411346435547, "Y": 0.6617085337638855}, {"X": 0.25022372603416443, "Y": 0.6765342354774475}, {"X": 0.22228194773197174, "Y": 0.6765303015708923}]}, "Id": "8b1ea7f7-b13e-48c7-8255-28e1b479180c"}, {"BlockType": "WORD", "Confidence": 79.0710678100586, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009297792799770832, "Height": 0.008624798618257046, "Left": 0.255423367023468, "Top": 0.6659112572669983}, "Polygon": [{"X": 0.25542357563972473, "Y": 0.6659112572669983}, {"X": 0.264721155166626, "Y": 0.6659125685691833}, {"X": 0.2647209167480469, "Y": 0.6745360493659973}, {"X": 0.255423367023468, "Y": 0.6745347380638123}]}, "Id": "86bbbf82-e80e-4c9a-9c40-d080d1f1171f"}, {"BlockType": "WORD", "Confidence": 96.89891052246094, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06112426146864891, "Height": 0.01323897484689951, "Left": 0.2642442286014557, "Top": 0.6620985269546509}, "Polygon": [{"X": 0.26424455642700195, "Y": 0.6620985269546509}, {"X": 0.3253684937953949, "Y": 0.6621071100234985}, {"X": 0.32536813616752625, "Y": 0.6753374934196472}, {"X": 0.2642442286014557, "Y": 0.6753289103507996}]}, "Id": "ced5cfed-3d8b-41ce-85cc-b6fc9929502a"}, {"BlockType": "WORD", "Confidence": 97.50338745117188, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01133705209940672, "Height": 0.004824519623070955, "Left": 0.32997235655784607, "Top": 0.6657844185829163}, "Polygon": [{"X": 0.329972505569458, "Y": 0.6657844185829163}, {"X": 0.34130942821502686, "Y": 0.6657860279083252}, {"X": 0.3413092792034149, "Y": 0.6706089377403259}, {"X": 0.32997235655784607, "Y": 0.6706073880195618}]}, "Id": "2d5fba5a-8f64-46a3-8d49-0aff46db90b1"}, {"BlockType": "WORD", "Confidence": 74.3895492553711, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00997991394251585, "Height": 0.012337488122284412, "Left": 0.3460158705711365, "Top": 0.6621382832527161}, "Polygon": [{"X": 0.34601619839668274, "Y": 0.6621382832527161}, {"X": 0.3559958040714264, "Y": 0.6621396541595459}, {"X": 0.3559954762458801, "Y": 0.6744757294654846}, {"X": 0.3460158705711365, "Y": 0.6744743585586548}]}, "Id": "c6df785d-5a27-455a-8cf3-5059573f546c"}, {"BlockType": "WORD", "Confidence": 95.77567291259766, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010098937898874283, "Height": 0.01212747860699892, "Left": 0.603948712348938, "Top": 0.6791166067123413}, "Polygon": [{"X": 0.6039490699768066, "Y": 0.6791166067123413}, {"X": 0.614047646522522, "Y": 0.6791180372238159}, {"X": 0.6140472888946533, "Y": 0.6912440657615662}, {"X": 0.603948712348938, "Y": 0.6912426352500916}]}, "Id": "3003bc3b-d9a2-4b98-8b25-b454d0a0ab04"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044669851660728455, "Height": 0.01153523288667202, "Left": 0.11812970787286758, "Top": 0.6964622735977173}, "Polygon": [{"X": 0.11812999844551086, "Y": 0.6964622735977173}, {"X": 0.16279956698417664, "Y": 0.6964685916900635}, {"X": 0.16279926896095276, "Y": 0.7079975008964539}, {"X": 0.11812970787286758, "Y": 0.7079912424087524}]}, "Id": "4226da2b-f384-4cd3-b60f-bc58886ce0d0"}, {"BlockType": "WORD", "Confidence": 99.52327728271484, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040473248809576035, "Height": 0.011523894965648651, "Left": 0.1672496348619461, "Top": 0.6963143348693848}, "Polygon": [{"X": 0.1672499179840088, "Y": 0.6963143348693848}, {"X": 0.20772287249565125, "Y": 0.6963200569152832}, {"X": 0.20772258937358856, "Y": 0.707838237285614}, {"X": 0.1672496348619461, "Y": 0.7078325152397156}]}, "Id": "c5b615bc-0b9b-4253-82a2-064a8ff6d781"}, {"BlockType": "WORD", "Confidence": 93.00482177734375, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005708469543606043, "Height": 0.013021725229918957, "Left": 0.21340321004390717, "Top": 0.6972136497497559}, "Polygon": [{"X": 0.21340353786945343, "Y": 0.6972136497497559}, {"X": 0.21911168098449707, "Y": 0.6972144842147827}, {"X": 0.2191113382577896, "Y": 0.7102353572845459}, {"X": 0.21340321004390717, "Y": 0.7102345824241638}]}, "Id": "b6c40a86-493a-4f25-a2a7-3c1430edec21"}, {"BlockType": "WORD", "Confidence": 93.48473358154297, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006112797185778618, "Height": 0.012515691109001637, "Left": 0.23562274873256683, "Top": 0.6970536708831787}, "Polygon": [{"X": 0.2356230616569519, "Y": 0.6970536708831787}, {"X": 0.2417355477809906, "Y": 0.6970545649528503}, {"X": 0.24173521995544434, "Y": 0.7095693945884705}, {"X": 0.23562274873256683, "Y": 0.7095685601234436}]}, "Id": "460904c5-2383-44b6-91f9-8d93d90d5471"}, {"BlockType": "WORD", "Confidence": 73.59195709228516, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009887147694826126, "Height": 0.008834282867610455, "Left": 0.2465316206216812, "Top": 0.6992959976196289}, "Polygon": [{"X": 0.24653184413909912, "Y": 0.6992959976196289}, {"X": 0.25641876459121704, "Y": 0.6992973685264587}, {"X": 0.25641852617263794, "Y": 0.7081303000450134}, {"X": 0.2465316206216812, "Y": 0.7081288695335388}]}, "Id": "0d5ecb49-8ace-4d21-b006-84a95f4f255d"}, {"BlockType": "WORD", "Confidence": 97.79874420166016, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0658784806728363, "Height": 0.011710491962730885, "Left": 0.2612293064594269, "Top": 0.6966966390609741}, "Polygon": [{"X": 0.26122960448265076, "Y": 0.6966966390609741}, {"X": 0.3271077871322632, "Y": 0.6967058777809143}, {"X": 0.3271074891090393, "Y": 0.7084071040153503}, {"X": 0.2612293064594269, "Y": 0.7083978652954102}]}, "Id": "45ede67b-c142-425c-8bd1-fcfdef1d934c"}, {"BlockType": "WORD", "Confidence": 79.47225952148438, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010627293027937412, "Height": 0.003068731864914298, "Left": 0.3316441774368286, "Top": 0.6993433237075806}, "Polygon": [{"X": 0.3316442668437958, "Y": 0.6993433237075806}, {"X": 0.34227147698402405, "Y": 0.6993448138237}, {"X": 0.3422713875770569, "Y": 0.7024120688438416}, {"X": 0.3316441774368286, "Y": 0.7024105191230774}]}, "Id": "4dff074a-1d68-4db2-80a0-38e9ca67c2db"}, {"BlockType": "WORD", "Confidence": 97.68495178222656, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010039864107966423, "Height": 0.011877928860485554, "Left": 0.34704914689064026, "Top": 0.6964814066886902}, "Polygon": [{"X": 0.3470494747161865, "Y": 0.6964814066886902}, {"X": 0.35708901286125183, "Y": 0.69648277759552}, {"X": 0.35708868503570557, "Y": 0.7083593010902405}, {"X": 0.34704914689064026, "Y": 0.7083578705787659}]}, "Id": "64fb9f09-66fc-453d-a5a8-069c5d3ffdd0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04021437093615532, "Height": 0.011835435405373573, "Left": 0.11816546320915222, "Top": 0.7308557033538818}, "Polygon": [{"X": 0.1181657612323761, "Y": 0.7308557033538818}, {"X": 0.15837983787059784, "Y": 0.7308613657951355}, {"X": 0.15837953984737396, "Y": 0.7426910996437073}, {"X": 0.11816546320915222, "Y": 0.7426854968070984}]}, "Id": "5613ff14-0272-48e7-aabb-45a2f1d5d7fa"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05481516197323799, "Height": 0.014726093038916588, "Left": 0.16341908276081085, "Top": 0.730798602104187}, "Polygon": [{"X": 0.1634194552898407, "Y": 0.730798602104187}, {"X": 0.21823424100875854, "Y": 0.7308063507080078}, {"X": 0.2182338684797287, "Y": 0.7455247044563293}, {"X": 0.16341908276081085, "Y": 0.7455170154571533}]}, "Id": "4510639f-072c-4932-b11c-afe7c1ff0474"}, {"BlockType": "WORD", "Confidence": 91.17486572265625, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0055338847450912, "Height": 0.012058770284056664, "Left": 0.22372227907180786, "Top": 0.7318230867385864}, "Polygon": [{"X": 0.22372259199619293, "Y": 0.7318230867385864}, {"X": 0.22925616800785065, "Y": 0.7318239212036133}, {"X": 0.22925585508346558, "Y": 0.74388188123703}, {"X": 0.22372227907180786, "Y": 0.743881106376648}]}, "Id": "206cd09a-5ad8-4b30-99cb-3d4214701ad2"}, {"BlockType": "WORD", "Confidence": 91.30221557617188, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005707674194127321, "Height": 0.013324182480573654, "Left": 0.24576258659362793, "Top": 0.7314085364341736}, "Polygon": [{"X": 0.2457629293203354, "Y": 0.7314085364341736}, {"X": 0.25147026777267456, "Y": 0.7314093708992004}, {"X": 0.2514699101448059, "Y": 0.7447327375411987}, {"X": 0.24576258659362793, "Y": 0.7447319030761719}]}, "Id": "7ff0fcac-0d34-4135-b73e-2e5ae7240df0"}, {"BlockType": "WORD", "Confidence": 63.433311462402344, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010432935319840908, "Height": 0.008735757321119308, "Left": 0.2562576234340668, "Top": 0.7337411642074585}, "Polygon": [{"X": 0.2562578320503235, "Y": 0.7337411642074585}, {"X": 0.26669055223464966, "Y": 0.7337426543235779}, {"X": 0.26669031381607056, "Y": 0.7424769401550293}, {"X": 0.2562576234340668, "Y": 0.7424754500389099}]}, "Id": "ca11c68f-b36a-45ef-bb1f-afa97841742d"}, {"BlockType": "WORD", "Confidence": 95.45659637451172, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05581032112240791, "Height": 0.012936838902533054, "Left": 0.2712685763835907, "Top": 0.7303600311279297}, "Polygon": [{"X": 0.27126890420913696, "Y": 0.7303600311279297}, {"X": 0.3270789086818695, "Y": 0.73036789894104}, {"X": 0.32707855105400085, "Y": 0.7432968616485596}, {"X": 0.2712685763835907, "Y": 0.743289053440094}]}, "Id": "86ea18b1-b53a-402a-b525-c5f0bdc0f16e"}, {"BlockType": "WORD", "Confidence": 97.31046295166016, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011988215148448944, "Height": 0.004923770669847727, "Left": 0.3310393989086151, "Top": 0.7340360879898071}, "Polygon": [{"X": 0.33103954792022705, "Y": 0.7340360879898071}, {"X": 0.34302762150764465, "Y": 0.7340377569198608}, {"X": 0.3430275022983551, "Y": 0.7389598488807678}, {"X": 0.3310393989086151, "Y": 0.7389581203460693}]}, "Id": "4f484646-14f5-4d08-af05-992687573032"}, {"BlockType": "WORD", "Confidence": 96.07382202148438, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009692336432635784, "Height": 0.012331448495388031, "Left": 0.3473632335662842, "Top": 0.7304210662841797}, "Polygon": [{"X": 0.34736356139183044, "Y": 0.7304210662841797}, {"X": 0.35705557465553284, "Y": 0.7304224371910095}, {"X": 0.3570552468299866, "Y": 0.7427524924278259}, {"X": 0.3473632335662842, "Y": 0.7427511811256409}]}, "Id": "d6df5984-9bf2-4d9d-852e-af577a0bac37"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08577918261289597, "Height": 0.014093016274273396, "Left": 0.11782500147819519, "Top": 0.7653647065162659}, "Polygon": [{"X": 0.11782535165548325, "Y": 0.7653647065162659}, {"X": 0.20360419154167175, "Y": 0.7653767466545105}, {"X": 0.2036038190126419, "Y": 0.7794577479362488}, {"X": 0.11782500147819519, "Y": 0.7794457077980042}]}, "Id": "4868b51f-5b42-4280-9c93-54d4b07c51a1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015143593773245811, "Height": 0.01186202559620142, "Left": 0.2085617631673813, "Top": 0.7653040289878845}, "Polygon": [{"X": 0.20856206119060516, "Y": 0.7653040289878845}, {"X": 0.22370535135269165, "Y": 0.7653061747550964}, {"X": 0.22370505332946777, "Y": 0.7771660685539246}, {"X": 0.2085617631673813, "Y": 0.7771639227867126}]}, "Id": "ed3807da-bcf5-4d5a-b944-9452a1c1e2cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04216981679201126, "Height": 0.012150295078754425, "Left": 0.22882036864757538, "Top": 0.7650332450866699}, "Polygon": [{"X": 0.22882068157196045, "Y": 0.7650332450866699}, {"X": 0.27099019289016724, "Y": 0.7650391459465027}, {"X": 0.27098986506462097, "Y": 0.7771835327148438}, {"X": 0.22882036864757538, "Y": 0.777177631855011}]}, "Id": "65a973b1-3da1-412a-bfa8-c303907fa10c"}, {"BlockType": "WORD", "Confidence": 99.37440490722656, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0433683805167675, "Height": 0.012176922522485256, "Left": 0.27519676089286804, "Top": 0.7650023102760315}, "Polygon": [{"X": 0.2751970589160919, "Y": 0.7650023102760315}, {"X": 0.31856513023376465, "Y": 0.7650083899497986}, {"X": 0.3185648024082184, "Y": 0.7771792411804199}, {"X": 0.27519676089286804, "Y": 0.7771731019020081}]}, "Id": "3f677c7e-17d8-48d5-be16-1d484d736801"}, {"BlockType": "WORD", "Confidence": 99.5655288696289, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010133316740393639, "Height": 0.012428064830601215, "Left": 0.32276543974876404, "Top": 0.7648627758026123}, "Polygon": [{"X": 0.3227657675743103, "Y": 0.7648627758026123}, {"X": 0.33289873600006104, "Y": 0.7648642063140869}, {"X": 0.33289840817451477, "Y": 0.7772908210754395}, {"X": 0.32276543974876404, "Y": 0.7772893905639648}]}, "Id": "d49c983b-d951-4218-a052-52569836e4b8"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032408978790044785, "Height": 0.01246245950460434, "Left": 0.11756269633769989, "Top": 0.7834774255752563}, "Polygon": [{"X": 0.11756300926208496, "Y": 0.7834774255752563}, {"X": 0.14997167885303497, "Y": 0.7834819555282593}, {"X": 0.1499713659286499, "Y": 0.7959398627281189}, {"X": 0.11756269633769989, "Y": 0.795935332775116}]}, "Id": "a25bec4a-8d16-492b-84c5-e1a566427413"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06655557453632355, "Height": 0.011859230697154999, "Left": 0.15415357053279877, "Top": 0.7822515964508057}, "Polygon": [{"X": 0.15415386855602264, "Y": 0.7822515964508057}, {"X": 0.22070914506912231, "Y": 0.7822609543800354}, {"X": 0.22070884704589844, "Y": 0.7941108345985413}, {"X": 0.15415357053279877, "Y": 0.7941014766693115}]}, "Id": "54d546bc-1f7f-4ded-8866-d5885b077066"}, {"BlockType": "WORD", "Confidence": 97.39935302734375, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1256713718175888, "Height": 0.013761705718934536, "Left": 0.2252596616744995, "Top": 0.7826889753341675}, "Polygon": [{"X": 0.22526001930236816, "Y": 0.7826889753341675}, {"X": 0.3509310185909271, "Y": 0.782706618309021}, {"X": 0.35093066096305847, "Y": 0.796450674533844}, {"X": 0.2252596616744995, "Y": 0.7964330315589905}]}, "Id": "195abca7-8f60-4b29-9370-5b5781969574"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0711146667599678, "Height": 0.01420679409056902, "Left": 0.5494952201843262, "Top": 0.7830010652542114}, "Polygon": [{"X": 0.5494956374168396, "Y": 0.7830010652542114}, {"X": 0.6206098794937134, "Y": 0.7830110788345337}, {"X": 0.6206094622612, "Y": 0.7972078919410706}, {"X": 0.5494952201843262, "Y": 0.7971978783607483}]}, "Id": "7043cd5b-9251-4c66-b22c-b6ca1a18c669"}, {"BlockType": "WORD", "Confidence": 99.24266052246094, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08005757629871368, "Height": 0.01393220666795969, "Left": 0.6244449615478516, "Top": 0.783394992351532}, "Polygon": [{"X": 0.624445378780365, "Y": 0.783394992351532}, {"X": 0.704502522945404, "Y": 0.7834062576293945}, {"X": 0.7045021057128906, "Y": 0.7973272204399109}, {"X": 0.6244449615478516, "Y": 0.7973159551620483}]}, "Id": "e7a38df1-0471-486d-aa6c-5a9ce711fccf"}, {"BlockType": "WORD", "Confidence": 99.48181915283203, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05159206688404083, "Height": 0.012029643170535564, "Left": 0.11761604994535446, "Top": 0.8164384365081787}, "Polygon": [{"X": 0.11761634796857834, "Y": 0.8164384365081787}, {"X": 0.1692081242799759, "Y": 0.8164456486701965}, {"X": 0.16920781135559082, "Y": 0.8284680843353271}, {"X": 0.11761604994535446, "Y": 0.8284608125686646}]}, "Id": "2c713da8-5a96-4c7a-bf07-1ad95a6bfeeb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028948944061994553, "Height": 0.011225632391870022, "Left": 0.11811071634292603, "Top": 0.8682746291160583}, "Polygon": [{"X": 0.11811099201440811, "Y": 0.8682746291160583}, {"X": 0.14705966413021088, "Y": 0.8682787418365479}, {"X": 0.1470593810081482, "Y": 0.8795002698898315}, {"X": 0.11811071634292603, "Y": 0.8794962167739868}]}, "Id": "afec50b9-73ee-49ea-ab1e-d2bd23a5e5f2"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025950679555535316, "Height": 0.01030617393553257, "Left": 0.15092016756534576, "Top": 0.8672895431518555}, "Polygon": [{"X": 0.15092042088508606, "Y": 0.8672895431518555}, {"X": 0.17687085270881653, "Y": 0.8672931790351868}, {"X": 0.17687058448791504, "Y": 0.8775957226753235}, {"X": 0.15092016756534576, "Y": 0.8775920867919922}]}, "Id": "ecea61b9-a336-4b47-ac5a-30832400bd41"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021968604996800423, "Height": 0.008333445526659489, "Left": 0.18015296757221222, "Top": 0.8695351481437683}, "Polygon": [{"X": 0.18015319108963013, "Y": 0.8695351481437683}, {"X": 0.2021215856075287, "Y": 0.8695382475852966}, {"X": 0.20212136209011078, "Y": 0.8778685927391052}, {"X": 0.18015296757221222, "Y": 0.8778654932975769}]}, "Id": "f399b909-bf5a-4fc4-ae29-91f307f365fb"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05989030376076698, "Height": 0.012638813816010952, "Left": 0.20533068478107452, "Top": 0.8675015568733215}, "Polygon": [{"X": 0.2053310126066208, "Y": 0.8675015568733215}, {"X": 0.2652209997177124, "Y": 0.8675099611282349}, {"X": 0.26522067189216614, "Y": 0.8801403641700745}, {"X": 0.20533068478107452, "Y": 0.8801319599151611}]}, "Id": "7335db53-6564-40c2-98fb-28d826ca3af3"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04305916652083397, "Height": 0.009959128685295582, "Left": 0.26870715618133545, "Top": 0.8674629926681519}, "Polygon": [{"X": 0.26870742440223694, "Y": 0.8674629926681519}, {"X": 0.3117663264274597, "Y": 0.8674690127372742}, {"X": 0.3117660582065582, "Y": 0.8774220943450928}, {"X": 0.26870715618133545, "Y": 0.8774160742759705}]}, "Id": "881fb6ae-ad42-4a26-a2c2-51ee270308f4"}, {"BlockType": "WORD", "Confidence": 99.6436538696289, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008326851762831211, "Height": 0.009763739071786404, "Left": 0.3152005970478058, "Top": 0.8676601052284241}, "Polygon": [{"X": 0.3152008354663849, "Y": 0.8676601052284241}, {"X": 0.32352742552757263, "Y": 0.8676612377166748}, {"X": 0.32352718710899353, "Y": 0.8774238228797913}, {"X": 0.3152005970478058, "Y": 0.8774226307868958}]}, "Id": "0d814767-c93c-4d1a-9af7-bb4f32a99eff"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05749214440584183, "Height": 0.0104988319799304, "Left": 0.3272935748100281, "Top": 0.8672377467155457}, "Polygon": [{"X": 0.32729384303092957, "Y": 0.8672377467155457}, {"X": 0.3847857117652893, "Y": 0.8672458529472351}, {"X": 0.3847854435443878, "Y": 0.8777365684509277}, {"X": 0.3272935748100281, "Y": 0.8777285218238831}]}, "Id": "2ce3225e-72cf-49bd-b8c7-404b2335481d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03120267391204834, "Height": 0.011895028874278069, "Left": 0.38798636198043823, "Top": 0.8678565621376038}, "Polygon": [{"X": 0.3879866898059845, "Y": 0.8678565621376038}, {"X": 0.4191890358924866, "Y": 0.8678609728813171}, {"X": 0.4191887080669403, "Y": 0.8797516226768494}, {"X": 0.38798636198043823, "Y": 0.879747211933136}]}, "Id": "8e3e317c-9444-4906-abf0-bae1035d2f16"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011242419481277466, "Height": 0.010094129480421543, "Left": 0.4226769506931305, "Top": 0.8673338890075684}, "Polygon": [{"X": 0.422677218914032, "Y": 0.8673338890075684}, {"X": 0.43391937017440796, "Y": 0.8673354387283325}, {"X": 0.43391910195350647, "Y": 0.8774279952049255}, {"X": 0.4226769506931305, "Y": 0.8774264454841614}]}, "Id": "9ae77bb1-0449-4c9f-ac55-8b8717a2f44e"}, {"BlockType": "WORD", "Confidence": 97.32939910888672, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0344957634806633, "Height": 0.010123689658939838, "Left": 0.43758878111839294, "Top": 0.8675383925437927}, "Polygon": [{"X": 0.43758904933929443, "Y": 0.8675383925437927}, {"X": 0.47208452224731445, "Y": 0.8675432205200195}, {"X": 0.47208425402641296, "Y": 0.8776620626449585}, {"X": 0.43758878111839294, "Y": 0.8776572346687317}]}, "Id": "9bf3f372-ff37-418f-8aba-924303bac070"}, {"BlockType": "WORD", "Confidence": 99.28252410888672, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0547621063888073, "Height": 0.012011663988232613, "Left": 0.47636422514915466, "Top": 0.8677747249603271}, "Polygon": [{"X": 0.4763645529747009, "Y": 0.8677747249603271}, {"X": 0.5311263203620911, "Y": 0.8677824139595032}, {"X": 0.5311259627342224, "Y": 0.8797863721847534}, {"X": 0.47636422514915466, "Y": 0.8797786831855774}]}, "Id": "07a15a6e-8cbc-405c-a0d2-6e29a0db363b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028886985033750534, "Height": 0.009423933923244476, "Left": 0.5342694520950317, "Top": 0.8703201413154602}, "Polygon": [{"X": 0.5342696905136108, "Y": 0.8703201413154602}, {"X": 0.5631564259529114, "Y": 0.8703241944313049}, {"X": 0.5631561875343323, "Y": 0.8797440528869629}, {"X": 0.5342694520950317, "Y": 0.8797399997711182}]}, "Id": "7d5d46ee-3063-43ab-9879-f3f78bf87f45"}, {"BlockType": "WORD", "Confidence": 91.2414321899414, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01660262979567051, "Height": 0.01001769583672285, "Left": 0.5666442513465881, "Top": 0.8675370216369629}, "Polygon": [{"X": 0.566644549369812, "Y": 0.8675370216369629}, {"X": 0.5832468867301941, "Y": 0.8675393462181091}, {"X": 0.5832465887069702, "Y": 0.877554714679718}, {"X": 0.5666442513465881, "Y": 0.8775523900985718}]}, "Id": "e0a5fdb6-99d6-477c-aa3a-2b791ff22adb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03650062903761864, "Height": 0.009908945299685001, "Left": 0.5869418978691101, "Top": 0.8676736950874329}, "Polygon": [{"X": 0.586942195892334, "Y": 0.8676736950874329}, {"X": 0.623442530632019, "Y": 0.8676788210868835}, {"X": 0.6234422326087952, "Y": 0.8775826692581177}, {"X": 0.5869418978691101, "Y": 0.877577543258667}]}, "Id": "8fb868be-7871-4623-88a6-8562e7fbf84b"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012371562421321869, "Height": 0.010067172348499298, "Left": 0.627257764339447, "Top": 0.8675341010093689}, "Polygon": [{"X": 0.6272580027580261, "Y": 0.8675341010093689}, {"X": 0.6396293044090271, "Y": 0.8675358295440674}, {"X": 0.6396290063858032, "Y": 0.8776012659072876}, {"X": 0.627257764339447, "Y": 0.8775995373725891}]}, "Id": "5f11df04-2b47-4378-8cc9-6119e0625c66"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04823950305581093, "Height": 0.012017665430903435, "Left": 0.6436449289321899, "Top": 0.8678750991821289}, "Polygon": [{"X": 0.6436452865600586, "Y": 0.8678750991821289}, {"X": 0.6918843984603882, "Y": 0.8678818941116333}, {"X": 0.6918841004371643, "Y": 0.8798927664756775}, {"X": 0.6436449289321899, "Y": 0.8798860311508179}]}, "Id": "005071ff-90a1-4f5d-9e5d-719038037810"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02468433789908886, "Height": 0.009880824945867062, "Left": 0.6955345869064331, "Top": 0.8676871657371521}, "Polygon": [{"X": 0.695534884929657, "Y": 0.8676871657371521}, {"X": 0.7202189564704895, "Y": 0.8676906228065491}, {"X": 0.7202186584472656, "Y": 0.8775680065155029}, {"X": 0.6955345869064331, "Y": 0.8775644898414612}]}, "Id": "8469beb2-5645-4a48-b184-f99eff14fd7e"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03719257935881615, "Height": 0.0098500931635499, "Left": 0.723811149597168, "Top": 0.8678760528564453}, "Polygon": [{"X": 0.7238114476203918, "Y": 0.8678760528564453}, {"X": 0.7610037326812744, "Y": 0.8678812384605408}, {"X": 0.7610034346580505, "Y": 0.877726137638092}, {"X": 0.723811149597168, "Y": 0.8777208924293518}]}, "Id": "111bc13f-938e-42a9-9d13-582cb28a1f60"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013110860250890255, "Height": 0.008009781129658222, "Left": 0.7647325396537781, "Top": 0.8699139952659607}, "Polygon": [{"X": 0.7647327780723572, "Y": 0.8699139952659607}, {"X": 0.7778434157371521, "Y": 0.8699158430099487}, {"X": 0.777843177318573, "Y": 0.8779237866401672}, {"X": 0.7647325396537781, "Y": 0.8779219388961792}]}, "Id": "d6d6e431-dea6-4337-affc-989c0921fbdb"}, {"BlockType": "WORD", "Confidence": 63.224647521972656, "Text": "(616)669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1044774204492569, "Height": 0.011757527478039265, "Left": 0.7810070514678955, "Top": 0.8679513931274414}, "Polygon": [{"X": 0.7810073494911194, "Y": 0.8679513931274414}, {"X": 0.8854844570159912, "Y": 0.8679660558700562}, {"X": 0.8854840993881226, "Y": 0.8797088861465454}, {"X": 0.7810070514678955, "Y": 0.8796942234039307}]}, "Id": "f8f9854f-8f9a-44ee-976e-78959156be71"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "ca090803-4c17-43d7-bf1e-d7067d9f7574", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "ca090803-4c17-43d7-bf1e-d7067d9f7574", "content-type": "application/x-amz-json-1.1", "content-length": "84339", "date": "Thu, 18 Sep 2025 18:07:21 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "681f7278-f941-4ce4-bbe9-02bf2a67b6fe", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 18:07:24 GMT", "content-type": "application/json", "content-length": "693", "connection": "keep-alive", "x-amzn-requestid": "681f7278-f941-4ce4-bbe9-02bf2a67b6fe"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The page shows \"Truckload Check-in Form\" and \"Clear-to-Pay Voucher\". The doc includes Load #, broker, cost, etc. This seems like a Clear to Pay. Yes doc type: clear_to_pay. Also it contains a check-in form but the main header is \"Clear-to-Pay Voucher\". So classify as clear_to_pay."}}}, {"toolUse": {"toolUseId": "tooluse_sl2HwXSYRTCb5A7qFde2ZA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2384, "outputTokens": 111, "totalTokens": 2495}, "metrics": {"latencyMs": 743}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6feac388_5_clear_to_pay_2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nSaw 7056563\nAMBVD\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20081\nBroker\nPE\nDate Processed 5/26/2023\nLoad Cost $1,811.50\nRacks Returned 58\nSurcharge + $\n/\nDeductions - $\n\\\n58 XS 4.- = $232,-\nRACKS AMOUNT TOTAL\nRacks + $232,-\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $2,043.50\nBillable Shortages:\nrequipment missing with insufficient documentation\nRacks: ( ) X $250.00 $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) X $25.00 = $\n$\nPallet Jack: ( ) X $500.00 - $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nNotes:\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616)669-7468\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}