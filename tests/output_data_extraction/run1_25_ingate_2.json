{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9999936819076538, "Left": 0.0, "Top": 6.320863576547708e-06}, "Polygon": [{"X": 7.78600792727957e-08, "Y": 8.034724487515632e-06}, {"X": 1.0, "Y": 6.320863576547708e-06}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "a4ab3d5c-4929-4164-8ece-9559c244bcab", "Relationships": [{"Type": "CHILD", "Ids": ["0ad23654-8004-4e9d-aae7-5bd0a4efde1f", "664a68b3-ad0f-420c-8e40-170357ff9bfe", "0b9658cb-8b82-49f3-8b27-0fbd15a66286", "19ebb3c0-128c-4b6e-99d9-9502130f2e7d", "e217ff20-4114-438f-834c-7527e7889c52", "ff27c6a2-fe66-4aee-a711-24d3f3ae9b4c", "e1b4ec60-e49f-4090-96ab-ec8d5bc98ed8", "cbc6b93b-f060-4b53-8aed-e9aba9804fa9", "30286bc3-465b-4b0a-a09f-a5a2caf23248", "f01d1a54-dd3f-4d18-b828-4b1c2a979c1e", "ec2f5424-aa00-4127-93eb-9e8b83fb97a5", "8a7fafca-1230-4da3-8240-2671a4183165", "463922f1-e5da-4a00-9601-4264a4415db6", "6c86f893-5e59-4c56-83f1-dd054c923f1c", "b434a3e1-206a-4cd1-bd6f-d4ae61d834c4", "9b9ff395-02f9-41a7-8f36-d7a19eedf6f8", "494634b9-2eb6-4d78-b2a8-530f689f24ac", "ee57425c-d64e-4784-b3b1-cf862377cdc5", "9150c38b-5532-42dd-8051-c092b6f6a625", "4a88718b-b0ea-4f59-b784-a7981bd33bcb", "f94ee0e1-a5cd-4c5c-a926-c0c86c8fe4be", "020d435b-75ae-45c8-9f41-9ef4a6f66632"]}]}, {"BlockType": "LINE", "Confidence": 99.93572998046875, "Text": "Equipment Information", "Geometry": {"BoundingBox": {"Width": 0.11613726615905762, "Height": 0.013995438814163208, "Left": 0.29738345742225647, "Top": 0.0689803957939148}, "Polygon": [{"X": 0.29738354682922363, "Y": 0.06898057460784912}, {"X": 0.4135207235813141, "Y": 0.0689803957939148}, {"X": 0.4135206341743469, "Y": 0.08297565579414368}, {"X": 0.29738345742225647, "Y": 0.082975834608078}]}, "Id": "0ad23654-8004-4e9d-aae7-5bd0a4efde1f", "Relationships": [{"Type": "CHILD", "Ids": ["81d4c935-7cf4-44ef-ba1d-2d35fc2afa3b", "ddba3ef5-0f11-40b0-b5c1-0d5f063f5411"]}]}, {"BlockType": "LINE", "Confidence": 99.91619873046875, "Text": "Event Information", "Geometry": {"BoundingBox": {"Width": 0.08981986343860626, "Height": 0.01139882393181324, "Left": 0.6492904424667358, "Top": 0.06898357719182968}, "Polygon": [{"X": 0.6492905616760254, "Y": 0.06898371875286102}, {"X": 0.7391103506088257, "Y": 0.06898357719182968}, {"X": 0.7391102313995361, "Y": 0.08038226515054703}, {"X": 0.6492904424667358, "Y": 0.08038239926099777}]}, "Id": "664a68b3-ad0f-420c-8e40-170357ff9bfe", "Relationships": [{"Type": "CHILD", "Ids": ["028c7489-7707-499b-b82a-2f63bf54bbeb", "21e5c535-ccef-4ea5-b961-8237869d2ae0"]}]}, {"BlockType": "LINE", "Confidence": 99.8486328125, "Text": "Equipment ID: MRSU 440762", "Geometry": {"BoundingBox": {"Width": 0.15020135045051575, "Height": 0.01367686502635479, "Left": 0.27199944853782654, "Top": 0.09885606914758682}, "Polygon": [{"X": 0.2719995379447937, "Y": 0.09885629266500473}, {"X": 0.4222008287906647, "Y": 0.09885606914758682}, {"X": 0.4222007393836975, "Y": 0.11253271251916885}, {"X": 0.27199944853782654, "Y": 0.11253292858600616}]}, "Id": "0b9658cb-8b82-49f3-8b27-0fbd15a66286", "Relationships": [{"Type": "CHILD", "Ids": ["51ce8076-e648-4bc3-bd08-a60872edf02c", "7c069f56-47bb-45d2-8afd-060d2874f274", "44425bce-70e9-43c8-8bb7-f3297be683e9", "ec17eaa0-1f88-49f0-b6d8-6b3c25ff7567"]}]}, {"BlockType": "LINE", "Confidence": 99.86268615722656, "Text": "Terminal: CHICAGO 59TH ST", "Geometry": {"BoundingBox": {"Width": 0.15095733106136322, "Height": 0.011312712915241718, "Left": 0.6224543452262878, "Top": 0.09874694049358368}, "Polygon": [{"X": 0.6224544048309326, "Y": 0.09874716401100159}, {"X": 0.7734116911888123, "Y": 0.09874694049358368}, {"X": 0.7734115719795227, "Y": 0.11005943268537521}, {"X": 0.6224543452262878, "Y": 0.11005964875221252}]}, "Id": "19ebb3c0-128c-4b6e-99d9-9502130f2e7d", "Relationships": [{"Type": "CHILD", "Ids": ["5cf7457c-1e06-4310-ab05-ddf05fdde227", "b4ec7d21-adb9-4a82-b9fc-e96fb708da50", "aded42ff-81f4-43b3-b17a-4237599917dc", "1d946ce2-30c7-4c66-bf3e-e8cb50b4c636"]}]}, {"BlockType": "LINE", "Confidence": 99.93359375, "Text": "Associated Equipment ID: SSRT 832122", "Geometry": {"BoundingBox": {"Width": 0.20698723196983337, "Height": 0.01382797583937645, "Left": 0.21169240772724152, "Top": 0.1144697517156601}, "Polygon": [{"X": 0.2116924673318863, "Y": 0.11447004973888397}, {"X": 0.4186796247959137, "Y": 0.1144697517156601}, {"X": 0.4186795651912689, "Y": 0.12829743325710297}, {"X": 0.21169240772724152, "Y": 0.12829773128032684}]}, "Id": "e217ff20-4114-438f-834c-7527e7889c52", "Relationships": [{"Type": "CHILD", "Ids": ["d0d7b50b-40ac-4961-b00e-c5e6d8ceb917", "c94fdd7b-f6aa-40bf-974b-131154d8cd8f", "bbc56128-d17a-4618-903f-43a9610fa0fa", "dd7cb51a-a83d-47b0-8ba1-ed726ee90b65", "1d91e111-8df4-49b0-881f-e6f033ba14ca"]}]}, {"BlockType": "LINE", "Confidence": 99.72835540771484, "Text": "Date and Time: 07/08/2025 20:17", "Geometry": {"BoundingBox": {"Width": 0.1666998565196991, "Height": 0.01139966119080782, "Left": 0.5936931371688843, "Top": 0.11449786275625229}, "Polygon": [{"X": 0.5936932563781738, "Y": 0.11449810117483139}, {"X": 0.7603930234909058, "Y": 0.11449786275625229}, {"X": 0.7603929042816162, "Y": 0.12589728832244873}, {"X": 0.5936931371688843, "Y": 0.12589752674102783}]}, "Id": "ff27c6a2-fe66-4aee-a711-24d3f3ae9b4c", "Relationships": [{"Type": "CHILD", "Ids": ["17893ca8-002e-439f-9ca8-bac9dee77884", "51132171-114d-4f7f-b62a-bad6fcc65bb3", "e743ed09-de5f-4ce1-90e3-b270d283ffe4", "19a73c5b-7a15-4da9-bb6f-e25f44240ad7", "dbf8d59b-1064-4b6a-bf1e-b158e82e8403"]}]}, {"BlockType": "LINE", "Confidence": 99.9509048461914, "Text": "L/E Status: Load", "Geometry": {"BoundingBox": {"Width": 0.08730001747608185, "Height": 0.011271505616605282, "Left": 0.2885200083255768, "Top": 0.13065025210380554}, "Polygon": [{"X": 0.28852006793022156, "Y": 0.1306503862142563}, {"X": 0.3758200407028198, "Y": 0.13065025210380554}, {"X": 0.37581998109817505, "Y": 0.14192163944244385}, {"X": 0.2885200083255768, "Y": 0.1419217586517334}]}, "Id": "e1b4ec60-e49f-4090-96ab-ec8d5bc98ed8", "Relationships": [{"Type": "CHILD", "Ids": ["23a558e6-856e-4a00-a9ee-e53a991ad1bb", "3915756d-b183-400e-a7f2-6e09ea6c1990", "f97485f4-af4c-4f3b-b7c6-dcd7ee80e29b"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Activity: Arrival", "Geometry": {"BoundingBox": {"Width": 0.08080586791038513, "Height": 0.013953294605016708, "Left": 0.6274310350418091, "Top": 0.1305174082517624}, "Polygon": [{"X": 0.6274310946464539, "Y": 0.13051752746105194}, {"X": 0.7082368731498718, "Y": 0.1305174082517624}, {"X": 0.7082367539405823, "Y": 0.14447058737277985}, {"X": 0.6274310350418091, "Y": 0.1444707065820694}]}, "Id": "cbc6b93b-f060-4b53-8aed-e9aba9804fa9", "Relationships": [{"Type": "CHILD", "Ids": ["32456a1c-c882-4498-8374-5c60def918b0", "9aaab189-b18d-47fd-a0e4-d3eb20371efc"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Length: 40'", "Geometry": {"BoundingBox": {"Width": 0.0608481764793396, "Height": 0.013894191943109035, "Left": 0.3052036166191101, "Top": 0.14628806710243225}, "Polygon": [{"X": 0.30520370602607727, "Y": 0.14628815650939941}, {"X": 0.3660517930984497, "Y": 0.14628806710243225}, {"X": 0.36605170369148254, "Y": 0.1601821780204773}, {"X": 0.3052036166191101, "Y": 0.16018226742744446}]}, "Id": "30286bc3-465b-4b0a-a09f-a5a2caf23248", "Relationships": [{"Type": "CHILD", "Ids": ["93f564ab-5db0-416a-ba9b-5ba2d949127e", "0ecd89d4-1f60-4bf1-858e-11834c095ea4"]}]}, {"BlockType": "LINE", "Confidence": 99.94901275634766, "Text": "Drayage Co: SUPER STAR FREIGHT INC", "Geometry": {"BoundingBox": {"Width": 0.2050505429506302, "Height": 0.013875426724553108, "Left": 0.6070642471313477, "Top": 0.1463923454284668}, "Polygon": [{"X": 0.6070643663406372, "Y": 0.14639262855052948}, {"X": 0.8121148347854614, "Y": 0.1463923454284668}, {"X": 0.8121147155761719, "Y": 0.16026748716831207}, {"X": 0.6070642471313477, "Y": 0.16026777029037476}]}, "Id": "f01d1a54-dd3f-4d18-b828-4b1c2a979c1e", "Relationships": [{"Type": "CHILD", "Ids": ["3f203226-1284-4f33-b988-1764dcb060e2", "17d53f36-cac3-4313-9118-d57eb6e29a25", "41529a6b-fa23-46a3-be86-996e3d7abba2", "2f3d1529-a67e-4f99-a84e-f66765d6de89", "abcf4b04-87af-4cf8-a06a-ed6c1f84a1b8", "dfe78a1c-dd13-4503-a04c-9a2a035c26d9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Type: CN", "Geometry": {"BoundingBox": {"Width": 0.051133304834365845, "Height": 0.013865184038877487, "Left": 0.3161860704421997, "Top": 0.16232746839523315}, "Polygon": [{"X": 0.31618615984916687, "Y": 0.16232754290103912}, {"X": 0.36731937527656555, "Y": 0.16232746839523315}, {"X": 0.3673193156719208, "Y": 0.17619259655475616}, {"X": 0.3161860704421997, "Y": 0.17619265615940094}]}, "Id": "ec2f5424-aa00-4127-93eb-9e8b83fb97a5", "Relationships": [{"Type": "CHILD", "Ids": ["9fd65a58-69c9-43cb-a5a5-c8c79c596aa3", "3889af2e-78a4-410f-a515-e6cbe25b93c8"]}]}, {"BlockType": "LINE", "Confidence": 99.85336303710938, "Text": "Driver Information: MURPHY, JOSHUA", "Geometry": {"BoundingBox": {"Width": 0.19449211657047272, "Height": 0.012899129651486874, "Left": 0.6099509000778198, "Top": 0.16242386400699615}, "Polygon": [{"X": 0.6099510192871094, "Y": 0.16242413222789764}, {"X": 0.8044430017471313, "Y": 0.16242386400699615}, {"X": 0.8044428825378418, "Y": 0.1753227412700653}, {"X": 0.6099509000778198, "Y": 0.1753229945898056}]}, "Id": "8a7fafca-1230-4da3-8240-2671a4183165", "Relationships": [{"Type": "CHILD", "Ids": ["5cadbe64-1f63-4893-9fb8-f460c81e38f3", "80323dee-17b8-485c-826c-1270452becc2", "6ab98f9c-01b3-4472-8d95-c964f1abf9dd", "89d23cf2-3689-44ef-8d64-fad3e25e27b6"]}]}, {"BlockType": "LINE", "Confidence": 99.99349212646484, "Text": "SCAC Code: SSRT", "Geometry": {"BoundingBox": {"Width": 0.09479837864637375, "Height": 0.0112106679007411, "Left": 0.6073819398880005, "Top": 0.17855508625507355}, "Polygon": [{"X": 0.6073819994926453, "Y": 0.1785552054643631}, {"X": 0.7021803259849548, "Y": 0.17855508625507355}, {"X": 0.7021802067756653, "Y": 0.18976563215255737}, {"X": 0.6073819398880005, "Y": 0.18976575136184692}]}, "Id": "463922f1-e5da-4a00-9601-4264a4415db6", "Relationships": [{"Type": "CHILD", "Ids": ["ad366a83-8db9-4484-b91b-a16f90fe194e", "72fe9417-be69-43cf-b3ad-9b36abc62f1a", "68182921-85c8-41a4-8fc8-1e92d97cc304"]}]}, {"BlockType": "LINE", "Confidence": 99.94386291503906, "Text": "Seal Number: 00000", "Geometry": {"BoundingBox": {"Width": 0.10633762925863266, "Height": 0.011376269161701202, "Left": 0.6011855602264404, "Top": 0.19408994913101196}, "Polygon": [{"X": 0.60118567943573, "Y": 0.1940900832414627}, {"X": 0.7075232267379761, "Y": 0.19408994913101196}, {"X": 0.7075231075286865, "Y": 0.20546607673168182}, {"X": 0.6011855602264404, "Y": 0.20546621084213257}]}, "Id": "6c86f893-5e59-4c56-83f1-dd054c923f1c", "Relationships": [{"Type": "CHILD", "Ids": ["f279572e-081f-4f33-8e9e-5e9478b567e7", "5463e6f7-ea46-47eb-8ffa-333279ef4053", "751738b2-b938-441d-a362-4a47c0c24ed7"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Booking Number:", "Geometry": {"BoundingBox": {"Width": 0.08851904422044754, "Height": 0.014054889790713787, "Left": 0.5807257294654846, "Top": 0.21021835505962372}, "Polygon": [{"X": 0.5807258486747742, "Y": 0.21021845936775208}, {"X": 0.6692447662353516, "Y": 0.21021835505962372}, {"X": 0.669244647026062, "Y": 0.22427313029766083}, {"X": 0.5807257294654846, "Y": 0.22427323460578918}]}, "Id": "b434a3e1-206a-4cd1-bd6f-d4ae61d834c4", "Relationships": [{"Type": "CHILD", "Ids": ["1509822c-bf06-4ef8-8b11-d8cbd6c3ad1b", "6168e38d-07ec-44df-9511-a23a0e67b0fe"]}]}, {"BlockType": "LINE", "Confidence": 99.9255599975586, "Text": "Inspection Information:", "Geometry": {"BoundingBox": {"Width": 0.1173822209239006, "Height": 0.013924927450716496, "Left": 0.053597841411828995, "Top": 0.2482140064239502}, "Polygon": [{"X": 0.053597886115312576, "Y": 0.24821415543556213}, {"X": 0.1709800660610199, "Y": 0.2482140064239502}, {"X": 0.17098000645637512, "Y": 0.26213881373405457}, {"X": 0.053597841411828995, "Y": 0.2621389329433441}]}, "Id": "9b9ff395-02f9-41a7-8f36-d7a19eedf6f8", "Relationships": [{"Type": "CHILD", "Ids": ["108c8786-be27-44f4-a48f-31067192d32a", "47e9d301-11d7-4c6c-a7c0-a7de14533449"]}]}, {"BlockType": "LINE", "Confidence": 99.76688385009766, "Text": "Please refer ShipCSX images for Inspection Information.", "Geometry": {"BoundingBox": {"Width": 0.26621687412261963, "Height": 0.014249077066779137, "Left": 0.053767528384923935, "Top": 0.264082670211792}, "Polygon": [{"X": 0.053767573088407516, "Y": 0.26408296823501587}, {"X": 0.31998440623283386, "Y": 0.264082670211792}, {"X": 0.3199843168258667, "Y": 0.278331458568573}, {"X": 0.053767528384923935, "Y": 0.2783317565917969}]}, "Id": "494634b9-2eb6-4d78-b2a8-530f689f24ac", "Relationships": [{"Type": "CHILD", "Ids": ["2326a075-320e-4020-b5f0-2c9bc31edd3c", "345a9104-29c3-419a-b39b-7e5e4f6aacae", "e4039d2f-8824-4ab5-bd39-139c505887cc", "5f4dd360-8a57-4597-903a-77e8c7f8a666", "cd1b5e14-dc01-4181-a795-4f419ed23cee", "9e0699e4-28d5-4826-83c2-7698d8833dde", "30d9d5c5-6c0d-4e1e-8af9-55314b301da3"]}]}, {"BlockType": "LINE", "Confidence": 99.9782943725586, "Text": "Damage is captured on recorded images at AGS locations", "Geometry": {"BoundingBox": {"Width": 0.29489853978157043, "Height": 0.01462577935308218, "Left": 0.05379126965999603, "Top": 0.2954127788543701}, "Polygon": [{"X": 0.05379131808876991, "Y": 0.295413076877594}, {"X": 0.3486897945404053, "Y": 0.2954127788543701}, {"X": 0.3486897051334381, "Y": 0.3100382387638092}, {"X": 0.05379126965999603, "Y": 0.3100385367870331}]}, "Id": "ee57425c-d64e-4784-b3b1-cf862377cdc5", "Relationships": [{"Type": "CHILD", "Ids": ["42668ad6-b32d-4b2f-8c14-704a37623cef", "a4637424-2d93-4054-966d-1763dd7198f6", "7314b7b1-3999-4e80-bd29-65f695881464", "7530bd5b-a957-4bef-92e4-16fc65da598d", "3c8e8958-39d7-4fb3-ba21-1137e4dd5721", "83d50a40-6890-40e9-9f70-dd0d888e3385", "e277e3d9-9854-4888-81ff-5d90e0d86208", "f4d393ad-3b23-4e6c-8605-f64756455fe2", "2feba163-b04d-4f61-a398-3c3459fcd20d"]}]}, {"BlockType": "LINE", "Confidence": 99.9568099975586, "Text": "Please note that this summary of data is provided for your convenience only and should not replace your own system of record keeping. CSX does not warrant the accurateness of the data", "Geometry": {"BoundingBox": {"Width": 0.8842694163322449, "Height": 0.014187041670084, "Left": 0.052787624299526215, "Top": 0.3386586904525757}, "Polygon": [{"X": 0.052787672728300095, "Y": 0.33865952491760254}, {"X": 0.9370570778846741, "Y": 0.3386586904525757}, {"X": 0.9370568990707397, "Y": 0.35284489393234253}, {"X": 0.052787624299526215, "Y": 0.3528457283973694}]}, "Id": "9150c38b-5532-42dd-8051-c092b6f6a625", "Relationships": [{"Type": "CHILD", "Ids": ["1c1cd3cd-8ec8-45e9-8727-a7e5adf805ec", "ae4fa0e4-bc36-4f9f-8828-8350d083134b", "03742789-8ff4-496a-80c1-d6c96d03e695", "7d02f29d-e7ae-452c-90ef-8e55792f5b5b", "8a2ee902-f507-42d2-ae12-c78e0a986985", "36178654-6481-4250-9063-789a3c8b5ff7", "9294d059-efec-4edf-95dd-4bc1faf04f57", "ddea734e-f59d-4ce9-a7c7-90a5e45b27df", "0b1cf8e7-38a7-43e6-b086-5e8d3e8ae065", "d8f82f54-6571-4865-899e-3df9e91a3340", "6548ef9a-46a5-4009-90fa-57199362a39f", "f9232e12-94bf-4b2c-9aa7-1d1d10ea3697", "ca9e49ec-e23e-43fb-956a-8f4e258b3093", "bc9370d3-54c2-4981-ae01-ab9fbc79927f", "fcaccda8-052f-43d9-a403-77e25a91de32", "58d2ada8-3a7c-49af-a083-4bf219bb5af5", "934da849-2d29-490a-acb6-599ba0055ad5", "66f00d3d-bc7c-4f82-a495-8cbcb14114ae", "8ec59ee6-08cf-4aff-b4b9-84f218a4d770", "22fcd416-2b05-4238-91b1-11f96e3c9019", "1a3fb4f0-b8ca-4d2c-b985-23a657ca9c6b", "8b9e687b-ca45-47fa-a487-b3bf72718c4d", "31c0c72e-418f-445d-8b8e-c3c5f74227bd", "cb78144b-8ab2-42ec-b7ab-51664fc06267", "20372e06-3040-475f-aa02-d14441e94f3e", "94a844da-466d-4c44-ad88-39c48336a07c", "8356e289-22f7-4769-8c18-395f11ec19c5", "51213034-fe44-4096-b4d1-9bbda70<PERSON><PERSON>", "9a390c19-5114-4278-be72-e8c783cd55d4", "1aefa437-0abf-4a11-9646-96e0e9e123ca", "7af490e3-8270-43bc-a8b3-8356b7542e9f", "051a6aae-f19c-46fd-8775-9e220a4a4ab2"]}]}, {"BlockType": "LINE", "Confidence": 99.96478271484375, "Text": "nor its availability for any particular time.", "Geometry": {"BoundingBox": {"Width": 0.18873253464698792, "Height": 0.014177143573760986, "Left": 0.0527179092168808, "Top": 0.3543650507926941}, "Polygon": [{"X": 0.05271795392036438, "Y": 0.35436519980430603}, {"X": 0.2414504438638687, "Y": 0.3543650507926941}, {"X": 0.24145036935806274, "Y": 0.36854201555252075}, {"X": 0.0527179092168808, "Y": 0.3685421943664551}]}, "Id": "4a88718b-b0ea-4f59-b784-a7981bd33bcb", "Relationships": [{"Type": "CHILD", "Ids": ["8ac2bcfc-4ded-4a80-8042-94a7a144c08e", "f20b0cc1-b7ad-483b-a19e-643f756c1ccc", "a35488c7-cdbe-4232-8146-befa63c95357", "05c5ab42-308f-4d20-b2c7-43a885f7f9cb", "fa75f329-6039-41a0-9f1e-0408cf94219c", "9c1f13df-3208-4c94-88a6-872bfed8c85f", "ae0163c4-3923-4ba1-b6b3-493e73b81620"]}]}, {"BlockType": "LINE", "Confidence": 99.96868133544922, "Text": "For assistance with shipment problems, use the Shipment Problem Resolution tool to contact Customer Service or call 800-542-2754", "Geometry": {"BoundingBox": {"Width": 0.6268273591995239, "Height": 0.014029106125235558, "Left": 0.05265401303768158, "Top": 0.3809857666492462}, "Polygon": [{"X": 0.05265406146645546, "Y": 0.3809863030910492}, {"X": 0.6794813871383667, "Y": 0.3809857666492462}, {"X": 0.6794812679290771, "Y": 0.39501434564590454}, {"X": 0.05265401303768158, "Y": 0.39501485228538513}]}, "Id": "f94ee0e1-a5cd-4c5c-a926-c0c86c8fe4be", "Relationships": [{"Type": "CHILD", "Ids": ["b36030c9-1752-438f-9092-71d8c146d25f", "60b2d89c-54dd-4db8-b785-98552dec854a", "b55b8293-26ea-49c0-8dc4-5630c1f93eda", "d28d8cbc-fc61-409e-a84a-6a600ee06510", "de9c1365-f7f8-44ed-8848-caeff8213afb", "7fe4951a-4595-4ac1-86d0-5287e5368a9b", "12bc6391-290b-4e4d-aef4-c925ae62d85c", "17b7dc7a-69d0-40c5-8431-54ada0fc230e", "5fdb8e30-6911-46e3-bb17-8a620cb5d16f", "502dc47a-41d7-422c-924c-b38a14c3b348", "94f5ee54-1a09-4284-bbc0-42d964375334", "af6953ca-5a0d-4d60-9fca-fe1686d343b7", "f26772b8-7242-443e-8dd1-d903e3aebc45", "aab7475e-6f7a-46bb-b210-9a92a7a454b4", "393cb604-cc99-40b8-91d8-19205cfeb323", "fe9e294c-63c0-44c3-a4da-bceef8b175c3", "efc05d84-1059-488f-9e83-565f51a57993", "a1ba3bc6-0293-483e-b471-50836f0c1097"]}]}, {"BlockType": "LINE", "Confidence": 99.74290466308594, "Text": "[Quoted text hidden]", "Geometry": {"BoundingBox": {"Width": 0.07389477640390396, "Height": 0.011039184406399727, "Left": 0.05238009989261627, "Top": 0.3993090093135834}, "Polygon": [{"X": 0.05238013342022896, "Y": 0.39930906891822815}, {"X": 0.12627488374710083, "Y": 0.3993090093135834}, {"X": 0.12627483904361725, "Y": 0.41034814715385437}, {"X": 0.05238009989261627, "Y": 0.41034820675849915}]}, "Id": "020d435b-75ae-45c8-9f41-9ef4a6f66632", "Relationships": [{"Type": "CHILD", "Ids": ["79415616-6345-4289-a06f-ecd2ec5576c4", "7e287d6e-7b2d-4f07-8a2b-b401e9ff69f2", "1638591d-2c06-4c04-904b-6d75d09ca039"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055057574063539505, "Height": 0.013875974342226982, "Left": 0.29738345742225647, "Top": 0.06909985840320587}, "Polygon": [{"X": 0.29738351702690125, "Y": 0.06909994781017303}, {"X": 0.35244104266166687, "Y": 0.06909985840320587}, {"X": 0.3524409532546997, "Y": 0.08297575265169144}, {"X": 0.29738345742225647, "Y": 0.082975834608078}]}, "Id": "81d4c935-7cf4-44ef-ba1d-2d35fc2afa3b"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05839109793305397, "Height": 0.011399268172681332, "Left": 0.3551296293735504, "Top": 0.0689803957939148}, "Polygon": [{"X": 0.3551296889781952, "Y": 0.06898048520088196}, {"X": 0.4135207235813141, "Y": 0.0689803957939148}, {"X": 0.4135206639766693, "Y": 0.08037957549095154}, {"X": 0.3551296293735504, "Y": 0.0803796648979187}]}, "Id": "ddba3ef5-0f11-40b0-b5c1-0d5f063f5411"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Event", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02913800999522209, "Height": 0.011226850561797619, "Left": 0.6492904424667358, "Top": 0.06915555149316788}, "Polygon": [{"X": 0.6492905616760254, "Y": 0.06915559619665146}, {"X": 0.6784284710884094, "Y": 0.06915555149316788}, {"X": 0.6784283518791199, "Y": 0.08038235455751419}, {"X": 0.6492904424667358, "Y": 0.08038239926099777}]}, "Id": "028c7489-7707-499b-b82a-2f63bf54bbeb"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057953014969825745, "Height": 0.01130749937146902, "Left": 0.6811572909355164, "Top": 0.06898357719182968}, "Polygon": [{"X": 0.6811574101448059, "Y": 0.06898366659879684}, {"X": 0.7391103506088257, "Y": 0.06898357719182968}, {"X": 0.7391102313995361, "Y": 0.08029098808765411}, {"X": 0.6811572909355164, "Y": 0.08029107749462128}]}, "Id": "21e5c535-ccef-4ea5-b961-8237869d2ae0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05500885471701622, "Height": 0.013608934357762337, "Left": 0.27199944853782654, "Top": 0.09892399609088898}, "Polygon": [{"X": 0.2719995379447937, "Y": 0.09892407804727554}, {"X": 0.32700830698013306, "Y": 0.09892399609088898}, {"X": 0.3270082473754883, "Y": 0.1125328540802002}, {"X": 0.27199944853782654, "Y": 0.11253292858600616}]}, "Id": "51ce8076-e648-4bc3-bd08-a60872edf02c"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013392928056418896, "Height": 0.01100181695073843, "Left": 0.3300132751464844, "Top": 0.0990099236369133}, "Polygon": [{"X": 0.33001333475112915, "Y": 0.09900994598865509}, {"X": 0.34340620040893555, "Y": 0.0990099236369133}, {"X": 0.34340614080429077, "Y": 0.11001171916723251}, {"X": 0.3300132751464844, "Y": 0.1100117415189743}]}, "Id": "7c069f56-47bb-45d2-8afd-060d2874f274"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "MRSU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030785055831074715, "Height": 0.011108648031949997, "Left": 0.352410227060318, "Top": 0.098856121301651}, "Polygon": [{"X": 0.35241028666496277, "Y": 0.09885617345571518}, {"X": 0.38319528102874756, "Y": 0.098856121301651}, {"X": 0.3831952214241028, "Y": 0.10996472835540771}, {"X": 0.352410227060318, "Y": 0.1099647730588913}]}, "Id": "44425bce-70e9-43c8-8bb7-f3297be683e9"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "440762", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03572390228509903, "Height": 0.01120366808027029, "Left": 0.38647690415382385, "Top": 0.09886395931243896}, "Polygon": [{"X": 0.38647696375846863, "Y": 0.09886401146650314}, {"X": 0.4222008287906647, "Y": 0.09886395931243896}, {"X": 0.4222007393836975, "Y": 0.11006757616996765}, {"X": 0.38647690415382385, "Y": 0.11006762832403183}]}, "Id": "ec17eaa0-1f88-49f0-b6d8-6b3c25ff7567"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Terminal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046659477055072784, "Height": 0.011070861481130123, "Left": 0.6224543452262878, "Top": 0.09895304590463638}, "Polygon": [{"X": 0.6224544048309326, "Y": 0.09895312041044235}, {"X": 0.66911381483078, "Y": 0.09895304590463638}, {"X": 0.6691136956214905, "Y": 0.11002384126186371}, {"X": 0.6224543452262878, "Y": 0.11002390831708908}]}, "Id": "5cf7457c-1e06-4310-ab05-ddf05fdde227"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "CHICAGO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04984718933701515, "Height": 0.01131256390362978, "Left": 0.6782379150390625, "Top": 0.09874700754880905}, "Polygon": [{"X": 0.678238034248352, "Y": 0.09874708205461502}, {"X": 0.7280851006507874, "Y": 0.09874700754880905}, {"X": 0.7280850410461426, "Y": 0.11005949974060059}, {"X": 0.6782379150390625, "Y": 0.11005956679582596}]}, "Id": "b4ec7d21-adb9-4a82-b9fc-e96fb708da50"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "59TH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025459036231040955, "Height": 0.010920722037553787, "Left": 0.7311055660247803, "Top": 0.09900546073913574}, "Polygon": [{"X": 0.7311056852340698, "Y": 0.09900549799203873}, {"X": 0.7565646171569824, "Y": 0.09900546073913574}, {"X": 0.7565644979476929, "Y": 0.10992614179849625}, {"X": 0.7311055660247803, "Y": 0.10992617905139923}]}, "Id": "aded42ff-81f4-43b3-b17a-4237599917dc"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "ST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013660972006618977, "Height": 0.011011743918061256, "Left": 0.7597507238388062, "Top": 0.09882361441850662}, "Polygon": [{"X": 0.7597507834434509, "Y": 0.09882363677024841}, {"X": 0.7734116911888123, "Y": 0.09882361441850662}, {"X": 0.7734115719795227, "Y": 0.10983534157276154}, {"X": 0.7597507238388062, "Y": 0.10983535647392273}]}, "Id": "1d946ce2-30c7-4c66-bf3e-e8cb50b4c636"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056747037917375565, "Height": 0.011326807551085949, "Left": 0.21169240772724152, "Top": 0.11459983140230179}, "Polygon": [{"X": 0.2116924673318863, "Y": 0.11459991335868835}, {"X": 0.2684394419193268, "Y": 0.11459983140230179}, {"X": 0.268439382314682, "Y": 0.12592655420303345}, {"X": 0.21169240772724152, "Y": 0.1259266436100006}]}, "Id": "d0d7b50b-40ac-4961-b00e-c5e6d8ceb917"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054880689829587936, "Height": 0.013637575320899487, "Left": 0.2719031572341919, "Top": 0.11466006934642792}, "Polygon": [{"X": 0.27190321683883667, "Y": 0.11466014385223389}, {"X": 0.32678383588790894, "Y": 0.11466006934642792}, {"X": 0.32678377628326416, "Y": 0.1282975673675537}, {"X": 0.2719031572341919, "Y": 0.12829764187335968}]}, "Id": "c94fdd7b-f6aa-40bf-974b-131154d8cd8f"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013306157663464546, "Height": 0.010925868526101112, "Left": 0.32996809482574463, "Top": 0.11469537019729614}, "Polygon": [{"X": 0.3299681544303894, "Y": 0.11469539254903793}, {"X": 0.3432742655277252, "Y": 0.11469537019729614}, {"X": 0.34327420592308044, "Y": 0.12562121450901031}, {"X": 0.32996809482574463, "Y": 0.1256212443113327}]}, "Id": "bbc56128-d17a-4618-903f-43a9610fa0fa"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028116507455706596, "Height": 0.011190679855644703, "Left": 0.35250669717788696, "Top": 0.11446980386972427}, "Polygon": [{"X": 0.3525067865848541, "Y": 0.11446984857320786}, {"X": 0.3806232213973999, "Y": 0.11446980386972427}, {"X": 0.3806231617927551, "Y": 0.12566044926643372}, {"X": 0.35250669717788696, "Y": 0.1256604939699173}]}, "Id": "dd7cb51a-a83d-47b0-8ba1-ed726ee90b65"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "832122", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035166725516319275, "Height": 0.010937945917248726, "Left": 0.3835129141807556, "Top": 0.1147332489490509}, "Polygon": [{"X": 0.3835129737854004, "Y": 0.11473330110311508}, {"X": 0.4186796247959137, "Y": 0.1147332489490509}, {"X": 0.4186795651912689, "Y": 0.1256711483001709}, {"X": 0.3835129141807556, "Y": 0.12567119300365448}]}, "Id": "1d91e111-8df4-49b0-881f-e6f033ba14ca"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022754788398742676, "Height": 0.01096298173069954, "Left": 0.5936931371688843, "Top": 0.11474645137786865}, "Polygon": [{"X": 0.5936932563781738, "Y": 0.11474648863077164}, {"X": 0.616447925567627, "Y": 0.11474645137786865}, {"X": 0.6164478659629822, "Y": 0.1257093995809555}, {"X": 0.5936931371688843, "Y": 0.1257094293832779}]}, "Id": "17893ca8-002e-439f-9ca8-bac9dee77884"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018748734146356583, "Height": 0.01099340245127678, "Left": 0.6197072863578796, "Top": 0.11469399183988571}, "Polygon": [{"X": 0.6197073459625244, "Y": 0.1146940216422081}, {"X": 0.6384559869766235, "Y": 0.11469399183988571}, {"X": 0.6384559273719788, "Y": 0.1256873607635498}, {"X": 0.6197072863578796, "Y": 0.1256873905658722}]}, "Id": "51132171-114d-4f7f-b62a-bad6fcc65bb3"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02777629904448986, "Height": 0.011182727292180061, "Left": 0.6409792900085449, "Top": 0.11459819227457047}, "Polygon": [{"X": 0.6409793496131897, "Y": 0.11459822952747345}, {"X": 0.6687555909156799, "Y": 0.11459819227457047}, {"X": 0.6687554717063904, "Y": 0.1257808804512024}, {"X": 0.6409792900085449, "Y": 0.12578092515468597}]}, "Id": "e743ed09-de5f-4ce1-90e3-b270d283ffe4"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "07/08/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05306520685553551, "Height": 0.011354630813002586, "Left": 0.6779889464378357, "Top": 0.11449790000915527}, "Polygon": [{"X": 0.6779890656471252, "Y": 0.11449798196554184}, {"X": 0.7310541272163391, "Y": 0.11449790000915527}, {"X": 0.7310540676116943, "Y": 0.12585246562957764}, {"X": 0.6779889464378357, "Y": 0.1258525401353836}]}, "Id": "19a73c5b-7a15-4da9-bb6f-e25f44240ad7"}, {"BlockType": "WORD", "Confidence": 98.90625, "Text": "20:17", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026091696694493294, "Height": 0.011263013817369938, "Left": 0.7343013286590576, "Top": 0.11463430523872375}, "Polygon": [{"X": 0.7343013882637024, "Y": 0.11463434249162674}, {"X": 0.7603930234909058, "Y": 0.11463430523872375}, {"X": 0.7603929042816162, "Y": 0.12589728832244873}, {"X": 0.7343013286590576, "Y": 0.12589731812477112}]}, "Id": "dbf8d59b-1064-4b6a-bf1e-b158e82e8403"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "L/E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016262073069810867, "Height": 0.010872989892959595, "Left": 0.2885200083255768, "Top": 0.13068188726902008}, "Polygon": [{"X": 0.28852006793022156, "Y": 0.13068191707134247}, {"X": 0.30478209257125854, "Y": 0.13068188726902008}, {"X": 0.30478203296661377, "Y": 0.14155486226081848}, {"X": 0.2885200083255768, "Y": 0.14155487716197968}]}, "Id": "23a558e6-856e-4a00-a9ee-e53a991ad1bb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Status:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03529876843094826, "Height": 0.011271432042121887, "Left": 0.30803707242012024, "Top": 0.13065029680728912}, "Polygon": [{"X": 0.308037132024765, "Y": 0.1306503564119339}, {"X": 0.3433358669281006, "Y": 0.13065029680728912}, {"X": 0.3433357775211334, "Y": 0.14192168414592743}, {"X": 0.30803707242012024, "Y": 0.141921728849411}]}, "Id": "3915756d-b183-400e-a7f2-6e09ea6c1990"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023413212969899178, "Height": 0.011077743023633957, "Left": 0.3524068295955658, "Top": 0.13069748878479004}, "Polygon": [{"X": 0.35240688920021057, "Y": 0.13069751858711243}, {"X": 0.3758200407028198, "Y": 0.13069748878479004}, {"X": 0.37581998109817505, "Y": 0.1417751908302307}, {"X": 0.3524068295955658, "Y": 0.1417752206325531}]}, "Id": "f97485f4-af4c-4f3b-b7c6-dcd7ee80e29b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Activity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042216457426548004, "Height": 0.013953239656984806, "Left": 0.6274310350418091, "Top": 0.13051746785640717}, "Polygon": [{"X": 0.6274310946464539, "Y": 0.13051752746105194}, {"X": 0.6696474552154541, "Y": 0.13051746785640717}, {"X": 0.6696473360061646, "Y": 0.14447064697742462}, {"X": 0.6274310350418091, "Y": 0.1444707065820694}]}, "Id": "32456a1c-c882-4498-8374-5c60def918b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Arrival", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030384914949536324, "Height": 0.01111556589603424, "Left": 0.6778519749641418, "Top": 0.13062316179275513}, "Polygon": [{"X": 0.6778520345687866, "Y": 0.1306232064962387}, {"X": 0.7082368731498718, "Y": 0.13062316179275513}, {"X": 0.7082367539405823, "Y": 0.14173869788646698}, {"X": 0.6778519749641418, "Y": 0.14173872768878937}]}, "Id": "9aaab189-b18d-47fd-a0e4-d3eb20371efc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Length:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038536667823791504, "Height": 0.013894161209464073, "Left": 0.3052036166191101, "Top": 0.14628811180591583}, "Polygon": [{"X": 0.30520370602607727, "Y": 0.14628815650939941}, {"X": 0.3437402844429016, "Y": 0.14628811180591583}, {"X": 0.34374022483825684, "Y": 0.16018220782279968}, {"X": 0.3052036166191101, "Y": 0.16018226742744446}]}, "Id": "93f564ab-5db0-416a-ba9b-5ba2d949127e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "40'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01394616812467575, "Height": 0.011284628883004189, "Left": 0.35210561752319336, "Top": 0.14634156227111816}, "Polygon": [{"X": 0.3521057069301605, "Y": 0.14634157717227936}, {"X": 0.3660517930984497, "Y": 0.14634156227111816}, {"X": 0.36605173349380493, "Y": 0.1576261669397354}, {"X": 0.35210561752319336, "Y": 0.1576261818408966}]}, "Id": "0ecd89d4-1f60-4bf1-858e-11834c095ea4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04265577718615532, "Height": 0.013462858274579048, "Left": 0.6070642471313477, "Top": 0.14680491387844086}, "Polygon": [{"X": 0.6070643663406372, "Y": 0.14680497348308563}, {"X": 0.6497200727462769, "Y": 0.14680491387844086}, {"X": 0.6497199535369873, "Y": 0.16026771068572998}, {"X": 0.6070642471313477, "Y": 0.16026777029037476}]}, "Id": "3f203226-1284-4f33-b988-1764dcb060e2"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Co:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017241718247532845, "Height": 0.010802606120705605, "Left": 0.6520665884017944, "Top": 0.14679086208343506}, "Polygon": [{"X": 0.652066707611084, "Y": 0.14679089188575745}, {"X": 0.6693083047866821, "Y": 0.14679086208343506}, {"X": 0.6693082451820374, "Y": 0.15759344398975372}, {"X": 0.6520665884017944, "Y": 0.1575934737920761}]}, "Id": "17d53f36-cac3-4313-9118-d57eb6e29a25"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "SUPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03642604500055313, "Height": 0.011110865511000156, "Left": 0.6731038093566895, "Top": 0.14645788073539734}, "Polygon": [{"X": 0.6731038689613342, "Y": 0.14645792543888092}, {"X": 0.7095298171043396, "Y": 0.14645788073539734}, {"X": 0.7095297574996948, "Y": 0.15756869316101074}, {"X": 0.6731038093566895, "Y": 0.15756875276565552}]}, "Id": "41529a6b-fa23-46a3-be86-996e3d7abba2"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "STAR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028111325576901436, "Height": 0.011214636266231537, "Left": 0.7122028470039368, "Top": 0.14639244973659515}, "Polygon": [{"X": 0.7122029662132263, "Y": 0.14639247953891754}, {"X": 0.7403141856193542, "Y": 0.14639244973659515}, {"X": 0.7403141260147095, "Y": 0.1576070338487625}, {"X": 0.7122028470039368, "Y": 0.1576070785522461}]}, "Id": "2f3d1529-a67e-4f99-a84e-f66765d6de89"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04670082405209541, "Height": 0.010863407514989376, "Left": 0.7440065741539001, "Top": 0.1466679722070694}, "Polygon": [{"X": 0.7440066337585449, "Y": 0.14666803181171417}, {"X": 0.7907074093818665, "Y": 0.1466679722070694}, {"X": 0.7907072901725769, "Y": 0.15753130614757538}, {"X": 0.7440065741539001, "Y": 0.15753138065338135}]}, "Id": "abcf4b04-87af-4cf8-a06a-ed6c1f84a1b8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "INC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018222738057374954, "Height": 0.01093507744371891, "Left": 0.7938920855522156, "Top": 0.1465829461812973}, "Polygon": [{"X": 0.7938922047615051, "Y": 0.1465829759836197}, {"X": 0.8121148347854614, "Y": 0.1465829461812973}, {"X": 0.8121147155761719, "Y": 0.15751799941062927}, {"X": 0.7938920855522156, "Y": 0.15751802921295166}]}, "Id": "dfe78a1c-dd13-4503-a04c-9a2a035c26d9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02738901413977146, "Height": 0.01379499863833189, "Left": 0.3161860704421997, "Top": 0.16239765286445618}, "Polygon": [{"X": 0.31618615984916687, "Y": 0.16239769756793976}, {"X": 0.3435750901699066, "Y": 0.16239765286445618}, {"X": 0.34357500076293945, "Y": 0.17619262635707855}, {"X": 0.3161860704421997, "Y": 0.17619265615940094}]}, "Id": "9fd65a58-69c9-43cb-a5a5-c8c79c596aa3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014919298700988293, "Height": 0.011179041117429733, "Left": 0.3524000942707062, "Top": 0.16232746839523315}, "Polygon": [{"X": 0.35240015387535095, "Y": 0.16232749819755554}, {"X": 0.36731937527656555, "Y": 0.16232746839523315}, {"X": 0.3673193156719208, "Y": 0.173506498336792}, {"X": 0.3524000942707062, "Y": 0.17350651323795319}]}, "Id": "3889af2e-78a4-410f-a515-e6cbe25b93c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03123265877366066, "Height": 0.011155377142131329, "Left": 0.6099509000778198, "Top": 0.16255398094654083}, "Polygon": [{"X": 0.6099510192871094, "Y": 0.16255402565002441}, {"X": 0.6411835551261902, "Y": 0.16255398094654083}, {"X": 0.6411834955215454, "Y": 0.17370931804180145}, {"X": 0.6099509000778198, "Y": 0.17370936274528503}]}, "Id": "5cadbe64-1f63-4893-9fb8-f460c81e38f3"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "Information:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061521634459495544, "Height": 0.011225651949644089, "Left": 0.6441721320152283, "Top": 0.1624239981174469}, "Polygon": [{"X": 0.644172191619873, "Y": 0.16242408752441406}, {"X": 0.7056937217712402, "Y": 0.1624239981174469}, {"X": 0.7056936621665955, "Y": 0.17364956438541412}, {"X": 0.6441721320152283, "Y": 0.1736496537923813}]}, "Id": "80323dee-17b8-485c-826c-1270452becc2"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "MURPHY,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04882249981164932, "Height": 0.012581690214574337, "Left": 0.7088456749916077, "Top": 0.16274116933345795}, "Polygon": [{"X": 0.7088457942008972, "Y": 0.16274124383926392}, {"X": 0.7576681971549988, "Y": 0.16274116933345795}, {"X": 0.7576680779457092, "Y": 0.17532280087471008}, {"X": 0.7088456749916077, "Y": 0.17532286047935486}]}, "Id": "6ab98f9c-01b3-4472-8d95-c964f1abf9dd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "JOSHUA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043069519102573395, "Height": 0.010877616703510284, "Left": 0.7613735198974609, "Top": 0.1627502143383026}, "Polygon": [{"X": 0.7613735795021057, "Y": 0.1627502739429474}, {"X": 0.8044430017471313, "Y": 0.1627502143383026}, {"X": 0.8044429421424866, "Y": 0.17362777888774872}, {"X": 0.7613735198974609, "Y": 0.1736278384923935}]}, "Id": "89d23cf2-3689-44ef-8d64-fad3e25e27b6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SCAC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029874702915549278, "Height": 0.010874928906559944, "Left": 0.6073819398880005, "Top": 0.1787901371717453}, "Polygon": [{"X": 0.6073819994926453, "Y": 0.17879018187522888}, {"X": 0.6372566223144531, "Y": 0.1787901371717453}, {"X": 0.6372565627098083, "Y": 0.1896650344133377}, {"X": 0.6073819398880005, "Y": 0.1896650642156601}]}, "Id": "ad366a83-8db9-4484-b91b-a16f90fe194e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030015064403414726, "Height": 0.01121058315038681, "Left": 0.6402493715286255, "Top": 0.17855513095855713}, "Polygon": [{"X": 0.6402494311332703, "Y": 0.17855516076087952}, {"X": 0.6702644228935242, "Y": 0.17855513095855713}, {"X": 0.6702643036842346, "Y": 0.18976567685604095}, {"X": 0.6402493715286255, "Y": 0.18976570665836334}]}, "Id": "72fe9417-be69-43cf-b3ad-9b36abc62f1a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028949018567800522, "Height": 0.010936505161225796, "Left": 0.673231303691864, "Top": 0.17875851690769196}, "Polygon": [{"X": 0.6732313632965088, "Y": 0.17875854671001434}, {"X": 0.7021803259849548, "Y": 0.17875851690769196}, {"X": 0.7021802067756653, "Y": 0.18969497084617615}, {"X": 0.673231303691864, "Y": 0.18969501554965973}]}, "Id": "68182921-85c8-41a4-8fc8-1e92d97cc304"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021833963692188263, "Height": 0.011265067383646965, "Left": 0.6011855602264404, "Top": 0.19409005343914032}, "Polygon": [{"X": 0.60118567943573, "Y": 0.1940900832414627}, {"X": 0.6230195164680481, "Y": 0.19409005343914032}, {"X": 0.6230194568634033, "Y": 0.20535509288311005}, {"X": 0.6011855602264404, "Y": 0.20535512268543243}]}, "Id": "f279572e-081f-4f33-8e9e-5e9478b567e7"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04332965612411499, "Height": 0.011312993243336678, "Left": 0.6260933876037598, "Top": 0.19415318965911865}, "Polygon": [{"X": 0.6260934472084045, "Y": 0.19415324926376343}, {"X": 0.6694230437278748, "Y": 0.19415318965911865}, {"X": 0.6694229245185852, "Y": 0.2054661214351654}, {"X": 0.6260933876037598, "Y": 0.20546618103981018}]}, "Id": "5463e6f7-ea46-47eb-8ffa-333279ef4053"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029474711045622826, "Height": 0.01091531291604042, "Left": 0.6780484914779663, "Top": 0.19435057044029236}, "Polygon": [{"X": 0.6780486106872559, "Y": 0.19435060024261475}, {"X": 0.7075232267379761, "Y": 0.19435057044029236}, {"X": 0.7075231075286865, "Y": 0.2052658498287201}, {"X": 0.6780484914779663, "Y": 0.20526587963104248}]}, "Id": "751738b2-b938-441d-a362-4a47c0c24ed7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04192809760570526, "Height": 0.0139956409111619, "Left": 0.5807257294654846, "Top": 0.21027760207653046}, "Polygon": [{"X": 0.5807258486747742, "Y": 0.21027764678001404}, {"X": 0.6226538419723511, "Y": 0.21027760207653046}, {"X": 0.6226537227630615, "Y": 0.2242731899023056}, {"X": 0.5807257294654846, "Y": 0.22427323460578918}]}, "Id": "1509822c-bf06-4ef8-8b11-d8cbd6c3ad1b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04319845885038376, "Height": 0.011301010847091675, "Left": 0.6260462999343872, "Top": 0.21021835505962372}, "Polygon": [{"X": 0.6260464191436768, "Y": 0.2102183997631073}, {"X": 0.6692447662353516, "Y": 0.21021835505962372}, {"X": 0.669244647026062, "Y": 0.22151930630207062}, {"X": 0.6260462999343872, "Y": 0.2215193659067154}]}, "Id": "6168e38d-07ec-44df-9511-a23a0e67b0fe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05266885086894035, "Height": 0.013841073960065842, "Left": 0.053597841411828995, "Top": 0.24829787015914917}, "Polygon": [{"X": 0.053597886115312576, "Y": 0.24829792976379395}, {"X": 0.10626669228076935, "Y": 0.24829787015914917}, {"X": 0.10626664012670517, "Y": 0.26213887333869934}, {"X": 0.053597841411828995, "Y": 0.2621389329433441}]}, "Id": "108c8786-be27-44f4-a48f-31067192d32a"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Information:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061380065977573395, "Height": 0.011326267383992672, "Left": 0.1096000000834465, "Top": 0.2482140064239502}, "Polygon": [{"X": 0.10960004478693008, "Y": 0.24821408092975616}, {"X": 0.1709800660610199, "Y": 0.2482140064239502}, {"X": 0.17098002135753632, "Y": 0.2595402002334595}, {"X": 0.1096000000834465, "Y": 0.25954028964042664}]}, "Id": "47e9d301-11d7-4c6c-a7c0-a7de14533449"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03192508965730667, "Height": 0.011145179159939289, "Left": 0.05376753583550453, "Top": 0.2643861472606659}, "Polygon": [{"X": 0.053767573088407516, "Y": 0.2643861770629883}, {"X": 0.0856926292181015, "Y": 0.2643861472606659}, {"X": 0.08569258451461792, "Y": 0.2755312919616699}, {"X": 0.05376753583550453, "Y": 0.2755313217639923}]}, "Id": "2326a075-320e-4020-b5f0-2c9bc31edd3c"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "refer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022167637944221497, "Height": 0.011164380237460136, "Left": 0.08884087949991226, "Top": 0.26436519622802734}, "Polygon": [{"X": 0.08884092420339584, "Y": 0.26436522603034973}, {"X": 0.11100851744413376, "Y": 0.26436519622802734}, {"X": 0.11100848019123077, "Y": 0.27552956342697144}, {"X": 0.08884087949991226, "Y": 0.2755295932292938}]}, "Id": "345a9104-29c3-419a-b39b-7e5e4f6aacae"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ShipCSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04322930797934532, "Height": 0.013774411752820015, "Left": 0.11384332925081253, "Top": 0.2640828490257263}, "Polygon": [{"X": 0.11384337395429611, "Y": 0.2640829086303711}, {"X": 0.15707263350486755, "Y": 0.2640828490257263}, {"X": 0.15707257390022278, "Y": 0.2778572142124176}, {"X": 0.11384332925081253, "Y": 0.2778572738170624}]}, "Id": "e4039d2f-8824-4ab5-bd39-139c505887cc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431063890457153, "Height": 0.013740433380007744, "Left": 0.15991218388080597, "Top": 0.26459118723869324}, "Polygon": [{"X": 0.15991224348545074, "Y": 0.264591246843338}, {"X": 0.1942228227853775, "Y": 0.26459118723869324}, {"X": 0.19422276318073273, "Y": 0.27833157777786255}, {"X": 0.15991218388080597, "Y": 0.2783316373825073}]}, "Id": "5f4dd360-8a57-4597-903a-77e8c7f8a666"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01307232677936554, "Height": 0.011343683116137981, "Left": 0.1969093531370163, "Top": 0.26412326097488403}, "Polygon": [{"X": 0.19690939784049988, "Y": 0.2641232907772064}, {"X": 0.20998167991638184, "Y": 0.26412326097488403}, {"X": 0.20998162031173706, "Y": 0.2754669487476349}, {"X": 0.1969093531370163, "Y": 0.2754669487476349}]}, "Id": "cd1b5e14-dc01-4181-a795-4f419ed23cee"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04796130582690239, "Height": 0.013933095149695873, "Left": 0.21307021379470825, "Top": 0.2643078863620758}, "Polygon": [{"X": 0.21307027339935303, "Y": 0.2643079161643982}, {"X": 0.26103150844573975, "Y": 0.2643078863620758}, {"X": 0.26103144884109497, "Y": 0.2782409191131592}, {"X": 0.21307021379470825, "Y": 0.27824097871780396}]}, "Id": "9e0699e4-28d5-4826-83c2-7698d8833dde"}, {"BlockType": "WORD", "Confidence": 98.56505584716797, "Text": "Information.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05540919303894043, "Height": 0.011377464979887009, "Left": 0.26457521319389343, "Top": 0.2641872465610504}, "Polygon": [{"X": 0.2645752727985382, "Y": 0.2641873061656952}, {"X": 0.31998440623283386, "Y": 0.2641872465610504}, {"X": 0.3199843466281891, "Y": 0.27556464076042175}, {"X": 0.26457521319389343, "Y": 0.27556470036506653}]}, "Id": "30d9d5c5-6c0d-4e1e-8af9-55314b301da3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0426037572324276, "Height": 0.014409230090677738, "Left": 0.05379126965999603, "Top": 0.2955961227416992}, "Polygon": [{"X": 0.053791314363479614, "Y": 0.2955961525440216}, {"X": 0.09639502316713333, "Y": 0.2955961227416992}, {"X": 0.09639497101306915, "Y": 0.3100053071975708}, {"X": 0.05379126965999603, "Y": 0.3100053369998932}]}, "Id": "42668ad6-b32d-4b2f-8c14-704a37623cef"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00795100349932909, "Height": 0.011857853271067142, "Left": 0.10000035166740417, "Top": 0.2954130172729492}, "Polygon": [{"X": 0.10000039637088776, "Y": 0.2954130172729492}, {"X": 0.10795135796070099, "Y": 0.2954130172729492}, {"X": 0.10795131325721741, "Y": 0.3072708547115326}, {"X": 0.10000035166740417, "Y": 0.307270884513855}]}, "Id": "a4637424-2d93-4054-966d-1763dd7198f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "captured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04438886046409607, "Height": 0.014289693906903267, "Left": 0.1112980991601944, "Top": 0.29556185007095337}, "Polygon": [{"X": 0.11129815131425858, "Y": 0.29556187987327576}, {"X": 0.15568695962429047, "Y": 0.29556185007095337}, {"X": 0.1556869000196457, "Y": 0.3098514974117279}, {"X": 0.1112980991601944, "Y": 0.3098515272140503}]}, "Id": "7314b7b1-3999-4e80-bd29-65f695881464"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012394677847623825, "Height": 0.008766267448663712, "Left": 0.15912950038909912, "Top": 0.2982921302318573}, "Polygon": [{"X": 0.1591295301914215, "Y": 0.2982921600341797}, {"X": 0.17152418196201324, "Y": 0.2982921302318573}, {"X": 0.17152413725852966, "Y": 0.3070583939552307}, {"X": 0.15912950038909912, "Y": 0.3070583939552307}]}, "Id": "7530bd5b-a957-4bef-92e4-16fc65da598d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "recorded", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04493344575166702, "Height": 0.011552850715816021, "Left": 0.1752087026834488, "Top": 0.29552263021469116}, "Polygon": [{"X": 0.17520874738693237, "Y": 0.29552266001701355}, {"X": 0.2201421558856964, "Y": 0.29552263021469116}, {"X": 0.22014209628105164, "Y": 0.3070754110813141}, {"X": 0.1752087026834488, "Y": 0.30707547068595886}]}, "Id": "3c8e8958-39d7-4fb3-ba21-1137e4dd5721"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03679768368601799, "Height": 0.014461198821663857, "Left": 0.2238202840089798, "Top": 0.29557716846466064}, "Polygon": [{"X": 0.22382034361362457, "Y": 0.2955772280693054}, {"X": 0.2606179714202881, "Y": 0.29557716846466064}, {"X": 0.2606178820133209, "Y": 0.31003832817077637}, {"X": 0.2238202840089798, "Y": 0.31003838777542114}]}, "Id": "83d50a40-6890-40e9-9f70-dd0d888e3385"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009800614789128304, "Height": 0.011231329292058945, "Left": 0.2639021873474121, "Top": 0.2960110902786255}, "Polygon": [{"X": 0.2639022469520569, "Y": 0.2960111200809479}, {"X": 0.27370280027389526, "Y": 0.2960110902786255}, {"X": 0.2737027406692505, "Y": 0.30724242329597473}, {"X": 0.2639021873474121, "Y": 0.30724242329597473}]}, "Id": "e277e3d9-9854-4888-81ff-5d90e0d86208"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AGS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02413157746195793, "Height": 0.011821980588138103, "Left": 0.2758614718914032, "Top": 0.29544147849082947}, "Polygon": [{"X": 0.275861531496048, "Y": 0.29544150829315186}, {"X": 0.2999930679798126, "Y": 0.29544147849082947}, {"X": 0.29999300837516785, "Y": 0.30726343393325806}, {"X": 0.2758614718914032, "Y": 0.30726346373558044}]}, "Id": "f4d393ad-3b23-4e6c-8605-f64756455fe2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "locations", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045113492757081985, "Height": 0.011587720364332199, "Left": 0.3035762906074524, "Top": 0.29560402035713196}, "Polygon": [{"X": 0.30357635021209717, "Y": 0.29560407996177673}, {"X": 0.3486897945404053, "Y": 0.29560402035713196}, {"X": 0.3486897349357605, "Y": 0.3071916997432709}, {"X": 0.3035762906074524, "Y": 0.30719172954559326}]}, "Id": "2feba163-b04d-4f61-a398-3c3459fcd20d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03203519061207771, "Height": 0.011185559444129467, "Left": 0.05278763547539711, "Top": 0.33884432911872864}, "Polygon": [{"X": 0.052787669003009796, "Y": 0.338844358921051}, {"X": 0.08482282608747482, "Y": 0.33884432911872864}, {"X": 0.08482278138399124, "Y": 0.350029855966568}, {"X": 0.05278763547539711, "Y": 0.3500298857688904}]}, "Id": "1c1cd3cd-8ec8-45e9-8727-a7e5adf805ec"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "note", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02063317969441414, "Height": 0.01035947073251009, "Left": 0.08794446289539337, "Top": 0.33955779671669006}, "Polygon": [{"X": 0.08794450014829636, "Y": 0.33955779671669006}, {"X": 0.10857764631509781, "Y": 0.33955779671669006}, {"X": 0.10857760161161423, "Y": 0.3499172329902649}, {"X": 0.08794446289539337, "Y": 0.3499172627925873}]}, "Id": "ae4fa0e4-bc36-4f9f-8828-8350d083134b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018249228596687317, "Height": 0.010857578366994858, "Left": 0.11145218461751938, "Top": 0.3390399217605591}, "Polygon": [{"X": 0.11145222932100296, "Y": 0.3390399217605591}, {"X": 0.1297014206647873, "Y": 0.3390399217605591}, {"X": 0.1297013759613037, "Y": 0.34989747405052185}, {"X": 0.11145218461751938, "Y": 0.34989750385284424}]}, "Id": "03742789-8ff4-496a-80c1-d6c96d03e695"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016769757494330406, "Height": 0.011052013374865055, "Left": 0.13215994834899902, "Top": 0.3388977646827698}, "Polygon": [{"X": 0.1321599930524826, "Y": 0.33889779448509216}, {"X": 0.14892970025539398, "Y": 0.3388977646827698}, {"X": 0.1489296555519104, "Y": 0.34994977712631226}, {"X": 0.13215994834899902, "Y": 0.34994977712631226}]}, "Id": "7d02f29d-e7ae-452c-90ef-8e55792f5b5b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "summary", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04409365728497505, "Height": 0.011299578472971916, "Left": 0.15193375945091248, "Top": 0.34132087230682373}, "Polygon": [{"X": 0.15193380415439606, "Y": 0.3413209021091461}, {"X": 0.19602741301059723, "Y": 0.34132087230682373}, {"X": 0.19602736830711365, "Y": 0.352620393037796}, {"X": 0.15193375945091248, "Y": 0.3526204228401184}]}, "Id": "8a2ee902-f507-42d2-ae12-c78e0a986985"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009715056046843529, "Height": 0.01123653631657362, "Left": 0.19868066906929016, "Top": 0.3387032449245453}, "Polygon": [{"X": 0.19868071377277374, "Y": 0.3387032747268677}, {"X": 0.20839571952819824, "Y": 0.3387032449245453}, {"X": 0.20839567482471466, "Y": 0.3499397933483124}, {"X": 0.19868066906929016, "Y": 0.3499397933483124}]}, "Id": "36178654-6481-4250-9063-789a3c8b5ff7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "data", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020457308739423752, "Height": 0.011037392541766167, "Left": 0.21062052249908447, "Top": 0.3389481008052826}, "Polygon": [{"X": 0.21062056720256805, "Y": 0.338948130607605}, {"X": 0.23107783496379852, "Y": 0.3389481008052826}, {"X": 0.23107777535915375, "Y": 0.3499854803085327}, {"X": 0.21062052249908447, "Y": 0.3499855101108551}]}, "Id": "9294d059-efec-4edf-95dd-4bc1faf04f57"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007480643689632416, "Height": 0.01092311181128025, "Left": 0.23437999188899994, "Top": 0.33896008133888245}, "Polygon": [{"X": 0.23438003659248352, "Y": 0.33896008133888245}, {"X": 0.24186062812805176, "Y": 0.33896008133888245}, {"X": 0.24186058342456818, "Y": 0.34988319873809814}, {"X": 0.23437999188899994, "Y": 0.34988319873809814}]}, "Id": "ddea734e-f59d-4ce9-a7c7-90a5e45b27df"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "provided", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04055178165435791, "Height": 0.01373172178864479, "Left": 0.24507182836532593, "Top": 0.3389144241809845}, "Polygon": [{"X": 0.2450718879699707, "Y": 0.3389144539833069}, {"X": 0.28562361001968384, "Y": 0.3389144241809845}, {"X": 0.2856235206127167, "Y": 0.3526461124420166}, {"X": 0.24507182836532593, "Y": 0.352646142244339}]}, "Id": "0b1cf8e7-38a7-43e6-b086-5e8d3e8ae065"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012930330820381641, "Height": 0.010958907194435596, "Left": 0.28864699602127075, "Top": 0.33883190155029297}, "Polygon": [{"X": 0.2886470556259155, "Y": 0.33883190155029297}, {"X": 0.3015773296356201, "Y": 0.33883190155029297}, {"X": 0.30157727003097534, "Y": 0.3497907817363739}, {"X": 0.28864699602127075, "Y": 0.3497908115386963}]}, "Id": "d8f82f54-6571-4865-899e-3df9e91a3340"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021655969321727753, "Height": 0.011515317484736443, "Left": 0.30388301610946655, "Top": 0.3413067162036896}, "Polygon": [{"X": 0.30388307571411133, "Y": 0.34130674600601196}, {"X": 0.3255389928817749, "Y": 0.3413067162036896}, {"X": 0.3255389332771301, "Y": 0.3528220057487488}, {"X": 0.30388301610946655, "Y": 0.35282203555107117}]}, "Id": "6548ef9a-46a5-4009-90fa-57199362a39f"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06007632240653038, "Height": 0.010948305949568748, "Left": 0.3278544843196869, "Top": 0.33904096484184265}, "Polygon": [{"X": 0.32785454392433167, "Y": 0.3390410244464874}, {"X": 0.3879307806491852, "Y": 0.33904096484184265}, {"X": 0.3879307210445404, "Y": 0.3499892055988312}, {"X": 0.3278544843196869, "Y": 0.34998926520347595}]}, "Id": "f9232e12-94bf-4b2c-9aa7-1d1d10ea3697"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "only", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019716637209057808, "Height": 0.01386284176260233, "Left": 0.3907807171344757, "Top": 0.33898255228996277}, "Polygon": [{"X": 0.39078080654144287, "Y": 0.33898258209228516}, {"X": 0.41049736738204956, "Y": 0.33898255228996277}, {"X": 0.4104972779750824, "Y": 0.3528454005718231}, {"X": 0.3907807171344757, "Y": 0.3528454005718231}]}, "Id": "ca9e49ec-e23e-43fb-956a-8f4e258b3093"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017513763159513474, "Height": 0.011090259999036789, "Left": 0.41344279050827026, "Top": 0.33892911672592163}, "Polygon": [{"X": 0.41344285011291504, "Y": 0.338929146528244}, {"X": 0.43095654249191284, "Y": 0.33892911672592163}, {"X": 0.43095648288726807, "Y": 0.3500193655490875}, {"X": 0.41344279050827026, "Y": 0.3500193953514099}]}, "Id": "bc9370d3-54c2-4981-ae01-ab9fbc79927f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "should", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031365104019641876, "Height": 0.011133178137242794, "Left": 0.4340410530567169, "Top": 0.33873337507247925}, "Polygon": [{"X": 0.4340411424636841, "Y": 0.33873340487480164}, {"X": 0.4654061794281006, "Y": 0.33873337507247925}, {"X": 0.4654060900211334, "Y": 0.34986650943756104}, {"X": 0.4340410530567169, "Y": 0.3498665392398834}]}, "Id": "fcaccda8-052f-43d9-a403-77e25a91de32"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014972425997257233, "Height": 0.010362320579588413, "Left": 0.46857643127441406, "Top": 0.33958035707473755}, "Polygon": [{"X": 0.46857649087905884, "Y": 0.33958035707473755}, {"X": 0.4835488498210907, "Y": 0.33958035707473755}, {"X": 0.4835487902164459, "Y": 0.3499426543712616}, {"X": 0.46857643127441406, "Y": 0.349942684173584}]}, "Id": "58d2ada8-3a7c-49af-a083-4bf219bb5af5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "replace", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034538932144641876, "Height": 0.013592076487839222, "Left": 0.48643216490745544, "Top": 0.3390738070011139}, "Polygon": [{"X": 0.4864322543144226, "Y": 0.3390738368034363}, {"X": 0.5209711194038391, "Y": 0.3390738070011139}, {"X": 0.5209710001945496, "Y": 0.35266584157943726}, {"X": 0.48643216490745544, "Y": 0.35266587138175964}]}, "Id": "934da849-2d29-490a-acb6-599ba0055ad5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "your", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02132435142993927, "Height": 0.011432678438723087, "Left": 0.5240663290023804, "Top": 0.34136319160461426}, "Polygon": [{"X": 0.5240663886070251, "Y": 0.34136322140693665}, {"X": 0.5453906655311584, "Y": 0.34136319160461426}, {"X": 0.5453905463218689, "Y": 0.3527958393096924}, {"X": 0.5240663290023804, "Y": 0.35279586911201477}]}, "Id": "66f00d3d-bc7c-4f82-a495-8cbcb14114ae"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "own", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019170941784977913, "Height": 0.008380378596484661, "Left": 0.5478779673576355, "Top": 0.3414839506149292}, "Polygon": [{"X": 0.5478780269622803, "Y": 0.3414839804172516}, {"X": 0.5670489072799683, "Y": 0.3414839506149292}, {"X": 0.5670488476753235, "Y": 0.34986430406570435}, {"X": 0.5478779673576355, "Y": 0.34986433386802673}]}, "Id": "8ec59ee6-08cf-4aff-b4b9-84f218a4d770"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "system", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03339048847556114, "Height": 0.013059287331998348, "Left": 0.5704398155212402, "Top": 0.33958694338798523}, "Polygon": [{"X": 0.5704399347305298, "Y": 0.3395869731903076}, {"X": 0.6038303375244141, "Y": 0.33958694338798523}, {"X": 0.6038302183151245, "Y": 0.35264620184898376}, {"X": 0.5704398155212402, "Y": 0.35264623165130615}]}, "Id": "22fcd416-2b05-4238-91b1-11f96e3c9019"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009546400979161263, "Height": 0.011155878193676472, "Left": 0.6070101857185364, "Top": 0.3387536108493805}, "Polygon": [{"X": 0.6070102453231812, "Y": 0.3387536406517029}, {"X": 0.6165565848350525, "Y": 0.3387536108493805}, {"X": 0.6165564656257629, "Y": 0.3499094843864441}, {"X": 0.6070101857185364, "Y": 0.3499094843864441}]}, "Id": "1a3fb4f0-b8ca-4d2c-b985-23a657ca9c6b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "record", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02983464114367962, "Height": 0.010881899856030941, "Left": 0.619131326675415, "Top": 0.3390001356601715}, "Polygon": [{"X": 0.6191313862800598, "Y": 0.3390001654624939}, {"X": 0.6489659547805786, "Y": 0.3390001356601715}, {"X": 0.6489658355712891, "Y": 0.34988200664520264}, {"X": 0.619131326675415, "Y": 0.349882036447525}]}, "Id": "8b9e687b-ca45-47fa-a487-b3bf72718c4d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "keeping.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039554402232170105, "Height": 0.013918501324951649, "Left": 0.6521974205970764, "Top": 0.33890146017074585}, "Polygon": [{"X": 0.652197539806366, "Y": 0.3389015197753906}, {"X": 0.6917517781257629, "Y": 0.33890146017074585}, {"X": 0.6917516589164734, "Y": 0.35281994938850403}, {"X": 0.6521974205970764, "Y": 0.3528199791908264}]}, "Id": "31c0c72e-418f-445d-8b8e-c3c5f74227bd"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "CSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022152360528707504, "Height": 0.011149910278618336, "Left": 0.6956069469451904, "Top": 0.33869946002960205}, "Polygon": [{"X": 0.69560706615448, "Y": 0.33869946002960205}, {"X": 0.7177593111991882, "Y": 0.33869946002960205}, {"X": 0.7177591919898987, "Y": 0.3498493432998657}, {"X": 0.6956069469451904, "Y": 0.3498493432998657}]}, "Id": "cb78144b-8ab2-42ec-b7ab-51664fc06267"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "does", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02260114625096321, "Height": 0.011025864630937576, "Left": 0.7204782962799072, "Top": 0.3389199376106262}, "Polygon": [{"X": 0.720478355884552, "Y": 0.3389199674129486}, {"X": 0.743079423904419, "Y": 0.3389199376106262}, {"X": 0.7430793046951294, "Y": 0.3499457836151123}, {"X": 0.7204782962799072, "Y": 0.3499458134174347}]}, "Id": "20372e06-3040-475f-aa02-d14441e94f3e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014580649323761463, "Height": 0.010356344282627106, "Left": 0.7465335130691528, "Top": 0.339465469121933}, "Polygon": [{"X": 0.7465336322784424, "Y": 0.33946549892425537}, {"X": 0.7611141800880432, "Y": 0.339465469121933}, {"X": 0.7611140608787537, "Y": 0.3498218059539795}, {"X": 0.7465335130691528, "Y": 0.3498218357563019}]}, "Id": "94a844da-466d-4c44-ad88-39c48336a07c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "warrant", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036111991852521896, "Height": 0.010408413596451283, "Left": 0.7639033198356628, "Top": 0.33953455090522766}, "Polygon": [{"X": 0.7639034390449524, "Y": 0.33953458070755005}, {"X": 0.8000153303146362, "Y": 0.33953455090522766}, {"X": 0.8000152111053467, "Y": 0.3499429225921631}, {"X": 0.7639033198356628, "Y": 0.3499429523944855}]}, "Id": "8356e289-22f7-4769-8c18-395f11ec19c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01460289303213358, "Height": 0.010737129487097263, "Left": 0.8024239540100098, "Top": 0.3390328586101532}, "Polygon": [{"X": 0.8024240732192993, "Y": 0.3390328586101532}, {"X": 0.8170268535614014, "Y": 0.3390328586101532}, {"X": 0.8170267343521118, "Y": 0.3497699797153473}, {"X": 0.8024239540100098, "Y": 0.3497699797153473}]}, "Id": "51213034-fe44-4096-b4d1-9bbda70<PERSON><PERSON>"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "accurateness", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.063571497797966, "Height": 0.010674454271793365, "Left": 0.8200756311416626, "Top": 0.33929377794265747}, "Polygon": [{"X": 0.8200757503509521, "Y": 0.33929383754730225}, {"X": 0.8836471438407898, "Y": 0.33929377794265747}, {"X": 0.8836470246315002, "Y": 0.34996816515922546}, {"X": 0.8200756311416626, "Y": 0.34996822476387024}]}, "Id": "9a390c19-5114-4278-be72-e8c783cd55d4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00940515287220478, "Height": 0.011244775727391243, "Left": 0.886656641960144, "Top": 0.33865872025489807}, "Polygon": [{"X": 0.8866567611694336, "Y": 0.33865872025489807}, {"X": 0.8960617780685425, "Y": 0.33865872025489807}, {"X": 0.8960616588592529, "Y": 0.34990349411964417}, {"X": 0.886656641960144, "Y": 0.34990349411964417}]}, "Id": "1aefa437-0abf-4a11-9646-96e0e9e123ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014779945835471153, "Height": 0.01075665932148695, "Left": 0.8984984159469604, "Top": 0.33905085921287537}, "Polygon": [{"X": 0.89849853515625, "Y": 0.33905088901519775}, {"X": 0.9132784008979797, "Y": 0.33905085921287537}, {"X": 0.9132782816886902, "Y": 0.3498075008392334}, {"X": 0.8984984159469604, "Y": 0.3498075306415558}]}, "Id": "7af490e3-8270-43bc-a8b3-8356b7542e9f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "data", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02052665688097477, "Height": 0.010985915549099445, "Left": 0.916530430316925, "Top": 0.33895596861839294}, "Polygon": [{"X": 0.9165304899215698, "Y": 0.33895599842071533}, {"X": 0.9370570778846741, "Y": 0.33895596861839294}, {"X": 0.9370569586753845, "Y": 0.3499418795108795}, {"X": 0.916530430316925, "Y": 0.3499418795108795}]}, "Id": "051a6aae-f19c-46fd-8775-9e220a4a4ab2"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "nor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0154563682153821, "Height": 0.008407823741436005, "Left": 0.052717916667461395, "Top": 0.3570459485054016}, "Polygon": [{"X": 0.05271794646978378, "Y": 0.3570459485054016}, {"X": 0.06817428767681122, "Y": 0.3570459485054016}, {"X": 0.06817425787448883, "Y": 0.3654537498950958}, {"X": 0.052717916667461395, "Y": 0.3654537498950958}]}, "Id": "8ac2bcfc-4ded-4a80-8042-94a7a144c08e"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "its", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010627713054418564, "Height": 0.011115252040326595, "Left": 0.07075989246368408, "Top": 0.3544972538948059}, "Polygon": [{"X": 0.07075992971658707, "Y": 0.3544972538948059}, {"X": 0.08138760924339294, "Y": 0.3544972538948059}, {"X": 0.08138757199048996, "Y": 0.3656124770641327}, {"X": 0.07075989246368408, "Y": 0.3656125068664551}]}, "Id": "f20b0cc1-b7ad-483b-a19e-643f756c1ccc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "availability", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04927590489387512, "Height": 0.013981631025671959, "Left": 0.0844339057803154, "Top": 0.354560524225235}, "Polygon": [{"X": 0.08443395793437958, "Y": 0.3545605540275574}, {"X": 0.13370981812477112, "Y": 0.354560524225235}, {"X": 0.13370975852012634, "Y": 0.3685421049594879}, {"X": 0.0844339057803154, "Y": 0.3685421645641327}]}, "Id": "a35488c7-cdbe-4232-8146-befa63c95357"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013083362020552158, "Height": 0.011203961446881294, "Left": 0.136255145072937, "Top": 0.35436511039733887}, "Polygon": [{"X": 0.1362551897764206, "Y": 0.35436514019966125}, {"X": 0.1493385136127472, "Y": 0.35436511039733887}, {"X": 0.1493384689092636, "Y": 0.3655690848827362}, {"X": 0.136255145072937, "Y": 0.3655690848827362}]}, "Id": "05c5ab42-308f-4d20-b2c7-43a885f7f9cb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017320025712251663, "Height": 0.011157029308378696, "Left": 0.15200534462928772, "Top": 0.3571931719779968}, "Polygon": [{"X": 0.1520053893327713, "Y": 0.3571932017803192}, {"X": 0.16932538151741028, "Y": 0.3571931719779968}, {"X": 0.1693253219127655, "Y": 0.36835020780563354}, {"X": 0.15200534462928772, "Y": 0.36835020780563354}]}, "Id": "fa75f329-6039-41a0-9f1e-0408cf94219c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "particular", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04417135566473007, "Height": 0.013802793808281422, "Left": 0.1722874790430069, "Top": 0.3546745777130127}, "Polygon": [{"X": 0.17228753864765167, "Y": 0.35467463731765747}, {"X": 0.21645884215831757, "Y": 0.3546745777130127}, {"X": 0.2164587825536728, "Y": 0.36847734451293945}, {"X": 0.1722874790430069, "Y": 0.36847737431526184}]}, "Id": "9c1f13df-3208-4c94-88a6-872bfed8c85f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "time.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022737814113497734, "Height": 0.011088517494499683, "Left": 0.21871262788772583, "Top": 0.35458868741989136}, "Polygon": [{"X": 0.2187126725912094, "Y": 0.35458871722221375}, {"X": 0.2414504438638687, "Y": 0.35458868741989136}, {"X": 0.24145038425922394, "Y": 0.36567720770835876}, {"X": 0.21871262788772583, "Y": 0.36567720770835876}]}, "Id": "ae0163c4-3923-4ba1-b6b3-493e73b81620"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01594165712594986, "Height": 0.011068657040596008, "Left": 0.052654024213552475, "Top": 0.3812220096588135}, "Polygon": [{"X": 0.05265405774116516, "Y": 0.38122203946113586}, {"X": 0.06859567761421204, "Y": 0.3812220096588135}, {"X": 0.06859564036130905, "Y": 0.3922906517982483}, {"X": 0.052654024213552475, "Y": 0.3922906816005707}]}, "Id": "b36030c9-1752-438f-9092-71d8c146d25f"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "assistance", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05024005100131035, "Height": 0.011000720784068108, "Left": 0.07144626975059509, "Top": 0.3813016414642334}, "Polygon": [{"X": 0.07144630700349808, "Y": 0.3813016712665558}, {"X": 0.12168632447719574, "Y": 0.3813016414642334}, {"X": 0.12168627977371216, "Y": 0.3923023045063019}, {"X": 0.07144626975059509, "Y": 0.39230236411094666}]}, "Id": "60b2d89c-54dd-4db8-b785-98552dec854a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019146041944622993, "Height": 0.011160292662680149, "Left": 0.12430106103420258, "Top": 0.381154328584671}, "Polygon": [{"X": 0.12430109828710556, "Y": 0.3811543583869934}, {"X": 0.14344710111618042, "Y": 0.381154328584671}, {"X": 0.14344705641269684, "Y": 0.392314612865448}, {"X": 0.12430106103420258, "Y": 0.392314612865448}]}, "Id": "b55b8293-26ea-49c0-8dc4-5630c1f93eda"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04346584901213646, "Height": 0.013748442754149437, "Left": 0.1467466950416565, "Top": 0.38115394115448}, "Polygon": [{"X": 0.14674673974514008, "Y": 0.38115397095680237}, {"X": 0.19021254777908325, "Y": 0.38115394115448}, {"X": 0.19021248817443848, "Y": 0.3949023485183716}, {"X": 0.1467466950416565, "Y": 0.39490237832069397}]}, "Id": "d28d8cbc-fc61-409e-a84a-6a600ee06510"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "problems,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04609900712966919, "Height": 0.013579994440078735, "Left": 0.19300009310245514, "Top": 0.38143476843833923}, "Polygon": [{"X": 0.19300015270709991, "Y": 0.3814347982406616}, {"X": 0.23909910023212433, "Y": 0.38143476843833923}, {"X": 0.23909904062747955, "Y": 0.3950147032737732}, {"X": 0.19300009310245514, "Y": 0.39501476287841797}]}, "Id": "de9c1365-f7f8-44ed-8848-caeff8213afb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "use", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0167499091476202, "Height": 0.008504435420036316, "Left": 0.24277886748313904, "Top": 0.3837624490261078}, "Polygon": [{"X": 0.24277891218662262, "Y": 0.3837624490261078}, {"X": 0.259528785943985, "Y": 0.3837624490261078}, {"X": 0.2595287263393402, "Y": 0.3922668695449829}, {"X": 0.24277886748313904, "Y": 0.3922668695449829}]}, "Id": "7fe4951a-4595-4ac1-86d0-5287e5368a9b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01498857606202364, "Height": 0.010823309421539307, "Left": 0.26260241866111755, "Top": 0.381417840719223}, "Polygon": [{"X": 0.26260247826576233, "Y": 0.3814178705215454}, {"X": 0.2775909900665283, "Y": 0.381417840719223}, {"X": 0.27759093046188354, "Y": 0.39224115014076233}, {"X": 0.26260241866111755, "Y": 0.3922411799430847}]}, "Id": "12bc6391-290b-4e4d-aef4-c925ae62d85c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045020122081041336, "Height": 0.013752796687185764, "Left": 0.280821293592453, "Top": 0.38110533356666565}, "Polygon": [{"X": 0.2808213531970978, "Y": 0.3811053931713104}, {"X": 0.32584139704704285, "Y": 0.38110533356666565}, {"X": 0.32584133744239807, "Y": 0.39485809206962585}, {"X": 0.280821293592453, "Y": 0.39485812187194824}]}, "Id": "17b7dc7a-69d0-40c5-8431-54ada0fc230e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Problem", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039417896419763565, "Height": 0.011130140163004398, "Left": 0.3287894129753113, "Top": 0.3811226785182953}, "Polygon": [{"X": 0.32878947257995605, "Y": 0.3811227083206177}, {"X": 0.36820730566978455, "Y": 0.3811226785182953}, {"X": 0.36820724606513977, "Y": 0.39225277304649353}, {"X": 0.3287894129753113, "Y": 0.3922528028488159}]}, "Id": "5fdb8e30-6911-46e3-bb17-8a620cb5d16f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Resolution", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04999839887022972, "Height": 0.011088644154369831, "Left": 0.3713071942329407, "Top": 0.3811813294887543}, "Polygon": [{"X": 0.37130725383758545, "Y": 0.38118138909339905}, {"X": 0.4213055968284607, "Y": 0.3811813294887543}, {"X": 0.42130550742149353, "Y": 0.39226993918418884}, {"X": 0.3713071942329407, "Y": 0.3922699987888336}]}, "Id": "502dc47a-41d7-422c-924c-b38a14c3b348"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "tool", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017249993979930878, "Height": 0.010964254848659039, "Left": 0.4242969751358032, "Top": 0.3812897205352783}, "Polygon": [{"X": 0.424297034740448, "Y": 0.3812897205352783}, {"X": 0.4415469765663147, "Y": 0.3812897205352783}, {"X": 0.44154688715934753, "Y": 0.39225396513938904}, {"X": 0.4242969751358032, "Y": 0.39225396513938904}]}, "Id": "94f5ee54-1a09-4284-bbc0-42d964375334"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009075195528566837, "Height": 0.010412984527647495, "Left": 0.44437023997306824, "Top": 0.381820410490036}, "Polygon": [{"X": 0.444370299577713, "Y": 0.381820410490036}, {"X": 0.4534454345703125, "Y": 0.381820410490036}, {"X": 0.4534453749656677, "Y": 0.39223340153694153}, {"X": 0.44437023997306824, "Y": 0.39223340153694153}]}, "Id": "af6953ca-5a0d-4d60-9fca-fe1686d343b7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "contact", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03461429849267006, "Height": 0.010459678247570992, "Left": 0.45661085844039917, "Top": 0.38183319568634033}, "Polygon": [{"X": 0.45661091804504395, "Y": 0.3818332254886627}, {"X": 0.49122515320777893, "Y": 0.38183319568634033}, {"X": 0.49122509360313416, "Y": 0.3922928273677826}, {"X": 0.45661085844039917, "Y": 0.392292857170105}]}, "Id": "f26772b8-7242-443e-8dd1-d903e3aebc45"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04622435197234154, "Height": 0.011167335323989391, "Left": 0.4940696954727173, "Top": 0.3811708688735962}, "Polygon": [{"X": 0.49406975507736206, "Y": 0.38117092847824097}, {"X": 0.5402940511703491, "Y": 0.3811708688735962}, {"X": 0.5402939319610596, "Y": 0.39233818650245667}, {"X": 0.4940696954727173, "Y": 0.39233821630477905}]}, "Id": "aab7475e-6f7a-46bb-b210-9a92a7a454b4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03521396219730377, "Height": 0.011277775280177593, "Left": 0.5431973338127136, "Top": 0.3809858560562134}, "Polygon": [{"X": 0.5431973934173584, "Y": 0.38098588585853577}, {"X": 0.5784112811088562, "Y": 0.3809858560562134}, {"X": 0.5784111618995667, "Y": 0.39226359128952026}, {"X": 0.5431973338127136, "Y": 0.39226362109184265}]}, "Id": "393cb604-cc99-40b8-91d8-19205cfeb323"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009916693903505802, "Height": 0.008507177233695984, "Left": 0.581623375415802, "Top": 0.3837030529975891}, "Polygon": [{"X": 0.5816234350204468, "Y": 0.3837030529975891}, {"X": 0.5915400981903076, "Y": 0.3837030529975891}, {"X": 0.5915399789810181, "Y": 0.3922102153301239}, {"X": 0.581623375415802, "Y": 0.3922102153301239}]}, "Id": "fe9e294c-63c0-44c3-a4da-bceef8b175c3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "call", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015514385886490345, "Height": 0.010971267707645893, "Left": 0.5942073464393616, "Top": 0.38127705454826355}, "Polygon": [{"X": 0.5942074060440063, "Y": 0.38127705454826355}, {"X": 0.6097217202186584, "Y": 0.38127705454826355}, {"X": 0.6097216010093689, "Y": 0.3922483026981354}, {"X": 0.5942073464393616, "Y": 0.39224833250045776}]}, "Id": "efc05d84-1059-488f-9e83-565f51a57993"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "800-542-2754", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06656184792518616, "Height": 0.01124971080571413, "Left": 0.6129195094108582, "Top": 0.38127049803733826}, "Polygon": [{"X": 0.6129196286201477, "Y": 0.38127052783966064}, {"X": 0.6794813871383667, "Y": 0.38127049803733826}, {"X": 0.6794812679290771, "Y": 0.39252012968063354}, {"X": 0.6129195094108582, "Y": 0.3925201892852783}]}, "Id": "a1ba3bc6-0293-483e-b471-50836f0c1097"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "[Quoted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02913292869925499, "Height": 0.010966754518449306, "Left": 0.05238009989261627, "Top": 0.39930903911590576}, "Polygon": [{"X": 0.05238013342022896, "Y": 0.39930906891822815}, {"X": 0.08151302486658096, "Y": 0.39930903911590576}, {"X": 0.08151298761367798, "Y": 0.41027578711509705}, {"X": 0.05238009989261627, "Y": 0.41027578711509705}]}, "Id": "79415616-6345-4289-a06f-ecd2ec5576c4"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "text", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01371832750737667, "Height": 0.0084412582218647, "Left": 0.08363275974988937, "Top": 0.39997056126594543}, "Polygon": [{"X": 0.08363278955221176, "Y": 0.39997056126594543}, {"X": 0.0973510891199112, "Y": 0.39997056126594543}, {"X": 0.0973510593175888, "Y": 0.40841180086135864}, {"X": 0.08363275974988937, "Y": 0.40841180086135864}]}, "Id": "7e287d6e-7b2d-4f07-8a2b-b401e9ff69f2"}, {"BlockType": "WORD", "Confidence": 99.6045913696289, "Text": "hidden]", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026894137263298035, "Height": 0.010968177579343319, "Left": 0.0993807390332222, "Top": 0.3993799686431885}, "Polygon": [{"X": 0.09938077628612518, "Y": 0.39937999844551086}, {"X": 0.12627488374710083, "Y": 0.3993799686431885}, {"X": 0.12627483904361725, "Y": 0.41034814715385437}, {"X": 0.0993807390332222, "Y": 0.41034814715385437}]}, "Id": "1638591d-2c06-4c04-904b-6d75d09ca039"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "9aba44db-623e-4e8c-9392-8c00ba4a8e9d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "9aba44db-623e-4e8c-9392-8c00ba4a8e9d", "content-type": "application/x-amz-json-1.1", "content-length": "78024", "date": "Thu, 18 Sep 2025 18:07:13 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "6fa9cbe0-fcaf-41ca-be54-98c04c34a8b9", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 18:07:15 GMT", "content-type": "application/json", "content-length": "1157", "connection": "keep-alive", "x-amzn-requestid": "6fa9cbe0-fcaf-41ca-be54-98c04c34a8b9"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have one page. We must classify. The page is about equipment arrival event. It includes equipment ID, terminal, driver info, etc. Looks like a gate-in or ingate document? Let's check keywords: \"Equipment Information\", \"Event Information\", \"Activity: Arrival\" indicates a gate-in event. The fields: \"Equipment ID\", \"Terminal\", \"Driver Information\", \"SCAC Code\", \"Seal Number\". This is typical ingate. The doc type \"ingate\" matches. The system says we should use the tool to return classification.\n\nThus we need to call function classify_logistics_doc_type with documents array containing page 1 with doc_type \"ingate\".\n\nAlso we must include page_no. So pass array with one object: {page_no: 1, doc_type:\"ingate\"}.\n\nReturn that."}}}, {"toolUse": {"toolUseId": "tooluse_Ou_jmC9sTY-bsI6op0MCxA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2358, "outputTokens": 206, "totalTokens": 2564}, "metrics": {"latencyMs": 985}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/77e6ca6a_25_ingate_2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nEquipment Information\nEvent Information\nEquipment ID: MRSU 440762\nTerminal: CH<PERSON>AGO 59TH ST\nAssociated Equipment ID: SSRT 832122\nDate and Time: 07/08/2025 20:17\nL/E Status: Load\nActivity: Arrival\nLength: 40'\nDrayage Co: SUPER STAR FREIGHT INC\nType: CN\nDriver Information: MURPHY, JOSHUA\nSCAC Code: SSRT\nSeal Number: 00000\nBooking Number:\nInspection Information:\nPlease refer ShipCSX images for Inspection Information.\nDamage is captured on recorded images at AGS locations\nPlease note that this summary of data is provided for your convenience only and should not replace your own system of record keeping. CSX does not warrant the accurateness of the data\nnor its availability for any particular time.\nFor assistance with shipment problems, use the Shipment Problem Resolution tool to contact Customer Service or call 800-542-2754\n[Quoted text hidden]\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}