{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9999728202819824, "Height": 0.9999380111694336, "Left": 2.718538962653838e-05, "Top": 6.199473864398897e-05}, "Polygon": [{"X": 2.718538962653838e-05, "Y": 7.449216354871169e-05}, {"X": 1.0, "Y": 6.199473864398897e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0001409734832122922, "Y": 1.0}]}, "Id": "a18c39bd-9eff-458c-ba66-6b9aec733483", "Relationships": [{"Type": "CHILD", "Ids": ["4b92f5fb-7b25-4fbd-b0c0-8cf9e966f21b", "81564b79-6e4d-45b4-95da-125be4a2223c", "9cc297e7-0509-46e1-88f9-708e96950cf4", "38e357a3-df94-47f8-aabd-1adf48236c4c", "870ade41-591f-45a1-bdf0-6223f2ba72f5", "1a80501e-ffb2-4679-b346-484fa3a3042f", "63c88c5e-211f-44e3-82b6-ff44b0ab23d9", "a59e89d7-0df3-48aa-ae5b-057770f1dabf", "3dd4cc32-2e45-4dea-b9f8-374f71e31fa9", "dbc8fd74-45c1-4aa4-b2b7-b8d116cad346", "8054509b-9bf9-4bd6-b29c-85861996466e", "3125c0d5-2d47-446e-af40-5b635b31e318", "9cfe6482-57c8-4517-8842-6de2e72b04d6", "91be50fb-6a8c-49d5-bdbf-0cb016bcc7d4", "918ccd69-5d1d-49a0-90e1-68391baccd03", "90d37a4c-3e5f-429b-88ba-85051dd8e2d6", "52f1213e-0d5a-43e6-bc36-bb877f5003f4", "9f88c403-1072-490d-9343-c1be6f20f6d8", "6530e1bd-8843-42b3-86d5-966444127175", "78eeafc9-8b4f-4ecc-9538-caab9b1be681", "ab7c9917-9848-48e6-a5f0-cef4f122e7dd", "1c3b0b91-425e-4037-94ec-cd4b231d1fed", "cb2581ac-91bd-48e2-97b1-32ebc8088449", "e37638ef-c556-4970-a5f0-ae355a90633c", "e462609b-dc10-40c7-83ed-8eef7d1bd28f", "edc0520f-fbe5-40b8-9334-c74b83820232", "42bbcd66-1fe1-4656-9286-4d39bc7f4a7e", "a5d9edb4-9b92-435b-a692-9943153bae65", "1423860c-cccf-4e62-b088-16b6e7ed910d", "a055716d-5d03-4a62-9971-15924d7ad8cc", "63d8efc2-50e5-4aca-aa35-2934c6dd6234", "9f15b8e8-501e-4aa8-b544-32e1f412aa3c", "0f5821ac-9bd0-4d1f-a414-383292719322", "a4433c0f-03a2-4a13-9eb2-6e7cf289b262", "b0e4370a-0673-4648-9686-28d16d8227fc", "f23ee84a-1b39-4fa3-bd28-98f4822e4b3f", "4c295728-36d8-4fdf-8f59-db18f8d3560f", "7c7b05f8-7f61-48d4-844e-d3992f4a4f8c", "b16ec18a-308e-4e6b-b33b-0bd0df26aff3", "eb90badf-af66-46ec-b5d5-c42b687e4649", "e6223d4a-3858-406d-8c65-48c88362eb79", "7165cff1-0a01-4bb3-bc3c-c709b158d086", "0b99309a-531d-43a9-a61a-ffd2d83e0c1c", "3722f7fc-51bc-4221-a673-94b8f4f9e84f", "e8739d91-f7a4-4906-b156-4868ff851d8c", "b74f28ec-2844-4674-bdc5-83551c1a2c82", "7e41aefb-6606-4d9a-83f8-be0ca4b49cc1", "6fd1878e-0e3a-4068-9a94-48506251a03f", "37a6a54b-549d-4d3b-ab20-a13d76ca1ad9"]}]}, {"BlockType": "LINE", "Confidence": 98.26909637451172, "Text": "MERIT", "Geometry": {"BoundingBox": {"Width": 0.24773530662059784, "Height": 0.030968578532338142, "Left": 0.18507200479507446, "Top": 0.1073952466249466}, "Polygon": [{"X": 0.18507200479507446, "Y": 0.10739777982234955}, {"X": 0.43280428647994995, "Y": 0.1073952466249466}, {"X": 0.4328072965145111, "Y": 0.13836143910884857}, {"X": 0.1850752979516983, "Y": 0.1383638232946396}]}, "Id": "4b92f5fb-7b25-4fbd-b0c0-8cf9e966f21b", "Relationships": [{"Type": "CHILD", "Ids": ["a0f013b4-b6ca-47d6-9370-4c5ff264dcfd"]}]}, {"BlockType": "LINE", "Confidence": 57.2064323425293, "Text": "LOGISTIC", "Geometry": {"BoundingBox": {"Width": 0.3510882556438446, "Height": 0.03660334646701813, "Left": 0.5848082900047302, "Top": 0.0968146026134491}, "Polygon": [{"X": 0.5848082900047302, "Y": 0.09681828320026398}, {"X": 0.9358936548233032, "Y": 0.0968146026134491}, {"X": 0.9358965158462524, "Y": 0.13341455161571503}, {"X": 0.5848116278648376, "Y": 0.13341794908046722}]}, "Id": "81564b79-6e4d-45b4-95da-125be4a2223c", "Relationships": [{"Type": "CHILD", "Ids": ["6dfaee41-57d8-45bd-b753-50d1b710c7f0"]}]}, {"BlockType": "LINE", "Confidence": 97.59825134277344, "Text": "(949) 481-0685", "Geometry": {"BoundingBox": {"Width": 0.3080807328224182, "Height": 0.022024720907211304, "Left": 0.34846240282058716, "Top": 0.17896682024002075}, "Polygon": [{"X": 0.34846240282058716, "Y": 0.17896951735019684}, {"X": 0.6565411686897278, "Y": 0.17896682024002075}, {"X": 0.6565431356430054, "Y": 0.2009889930486679}, {"X": 0.34846460819244385, "Y": 0.20099154114723206}]}, "Id": "9cc297e7-0509-46e1-88f9-708e96950cf4", "Relationships": [{"Type": "CHILD", "Ids": ["684613e5-be85-4709-be6a-7d851a471b8e", "a5f686e6-3e30-4f21-9840-a50e06148951"]}]}, {"BlockType": "LINE", "Confidence": 99.15582275390625, "Text": "<PERSON><PERSON> (ALDFRE), <PERSON>, <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.6641154289245605, "Height": 0.023905981332063675, "Left": 0.17080968618392944, "Top": 0.21789641678333282}, "Polygon": [{"X": 0.17080968618392944, "Y": 0.21790169179439545}, {"X": 0.8349231481552124, "Y": 0.21789641678333282}, {"X": 0.83492511510849, "Y": 0.24179746210575104}, {"X": 0.17081224918365479, "Y": 0.2418024092912674}]}, "Id": "38e357a3-df94-47f8-aabd-1adf48236c4c", "Relationships": [{"Type": "CHILD", "Ids": ["4c4d769d-1e3f-4cbd-aa41-6bbf529ab3fa", "3dc007db-446d-401f-be87-b4dd436b9295", "1646d2d7-efc7-4f85-a13e-127256ae9723", "214460cf-6bc3-48a3-b09d-f78ba4d175de"]}]}, {"BlockType": "LINE", "Confidence": 97.20553588867188, "Text": "Tax Id", "Geometry": {"BoundingBox": {"Width": 0.10942088067531586, "Height": 0.012472054921090603, "Left": 0.14416749775409698, "Top": 0.27878475189208984}, "Polygon": [{"X": 0.14416749775409698, "Y": 0.27878549695014954}, {"X": 0.2535870671272278, "Y": 0.27878475189208984}, {"X": 0.25358837842941284, "Y": 0.2912561297416687}, {"X": 0.14416883885860443, "Y": 0.2912568151950836}]}, "Id": "870ade41-591f-45a1-bdf0-6223f2ba72f5", "Relationships": [{"Type": "CHILD", "Ids": ["880cf77b-fbec-4ffa-8fc0-059d11c5017f", "0bb7e0c7-1ff9-4275-9f57-7359baf509a9"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "*********", "Geometry": {"BoundingBox": {"Width": 0.19964005053043365, "Height": 0.01619478315114975, "Left": 0.6647797226905823, "Top": 0.2744753062725067}, "Polygon": [{"X": 0.6647797226905823, "Y": 0.27447664737701416}, {"X": 0.8644184470176697, "Y": 0.2744753062725067}, {"X": 0.8644197583198547, "Y": 0.2906688153743744}, {"X": 0.6647811532020569, "Y": 0.29067009687423706}]}, "Id": "1a80501e-ffb2-4679-b346-484fa3a3042f", "Relationships": [{"Type": "CHILD", "Ids": ["23a59e62-72d4-424f-8c89-f04f4b6ed0a8"]}]}, {"BlockType": "LINE", "Confidence": 92.99844360351562, "Text": "WO #", "Geometry": {"BoundingBox": {"Width": 0.0932382270693779, "Height": 0.012204968370497227, "Left": 0.4134569764137268, "Top": 0.2944173514842987}, "Polygon": [{"X": 0.4134569764137268, "Y": 0.29441794753074646}, {"X": 0.5066940784454346, "Y": 0.2944173514842987}, {"X": 0.5066952109336853, "Y": 0.3066217303276062}, {"X": 0.4134581685066223, "Y": 0.30662232637405396}]}, "Id": "63c88c5e-211f-44e3-82b6-ff44b0ab23d9", "Relationships": [{"Type": "CHILD", "Ids": ["d17a240b-9e1e-41f4-8dac-2d991d4e3009", "2899e01b-162c-4493-b6f0-2ba10329ad36"]}]}, {"BlockType": "LINE", "Confidence": 79.87165832519531, "Text": "ALDFRE9734307252025203958", "Geometry": {"BoundingBox": {"Width": 0.5582346320152283, "Height": 0.016875185072422028, "Left": 0.2240321785211563, "Top": 0.310842365026474}, "Polygon": [{"X": 0.2240321785211563, "Y": 0.3108457028865814}, {"X": 0.7822654247283936, "Y": 0.310842365026474}, {"X": 0.7822668552398682, "Y": 0.32771438360214233}, {"X": 0.22403395175933838, "Y": 0.32771754264831543}]}, "Id": "a59e89d7-0df3-48aa-ae5b-057770f1dabf", "Relationships": [{"Type": "CHILD", "Ids": ["3cb51c25-7360-4eee-b795-bbd9f4b5fcc4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.08533041924238205, "Height": 0.012756140902638435, "Left": 0.14407946169376373, "Top": 0.3356296718120575}, "Polygon": [{"X": 0.14407946169376373, "Y": 0.3356301188468933}, {"X": 0.22940854728221893, "Y": 0.3356296718120575}, {"X": 0.22940988838672638, "Y": 0.34838536381721497}, {"X": 0.14408084750175476, "Y": 0.3483858108520508}]}, "Id": "3dd4cc32-2e45-4dea-b9f8-374f71e31fa9", "Relationships": [{"Type": "CHILD", "Ids": ["4b4e0917-c5ee-4a0e-9cef-1ee921fab428"]}]}, {"BlockType": "LINE", "Confidence": 99.326171875, "Text": "7/25/2025 8:39:00 PM", "Geometry": {"BoundingBox": {"Width": 0.3877936601638794, "Height": 0.017211854457855225, "Left": 0.47627541422843933, "Top": 0.3310616910457611}, "Polygon": [{"X": 0.47627541422843933, "Y": 0.3310638666152954}, {"X": 0.8640676736831665, "Y": 0.3310616910457611}, {"X": 0.8640691041946411, "Y": 0.34827151894569397}, {"X": 0.47627705335617065, "Y": 0.34827354550361633}]}, "Id": "dbc8fd74-45c1-4aa4-b2b7-b8d116cad346", "Relationships": [{"Type": "CHILD", "Ids": ["c963b332-79de-40e7-84d7-f1fd8f487f2a", "3513c9ef-8fb8-4c2a-9ddf-415cd0d8e641", "73d00231-6f3d-46be-816f-2a42023d7e4e"]}]}, {"BlockType": "LINE", "Confidence": 99.2578125, "Text": "PO #", "Geometry": {"BoundingBox": {"Width": 0.08805301785469055, "Height": 0.011958755552768707, "Left": 0.14226120710372925, "Top": 0.35761022567749023}, "Polygon": [{"X": 0.14226120710372925, "Y": 0.35761067271232605}, {"X": 0.23031297326087952, "Y": 0.35761022567749023}, {"X": 0.2303142249584198, "Y": 0.3695685565471649}, {"X": 0.1422625035047531, "Y": 0.36956897377967834}]}, "Id": "8054509b-9bf9-4bd6-b29c-85861996466e", "Relationships": [{"Type": "CHILD", "Ids": ["187d6aec-e6b8-41ad-bdae-344d27dc391e", "57e1a924-98ab-4052-bc66-9b97d072447f"]}]}, {"BlockType": "LINE", "Confidence": 98.6756591796875, "Text": "7507418921", "Geometry": {"BoundingBox": {"Width": 0.21574129164218903, "Height": 0.016964301466941833, "Left": 0.6466652750968933, "Top": 0.3529658615589142}, "Polygon": [{"X": 0.6466652750968933, "Y": 0.35296696424484253}, {"X": 0.8624052405357361, "Y": 0.3529658615589142}, {"X": 0.8624066114425659, "Y": 0.36992913484573364}, {"X": 0.6466668248176575, "Y": 0.3699301779270172}]}, "Id": "3125c0d5-2d47-446e-af40-5b635b31e318", "Relationships": [{"Type": "CHILD", "Ids": ["b154dfe2-a984-405a-afb3-b6aa7ff043de"]}]}, {"BlockType": "LINE", "Confidence": 99.189453125, "Text": "Truck #", "Geometry": {"BoundingBox": {"Width": 0.1336185485124588, "Height": 0.012677724473178387, "Left": 0.14428651332855225, "Top": 0.3771931827068329}, "Polygon": [{"X": 0.14428651332855225, "Y": 0.377193808555603}, {"X": 0.2779037654399872, "Y": 0.3771931827068329}, {"X": 0.27790507674217224, "Y": 0.3898703157901764}, {"X": 0.14428788423538208, "Y": 0.38987091183662415}]}, "Id": "9cfe6482-57c8-4517-8842-6de2e72b04d6", "Relationships": [{"Type": "CHILD", "Ids": ["3f631572-daa8-4686-ba71-5b28b8816e48", "fa8864c7-0ea6-4154-a494-ecca8c8ac498"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "101", "Geometry": {"BoundingBox": {"Width": 0.06180214136838913, "Height": 0.015584289096295834, "Left": 0.8016756772994995, "Top": 0.37275105714797974}, "Polygon": [{"X": 0.8016756772994995, "Y": 0.3727513551712036}, {"X": 0.863476574420929, "Y": 0.37275105714797974}, {"X": 0.8634778261184692, "Y": 0.38833507895469666}, {"X": 0.8016769289970398, "Y": 0.38833534717559814}]}, "Id": "91be50fb-6a8c-49d5-bdbf-0cb016bcc7d4", "Relationships": [{"Type": "CHILD", "Ids": ["49f12429-977b-4335-9288-ad9d9ed4dd23"]}]}, {"BlockType": "LINE", "Confidence": 98.70027160644531, "Text": "Trailer #", "Geometry": {"BoundingBox": {"Width": 0.14712195098400116, "Height": 0.012239117175340652, "Left": 0.14484895765781403, "Top": 0.39901021122932434}, "Polygon": [{"X": 0.14484895765781403, "Y": 0.3990108072757721}, {"X": 0.2919696569442749, "Y": 0.39901021122932434}, {"X": 0.2919709086418152, "Y": 0.41124874353408813}, {"X": 0.14485028386116028, "Y": 0.4112493097782135}]}, "Id": "918ccd69-5d1d-49a0-90e1-68391baccd03", "Relationships": [{"Type": "CHILD", "Ids": ["52619a63-78a7-4b0b-8c6b-c7a04067e9b1", "45ad40e3-7fb1-4f7d-84fb-7155c97ed12d"]}]}, {"BlockType": "LINE", "Confidence": 88.96424865722656, "Text": "()--288", "Geometry": {"BoundingBox": {"Width": 0.12617294490337372, "Height": 0.019286949187517166, "Left": 0.7425933480262756, "Top": 0.39464476704597473}, "Polygon": [{"X": 0.7425933480262756, "Y": 0.3946453034877777}, {"X": 0.8687646985054016, "Y": 0.39464476704597473}, {"X": 0.8687662482261658, "Y": 0.4139312207698822}, {"X": 0.7425949573516846, "Y": 0.4139316976070404}]}, "Id": "90d37a4c-3e5f-429b-88ba-85051dd8e2d6", "Relationships": [{"Type": "CHILD", "Ids": ["036fa178-adb0-4ca6-890f-f38d61af75a6"]}]}, {"BlockType": "LINE", "Confidence": 98.94003295898438, "Text": "Door #", "Geometry": {"BoundingBox": {"Width": 0.11571364104747772, "Height": 0.011846007779240608, "Left": 0.14406432211399078, "Top": 0.4203703999519348}, "Polygon": [{"X": 0.14406432211399078, "Y": 0.42037081718444824}, {"X": 0.2597767412662506, "Y": 0.4203703999519348}, {"X": 0.2597779631614685, "Y": 0.43221598863601685}, {"X": 0.14406560361385345, "Y": 0.4322164058685303}]}, "Id": "52f1213e-0d5a-43e6-bc36-bb877f5003f4", "Relationships": [{"Type": "CHILD", "Ids": ["8c63c056-70bc-419a-90d7-aa9c858bcac4", "f43590a3-95c5-449f-81eb-bc6a2a37bce9"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "34", "Geometry": {"BoundingBox": {"Width": 0.04900645464658737, "Height": 0.01625366322696209, "Left": 0.8215357661247253, "Top": 0.4149540066719055}, "Polygon": [{"X": 0.8215357661247253, "Y": 0.41495418548583984}, {"X": 0.8705409169197083, "Y": 0.4149540066719055}, {"X": 0.8705422282218933, "Y": 0.4312075078487396}, {"X": 0.8215371370315552, "Y": 0.43120765686035156}]}, "Id": "9f88c403-1072-490d-9343-c1be6f20f6d8", "Relationships": [{"Type": "CHILD", "Ids": ["59fe1314-e9c3-49bf-9852-0597427ddd92"]}]}, {"BlockType": "LINE", "Confidence": 99.48222351074219, "Text": "Client's Dept", "Geometry": {"BoundingBox": {"Width": 0.22377616167068481, "Height": 0.013752000406384468, "Left": 0.1440788060426712, "Top": 0.44123655557632446}, "Polygon": [{"X": 0.1440788060426712, "Y": 0.44123730063438416}, {"X": 0.367853581905365, "Y": 0.44123655557632446}, {"X": 0.3678549528121948, "Y": 0.45498788356781006}, {"X": 0.1440802961587906, "Y": 0.454988569021225}]}, "Id": "6530e1bd-8843-42b3-86d5-966444127175", "Relationships": [{"Type": "CHILD", "Ids": ["69ed1edf-9c61-4fd1-a471-74711d49a2e5", "3bf97b49-ecef-43b9-b78e-6e618488824b"]}]}, {"BlockType": "LINE", "Confidence": 83.53784942626953, "Text": "Unipading (GRP)", "Geometry": {"BoundingBox": {"Width": 0.3153095841407776, "Height": 0.018871990963816643, "Left": 0.5517191290855408, "Top": 0.43752774596214294}, "Polygon": [{"X": 0.5517191290855408, "Y": 0.4375287890434265}, {"X": 0.867027223110199, "Y": 0.43752774596214294}, {"X": 0.8670287132263184, "Y": 0.45639878511428833}, {"X": 0.551720917224884, "Y": 0.45639973878860474}]}, "Id": "78eeafc9-8b4f-4ecc-9538-caab9b1be681", "Relationships": [{"Type": "CHILD", "Ids": ["6ce146fb-8413-474c-bafa-9c53c62a638f", "4c93c144-741b-409d-aea4-fdb487c03c8f"]}]}, {"BlockType": "LINE", "Confidence": 99.24745178222656, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.14044232666492462, "Height": 0.011927680112421513, "Left": 0.14443199336528778, "Top": 0.4618944227695465}, "Polygon": [{"X": 0.14443199336528778, "Y": 0.46189481019973755}, {"X": 0.2848730981349945, "Y": 0.4618944227695465}, {"X": 0.2848743200302124, "Y": 0.4738217294216156}, {"X": 0.14443328976631165, "Y": 0.47382208704948425}]}, "Id": "ab7c9917-9848-48e6-a5f0-cef4f122e7dd", "Relationships": [{"Type": "CHILD", "Ids": ["1b38080b-6352-4858-a4a4-bcff223c<PERSON><PERSON>"]}]}, {"BlockType": "LINE", "Confidence": 99.15876770019531, "Text": "GM", "Geometry": {"BoundingBox": {"Width": 0.06667549163103104, "Height": 0.0152316028252244, "Left": 0.8020117282867432, "Top": 0.4576770067214966}, "Polygon": [{"X": 0.8020117282867432, "Y": 0.4576772153377533}, {"X": 0.8686860203742981, "Y": 0.4576770067214966}, {"X": 0.8686872124671936, "Y": 0.4729084372520447}, {"X": 0.8020129799842834, "Y": 0.472908616065979}]}, "Id": "1c3b0b91-425e-4037-94ec-cd4b231d1fed", "Relationships": [{"Type": "CHILD", "Ids": ["e5c2d825-2cf4-4b7e-be91-9837ffbe79b0"]}]}, {"BlockType": "LINE", "Confidence": 98.73505401611328, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.12628546357154846, "Height": 0.011748413555324078, "Left": 0.1465824842453003, "Top": 0.4827662408351898}, "Polygon": [{"X": 0.1465824842453003, "Y": 0.4827665388584137}, {"X": 0.27286672592163086, "Y": 0.4827662408351898}, {"X": 0.27286794781684875, "Y": 0.4945143759250641}, {"X": 0.14658375084400177, "Y": 0.4945146441459656}]}, "Id": "cb2581ac-91bd-48e2-97b1-32ebc8088449", "Relationships": [{"Type": "CHILD", "Ids": ["df19eb64-3930-4ad3-b8e0-2571595159fb"]}]}, {"BlockType": "LINE", "Confidence": 98.52598571777344, "Text": "PREMIUM WATERS INC", "Geometry": {"BoundingBox": {"Width": 0.42432907223701477, "Height": 0.016455309465527534, "Left": 0.44603678584098816, "Top": 0.47775039076805115}, "Polygon": [{"X": 0.44603678584098816, "Y": 0.4777514338493347}, {"X": 0.8703645467758179, "Y": 0.47775039076805115}, {"X": 0.8703658580780029, "Y": 0.4942047595977783}, {"X": 0.4460383951663971, "Y": 0.49420568346977234}]}, "Id": "e37638ef-c556-4970-a5f0-ae355a90633c", "Relationships": [{"Type": "CHILD", "Ids": ["0e6b17f0-4271-412a-ae6c-02bcec079907", "5d44a955-0e44-454c-b240-d5e0d5a76878", "94ef6dc5-e219-4560-b9f3-903a8b1d2a36"]}]}, {"BlockType": "LINE", "Confidence": 99.3126220703125, "Text": "Carrier", "Geometry": {"BoundingBox": {"Width": 0.12479837983846664, "Height": 0.012299999594688416, "Left": 0.1439296156167984, "Top": 0.5031832456588745}, "Polygon": [{"X": 0.1439296156167984, "Y": 0.5031834840774536}, {"X": 0.26872673630714417, "Y": 0.5031832456588745}, {"X": 0.26872798800468445, "Y": 0.515483021736145}, {"X": 0.14393095672130585, "Y": 0.5154832005500793}]}, "Id": "e462609b-dc10-40c7-83ed-8eef7d1bd28f", "Relationships": [{"Type": "CHILD", "Ids": ["2c32408d-309a-4c8e-b762-2ff4aca917c0"]}]}, {"BlockType": "LINE", "Confidence": 97.76048278808594, "Text": "3A TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.30032235383987427, "Height": 0.01761532574892044, "Left": 0.5707411766052246, "Top": 0.4987722635269165}, "Polygon": [{"X": 0.5707411766052246, "Y": 0.49877285957336426}, {"X": 0.8710620999336243, "Y": 0.4987722635269165}, {"X": 0.8710635304450989, "Y": 0.5163870453834534}, {"X": 0.5707427859306335, "Y": 0.5163875818252563}]}, "Id": "edc0520f-fbe5-40b8-9334-c74b83820232", "Relationships": [{"Type": "CHILD", "Ids": ["38be4da0-3640-415d-8670-06cb40c80927", "6d60a897-05aa-46bb-b017-668077bb0994"]}]}, {"BlockType": "LINE", "Confidence": 99.73313903808594, "Text": "Bill <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.11350125819444656, "Height": 0.012435048818588257, "Left": 0.14364956319332123, "Top": 0.5256401300430298}, "Polygon": [{"X": 0.14364956319332123, "Y": 0.5256402492523193}, {"X": 0.25714951753616333, "Y": 0.5256401300430298}, {"X": 0.2571508288383484, "Y": 0.5380750298500061}, {"X": 0.14365090429782867, "Y": 0.5380751490592957}]}, "Id": "42bbcd66-1fe1-4656-9286-4d39bc7f4a7e", "Relationships": [{"Type": "CHILD", "Ids": ["02b2ece0-f82a-4467-a02d-190fd30d0360", "ad0400ba-f1e6-43da-acc3-2f683b089129"]}]}, {"BlockType": "LINE", "Confidence": 99.5301513671875, "Text": "3A TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.3009212911128998, "Height": 0.01580245979130268, "Left": 0.5708645582199097, "Top": 0.5210983157157898}, "Polygon": [{"X": 0.5708645582199097, "Y": 0.521098792552948}, {"X": 0.8717845678329468, "Y": 0.5210983157157898}, {"X": 0.8717858195304871, "Y": 0.5369004011154175}, {"X": 0.5708659887313843, "Y": 0.5369007587432861}]}, "Id": "a5d9edb4-9b92-435b-a692-9943153bae65", "Relationships": [{"Type": "CHILD", "Ids": ["68658683-8e05-4235-a246-0657279455db", "4ff391cb-c969-4cd5-ae8c-19ec40d077fa"]}]}, {"BlockType": "LINE", "Confidence": 95.54956817626953, "Text": "QTY < Case >", "Geometry": {"BoundingBox": {"Width": 0.24496714770793915, "Height": 0.014735712669789791, "Left": 0.14367571473121643, "Top": 0.5462460517883301}, "Polygon": [{"X": 0.14367571473121643, "Y": 0.5462462902069092}, {"X": 0.3886414170265198, "Y": 0.5462460517883301}, {"X": 0.3886428773403168, "Y": 0.5609815716743469}, {"X": 0.14367730915546417, "Y": 0.5609817504882812}]}, "Id": "1423860c-cccf-4e62-b088-16b6e7ed910d", "Relationships": [{"Type": "CHILD", "Ids": ["dccf91b6-d473-4878-b72e-67f50fbd6474", "6eb016d6-d81d-441c-bd73-eeb01f47f2f4", "470c423b-7715-44da-a834-259968e62e18", "5ac813e4-72f2-4992-9bb8-341f2cfc9fd0"]}]}, {"BlockType": "LINE", "Confidence": 99.833984375, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.02006562240421772, "Height": 0.017020318657159805, "Left": 0.8509932160377502, "Top": 0.5419660210609436}, "Polygon": [{"X": 0.8509932160377502, "Y": 0.5419660806655884}, {"X": 0.8710575103759766, "Y": 0.5419660210609436}, {"X": 0.8710588812828064, "Y": 0.5589863657951355}, {"X": 0.8509945869445801, "Y": 0.5589863657951355}]}, "Id": "a055716d-5d03-4a62-9971-15924d7ad8cc", "Relationships": [{"Type": "CHILD", "Ids": ["d2f12a34-fd08-4d24-b21c-6b6fe35ae699"]}]}, {"BlockType": "LINE", "Confidence": 99.77458953857422, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.13567721843719482, "Height": 0.014926869422197342, "Left": 0.14278826117515564, "Top": 0.5681006908416748}, "Polygon": [{"X": 0.14278826117515564, "Y": 0.5681008100509644}, {"X": 0.2784639298915863, "Y": 0.5681006908416748}, {"X": 0.27846547961235046, "Y": 0.5830275416374207}, {"X": 0.14278988540172577, "Y": 0.5830276012420654}]}, "Id": "63d8efc2-50e5-4aca-aa35-2934c6dd6234", "Relationships": [{"Type": "CHILD", "Ids": ["937b15fe-e905-4b55-af3e-89fac5012a98"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$91.00", "Geometry": {"BoundingBox": {"Width": 0.12356474250555038, "Height": 0.01906082034111023, "Left": 0.7518740296363831, "Top": 0.5626896023750305}, "Polygon": [{"X": 0.7518740296363831, "Y": 0.5626896619796753}, {"X": 0.8754372596740723, "Y": 0.5626896023750305}, {"X": 0.8754388093948364, "Y": 0.5817503333091736}, {"X": 0.751875638961792, "Y": 0.5817503929138184}]}, "Id": "9f15b8e8-501e-4aa8-b544-32e1f412aa3c", "Relationships": [{"Type": "CHILD", "Ids": ["b71e4db9-bc90-45dd-944b-b46142adce55"]}]}, {"BlockType": "LINE", "Confidence": 99.7944564819336, "Text": "Work Order Charge", "Geometry": {"BoundingBox": {"Width": 0.34035080671310425, "Height": 0.015810536220669746, "Left": 0.1442163586616516, "Top": 0.587481677532196}, "Polygon": [{"X": 0.1442163586616516, "Y": 0.5874817371368408}, {"X": 0.4845656752586365, "Y": 0.587481677532196}, {"X": 0.48456716537475586, "Y": 0.6032922267913818}, {"X": 0.1442180722951889, "Y": 0.6032921671867371}]}, "Id": "0f5821ac-9bd0-4d1f-a414-383292719322", "Relationships": [{"Type": "CHILD", "Ids": ["cbc5c00b-9208-4a12-8010-53a5cc636147", "99fdee98-9a86-489b-aede-9e9cbd9578e1", "fe74e89a-e608-4b0b-927d-9044ff297d30"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$91.00", "Geometry": {"BoundingBox": {"Width": 0.12454274296760559, "Height": 0.01950083300471306, "Left": 0.7530006170272827, "Top": 0.5844067335128784}, "Polygon": [{"X": 0.7530006170272827, "Y": 0.5844067335128784}, {"X": 0.8775417804718018, "Y": 0.5844067335128784}, {"X": 0.8775433301925659, "Y": 0.6039075255393982}, {"X": 0.7530022263526917, "Y": 0.6039075255393982}]}, "Id": "a4433c0f-03a2-4a13-9eb2-6e7cf289b262", "Relationships": [{"Type": "CHILD", "Ids": ["7a47a989-b308-4581-8573-1a6ffee7cac5"]}]}, {"BlockType": "LINE", "Confidence": 99.50703430175781, "Text": "Service Charge", "Geometry": {"BoundingBox": {"Width": 0.2778431475162506, "Height": 0.014770335517823696, "Left": 0.14305239915847778, "Top": 0.6108126640319824}, "Polygon": [{"X": 0.14305239915847778, "Y": 0.6108126640319824}, {"X": 0.4208940863609314, "Y": 0.6108127236366272}, {"X": 0.4208955466747284, "Y": 0.6255829930305481}, {"X": 0.14305399358272552, "Y": 0.6255828738212585}]}, "Id": "b0e4370a-0673-4648-9686-28d16d8227fc", "Relationships": [{"Type": "CHILD", "Ids": ["c2dbe2df-2663-42ee-949c-50ab6abe6907", "3280eb44-2199-48ab-954d-976e6ae346d6"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "$8.00", "Geometry": {"BoundingBox": {"Width": 0.10247338563203812, "Height": 0.017269406467676163, "Left": 0.7763200998306274, "Top": 0.6070967316627502}, "Polygon": [{"X": 0.7763200998306274, "Y": 0.6070967316627502}, {"X": 0.8787921071052551, "Y": 0.607096791267395}, {"X": 0.878793478012085, "Y": 0.6243661642074585}, {"X": 0.776321530342102, "Y": 0.6243661046028137}]}, "Id": "f23ee84a-1b39-4fa3-bd28-98f4822e4b3f", "Relationships": [{"Type": "CHILD", "Ids": ["bddd52d7-07ec-4a3f-a40c-d51920e2cb22"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.0894695296883583, "Height": 0.014490507543087006, "Left": 0.1441158652305603, "Top": 0.6322702169418335}, "Polygon": [{"X": 0.1441158652305603, "Y": 0.6322702169418335}, {"X": 0.23358388245105743, "Y": 0.6322702765464783}, {"X": 0.2335854023694992, "Y": 0.6467607021331787}, {"X": 0.14411742985248566, "Y": 0.6467606425285339}]}, "Id": "4c295728-36d8-4fdf-8f59-db18f8d3560f", "Relationships": [{"Type": "CHILD", "Ids": ["ef8c8e6c-b9c4-4acc-8a18-0107cda5676a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$99.00", "Geometry": {"BoundingBox": {"Width": 0.12457958608865738, "Height": 0.019586818292737007, "Left": 0.7548099756240845, "Top": 0.6282671093940735}, "Polygon": [{"X": 0.7548099756240845, "Y": 0.6282671093940735}, {"X": 0.8793879747390747, "Y": 0.6282671689987183}, {"X": 0.8793895244598389, "Y": 0.6478539109230042}, {"X": 0.7548116445541382, "Y": 0.6478537917137146}]}, "Id": "7c7b05f8-7f61-48d4-844e-d3992f4a4f8c", "Relationships": [{"Type": "CHILD", "Ids": ["891d3d79-7f00-4da3-8864-893d95093be8"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "Payment", "Geometry": {"BoundingBox": {"Width": 0.16054531931877136, "Height": 0.015378644689917564, "Left": 0.14344261586666107, "Top": 0.6547008156776428}, "Polygon": [{"X": 0.14344261586666107, "Y": 0.6547008156776428}, {"X": 0.3039863705635071, "Y": 0.6547009944915771}, {"X": 0.30398792028427124, "Y": 0.6700794696807861}, {"X": 0.14344428479671478, "Y": 0.670079231262207}]}, "Id": "b16ec18a-308e-4e6b-b33b-0bd0df26aff3", "Relationships": [{"Type": "CHILD", "Ids": ["7c66f1d0-bdd1-4030-87d0-4f16a432b038"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10958477109670639, "Height": 0.018479187041521072, "Left": 0.7728049159049988, "Top": 0.6508646011352539}, "Polygon": [{"X": 0.7728049159049988, "Y": 0.6508646011352539}, {"X": 0.8823882341384888, "Y": 0.6508647799491882}, {"X": 0.8823896646499634, "Y": 0.6693438291549683}, {"X": 0.7728064656257629, "Y": 0.6693436503410339}]}, "Id": "eb90badf-af66-46ec-b5d5-c42b687e4649", "Relationships": [{"Type": "CHILD", "Ids": ["829d336c-40a6-43ce-8033-af1247a2b289"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Authorization", "Geometry": {"BoundingBox": {"Width": 0.23951470851898193, "Height": 0.013458259403705597, "Left": 0.13951319456100464, "Top": 0.6752681136131287}, "Polygon": [{"X": 0.13951319456100464, "Y": 0.6752681136131287}, {"X": 0.3790265619754791, "Y": 0.6752685308456421}, {"X": 0.3790279030799866, "Y": 0.6887263655662537}, {"X": 0.13951465487480164, "Y": 0.6887259483337402}]}, "Id": "e6223d4a-3858-406d-8c65-48c88362eb79", "Relationships": [{"Type": "CHILD", "Ids": ["fcf5ab30-82b6-469e-93ec-54fcaf0598d3"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "12007196", "Geometry": {"BoundingBox": {"Width": 0.177385151386261, "Height": 0.015971913933753967, "Left": 0.7045323252677917, "Top": 0.6715840101242065}, "Polygon": [{"X": 0.7045323252677917, "Y": 0.6715840101242065}, {"X": 0.8819162249565125, "Y": 0.6715843081474304}, {"X": 0.8819174766540527, "Y": 0.6875559687614441}, {"X": 0.7045337557792664, "Y": 0.6875556111335754}]}, "Id": "7165cff1-0a01-4bb3-bc3c-c709b158d086", "Relationships": [{"Type": "CHILD", "Ids": ["c2db2bbd-39b9-4af2-8744-6b5a0a965697"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.11279182881116867, "Height": 0.01331867091357708, "Left": 0.1428855061531067, "Top": 0.6973837018013}, "Polygon": [{"X": 0.1428855061531067, "Y": 0.6973837018013}, {"X": 0.25567594170570374, "Y": 0.6973839402198792}, {"X": 0.25567731261253357, "Y": 0.7107023596763611}, {"X": 0.1428869366645813, "Y": 0.710702121257782}]}, "Id": "0b99309a-531d-43a9-a61a-ffd2d83e0c1c", "Relationships": [{"Type": "CHILD", "Ids": ["d6ee9e6a-350f-415e-8311-fdaa38d7c09c"]}]}, {"BlockType": "LINE", "Confidence": 99.45731353759766, "Text": "RRR", "Geometry": {"BoundingBox": {"Width": 0.08374407887458801, "Height": 0.016965564340353012, "Left": 0.803195059299469, "Top": 0.6927899718284607}, "Polygon": [{"X": 0.803195059299469, "Y": 0.6927899718284607}, {"X": 0.8869377970695496, "Y": 0.692790150642395}, {"X": 0.8869391083717346, "Y": 0.709755539894104}, {"X": 0.8031964302062988, "Y": 0.7097553610801697}]}, "Id": "3722f7fc-51bc-4221-a673-94b8f4f9e84f", "Relationships": [{"Type": "CHILD", "Ids": ["2046eae9-9cee-403d-9046-68de0a085bdb"]}]}, {"BlockType": "LINE", "Confidence": 99.68192291259766, "Text": "Driver's Signature", "Geometry": {"BoundingBox": {"Width": 0.32594433426856995, "Height": 0.01589202508330345, "Left": 0.3199101388454437, "Top": 0.7359161972999573}, "Polygon": [{"X": 0.3199101388454437, "Y": 0.7359161972999573}, {"X": 0.6458530426025391, "Y": 0.7359171509742737}, {"X": 0.6458544731140137, "Y": 0.751808226108551}, {"X": 0.31991174817085266, "Y": 0.7518072128295898}]}, "Id": "e8739d91-f7a4-4906-b156-4868ff851d8c", "Relationships": [{"Type": "CHILD", "Ids": ["6db2dfd2-444a-484a-8ad1-08210b2b0bc6", "4662330b-eda0-4dc0-8bd7-5863497d4975"]}]}, {"BlockType": "LINE", "Confidence": 99.56194305419922, "Text": "Supervisor's Signature", "Geometry": {"BoundingBox": {"Width": 0.41156283020973206, "Height": 0.016970403492450714, "Left": 0.28128546476364136, "Top": 0.8308382034301758}, "Polygon": [{"X": 0.28128546476364136, "Y": 0.8308382034301758}, {"X": 0.6928467750549316, "Y": 0.8308401703834534}, {"X": 0.692848265171051, "Y": 0.8478085994720459}, {"X": 0.28128719329833984, "Y": 0.847806453704834}]}, "Id": "b74f28ec-2844-4674-bdc5-83551c1a2c82", "Relationships": [{"Type": "CHILD", "Ids": ["751cc41a-cb79-4a75-9e78-6dee1b5dc9c4", "12fb3058-da75-4e94-bb23-fede2dc1b199"]}]}, {"BlockType": "LINE", "Confidence": 70.44145202636719, "Text": "29", "Geometry": {"BoundingBox": {"Width": 0.25587156414985657, "Height": 0.03180857002735138, "Left": 0.3889331519603729, "Top": 0.8613988757133484}, "Polygon": [{"X": 0.3889331519603729, "Y": 0.8613988757133484}, {"X": 0.644801914691925, "Y": 0.861400306224823}, {"X": 0.6448047161102295, "Y": 0.8932074308395386}, {"X": 0.38893628120422363, "Y": 0.8932058811187744}]}, "Id": "7e41aefb-6606-4d9a-83f8-be0ca4b49cc1", "Relationships": [{"Type": "CHILD", "Ids": ["4bcaa9c7-a94a-45d3-8e9a-df3e8b6557b6"]}]}, {"BlockType": "LINE", "Confidence": 96.42597961425781, "Text": "07/26/2025 00:35", "Geometry": {"BoundingBox": {"Width": 0.29983964562416077, "Height": 0.011939950287342072, "Left": 0.36763235926628113, "Top": 0.9582545161247253}, "Polygon": [{"X": 0.36763235926628113, "Y": 0.9582545161247253}, {"X": 0.6674709320068359, "Y": 0.9582567811012268}, {"X": 0.6674720048904419, "Y": 0.9701944589614868}, {"X": 0.36763355135917664, "Y": 0.9701921343803406}]}, "Id": "6fd1878e-0e3a-4068-9a94-48506251a03f", "Relationships": [{"Type": "CHILD", "Ids": ["3dc274a7-977a-4768-94e4-09149ff64c9e", "c87f8534-9331-492d-ba04-51a3dee0f9e4"]}]}, {"BlockType": "LINE", "Confidence": 30.004981994628906, "Text": "www.merittogistics.com", "Geometry": {"BoundingBox": {"Width": 0.4804471433162689, "Height": 0.012499780394136906, "Left": 0.27515482902526855, "Top": 0.9721404910087585}, "Polygon": [{"X": 0.27515482902526855, "Y": 0.9721404910087585}, {"X": 0.7556009292602539, "Y": 0.9721442461013794}, {"X": 0.7556019425392151, "Y": 0.9846402406692505}, {"X": 0.2751561105251312, "Y": 0.9846363663673401}]}, "Id": "37a6a54b-549d-4d3b-ab20-a13d76ca1ad9", "Relationships": [{"Type": "CHILD", "Ids": ["51c3e458-d06c-46d9-a5d6-7f3d45e20a6b"]}]}, {"BlockType": "WORD", "Confidence": 98.26909637451172, "Text": "MERIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24773530662059784, "Height": 0.030968578532338142, "Left": 0.18507200479507446, "Top": 0.1073952466249466}, "Polygon": [{"X": 0.18507200479507446, "Y": 0.10739777982234955}, {"X": 0.43280428647994995, "Y": 0.1073952466249466}, {"X": 0.4328072965145111, "Y": 0.13836143910884857}, {"X": 0.1850752979516983, "Y": 0.1383638232946396}]}, "Id": "a0f013b4-b6ca-47d6-9370-4c5ff264dcfd"}, {"BlockType": "WORD", "Confidence": 57.2064323425293, "Text": "LOGISTIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3510882556438446, "Height": 0.03660334646701813, "Left": 0.5848082900047302, "Top": 0.0968146026134491}, "Polygon": [{"X": 0.5848082900047302, "Y": 0.09681828320026398}, {"X": 0.9358936548233032, "Y": 0.0968146026134491}, {"X": 0.9358965158462524, "Y": 0.13341455161571503}, {"X": 0.5848116278648376, "Y": 0.13341794908046722}]}, "Id": "6dfaee41-57d8-45bd-b753-50d1b710c7f0"}, {"BlockType": "WORD", "Confidence": 98.13397216796875, "Text": "(949)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10251326858997345, "Height": 0.021099045872688293, "Left": 0.3484624922275543, "Top": 0.17989249527454376}, "Polygon": [{"X": 0.3484624922275543, "Y": 0.1798933893442154}, {"X": 0.4509737491607666, "Y": 0.17989249527454376}, {"X": 0.45097577571868896, "Y": 0.200990691781044}, {"X": 0.34846460819244385, "Y": 0.20099154114723206}]}, "Id": "684613e5-be85-4709-be6a-7d851a471b8e"}, {"BlockType": "WORD", "Confidence": 97.06253814697266, "Text": "481-0685", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19154953956604004, "Height": 0.017770186066627502, "Left": 0.4649932086467743, "Top": 0.17896682024002075}, "Polygon": [{"X": 0.4649932086467743, "Y": 0.17896850407123566}, {"X": 0.6565411686897278, "Y": 0.17896682024002075}, {"X": 0.6565427780151367, "Y": 0.19673539698123932}, {"X": 0.4649949073791504, "Y": 0.19673700630664825}]}, "Id": "a5f686e6-3e30-4f21-9840-a50e06148951"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08758188039064407, "Height": 0.016989395022392273, "Left": 0.1708100140094757, "Top": 0.22101864218711853}, "Polygon": [{"X": 0.1708100140094757, "Y": 0.22101934254169464}, {"X": 0.2583901286125183, "Y": 0.22101864218711853}, {"X": 0.2583919167518616, "Y": 0.23800738155841827}, {"X": 0.17081184685230255, "Y": 0.2380080372095108}]}, "Id": "4c4d769d-1e3f-4cbd-aa41-6bbf529ab3fa"}, {"BlockType": "WORD", "Confidence": 98.76435089111328, "Text": "(ALDFRE),", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22142870724201202, "Height": 0.020506413653492928, "Left": 0.27561527490615845, "Top": 0.22129520773887634}, "Polygon": [{"X": 0.27561527490615845, "Y": 0.22129695117473602}, {"X": 0.49704205989837646, "Y": 0.22129520773887634}, {"X": 0.49704399704933167, "Y": 0.2417999804019928}, {"X": 0.275617390871048, "Y": 0.24180161952972412}]}, "Id": "3dc007db-446d-401f-be87-b4dd436b9295"}, {"BlockType": "WORD", "Confidence": 98.48612976074219, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2255355566740036, "Height": 0.018261950463056564, "Left": 0.5158470273017883, "Top": 0.21942326426506042}, "Polygon": [{"X": 0.5158470273017883, "Y": 0.2194250524044037}, {"X": 0.741381049156189, "Y": 0.21942326426506042}, {"X": 0.7413825988769531, "Y": 0.2376835197210312}, {"X": 0.5158487558364868, "Y": 0.2376852184534073}]}, "Id": "1646d2d7-efc7-4f85-a13e-127256ae9723"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07407316565513611, "Height": 0.01718895696103573, "Left": 0.7608513832092285, "Top": 0.21789641678333282}, "Polygon": [{"X": 0.7608513832092285, "Y": 0.21789701282978058}, {"X": 0.8349231481552124, "Y": 0.21789641678333282}, {"X": 0.834924578666687, "Y": 0.23508481681346893}, {"X": 0.7608528137207031, "Y": 0.2350853830575943}]}, "Id": "214460cf-6bc3-48a3-b09d-f78ba4d175de"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06189828738570213, "Height": 0.012198771350085735, "Left": 0.14416752755641937, "Top": 0.279058039188385}, "Polygon": [{"X": 0.14416752755641937, "Y": 0.27905845642089844}, {"X": 0.20606452226638794, "Y": 0.279058039188385}, {"X": 0.2060658037662506, "Y": 0.2912564277648926}, {"X": 0.14416883885860443, "Y": 0.2912568151950836}]}, "Id": "880cf77b-fbec-4ffa-8fc0-059d11c5017f"}, {"BlockType": "WORD", "Confidence": 94.63568115234375, "Text": "Id", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036270540207624435, "Height": 0.012380054220557213, "Left": 0.21731781959533691, "Top": 0.27878475189208984}, "Polygon": [{"X": 0.21731781959533691, "Y": 0.27878499031066895}, {"X": 0.2535870671272278, "Y": 0.27878475189208984}, {"X": 0.25358834862709045, "Y": 0.2911645770072937}, {"X": 0.21731913089752197, "Y": 0.2911648154258728}]}, "Id": "0bb7e0c7-1ff9-4275-9f57-7359baf509a9"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "*********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19964005053043365, "Height": 0.01619478315114975, "Left": 0.6647797226905823, "Top": 0.2744753062725067}, "Polygon": [{"X": 0.6647797226905823, "Y": 0.27447664737701416}, {"X": 0.8644184470176697, "Y": 0.2744753062725067}, {"X": 0.8644197583198547, "Y": 0.2906688153743744}, {"X": 0.6647811532020569, "Y": 0.29067009687423706}]}, "Id": "23a59e62-72d4-424f-8c89-f04f4b6ed0a8"}, {"BlockType": "WORD", "Confidence": 87.84259796142578, "Text": "WO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05964205786585808, "Height": 0.01220475509762764, "Left": 0.4134569764137268, "Top": 0.2944175601005554}, "Polygon": [{"X": 0.4134569764137268, "Y": 0.29441794753074646}, {"X": 0.47309786081314087, "Y": 0.2944175601005554}, {"X": 0.473099023103714, "Y": 0.3066219389438629}, {"X": 0.4134581685066223, "Y": 0.30662232637405396}]}, "Id": "d17a240b-9e1e-41f4-8dac-2d991d4e3009"}, {"BlockType": "WORD", "Confidence": 98.154296875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02495601400732994, "Height": 0.011620372533798218, "Left": 0.4817391633987427, "Top": 0.2947494387626648}, "Polygon": [{"X": 0.4817391633987427, "Y": 0.29474958777427673}, {"X": 0.5066940784454346, "Y": 0.2947494387626648}, {"X": 0.5066951513290405, "Y": 0.3063696622848511}, {"X": 0.481740266084671, "Y": 0.306369811296463}]}, "Id": "2899e01b-162c-4493-b6f0-2ba10329ad36"}, {"BlockType": "WORD", "Confidence": 79.87165832519531, "Text": "ALDFRE9734307252025203958", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.5582346320152283, "Height": 0.016875185072422028, "Left": 0.2240321785211563, "Top": 0.310842365026474}, "Polygon": [{"X": 0.2240321785211563, "Y": 0.3108457028865814}, {"X": 0.7822654247283936, "Y": 0.310842365026474}, {"X": 0.7822668552398682, "Y": 0.32771438360214233}, {"X": 0.22403395175933838, "Y": 0.32771754264831543}]}, "Id": "3cb51c25-7360-4eee-b795-bbd9f4b5fcc4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08533041924238205, "Height": 0.012756140902638435, "Left": 0.14407946169376373, "Top": 0.3356296718120575}, "Polygon": [{"X": 0.14407946169376373, "Y": 0.3356301188468933}, {"X": 0.22940854728221893, "Y": 0.3356296718120575}, {"X": 0.22940988838672638, "Y": 0.34838536381721497}, {"X": 0.14408084750175476, "Y": 0.3483858108520508}]}, "Id": "4b4e0917-c5ee-4a0e-9cef-1ee921fab428"}, {"BlockType": "WORD", "Confidence": 99.052734375, "Text": "7/25/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17674770951271057, "Height": 0.014293763786554337, "Left": 0.4762756824493408, "Top": 0.3339797854423523}, "Polygon": [{"X": 0.4762756824493408, "Y": 0.3339807689189911}, {"X": 0.6530221104621887, "Y": 0.3339797854423523}, {"X": 0.653023362159729, "Y": 0.3482726216316223}, {"X": 0.47627705335617065, "Y": 0.34827354550361633}]}, "Id": "c963b332-79de-40e7-84d7-f1fd8f487f2a"}, {"BlockType": "WORD", "Confidence": 99.228515625, "Text": "8:39:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13315479457378387, "Height": 0.015541712753474712, "Left": 0.6603617668151855, "Top": 0.33210861682891846}, "Polygon": [{"X": 0.6603617668151855, "Y": 0.33210936188697815}, {"X": 0.7935152649879456, "Y": 0.33210861682891846}, {"X": 0.7935165762901306, "Y": 0.34764963388442993}, {"X": 0.6603631377220154, "Y": 0.34765031933784485}]}, "Id": "3513c9ef-8fb8-4c2a-9ddf-415cd0d8e641"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06124924123287201, "Height": 0.015116817317903042, "Left": 0.8028196692466736, "Top": 0.3310616910457611}, "Polygon": [{"X": 0.8028196692466736, "Y": 0.33106204867362976}, {"X": 0.8640676736831665, "Y": 0.3310616910457611}, {"X": 0.8640689253807068, "Y": 0.34617817401885986}, {"X": 0.8028209209442139, "Y": 0.34617850184440613}]}, "Id": "73d00231-6f3d-46be-816f-2a42023d7e4e"}, {"BlockType": "WORD", "Confidence": 99.1796875, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05549069494009018, "Height": 0.01195859257131815, "Left": 0.14226120710372925, "Top": 0.3576103746891022}, "Polygon": [{"X": 0.14226120710372925, "Y": 0.35761067271232605}, {"X": 0.19775062799453735, "Y": 0.3576103746891022}, {"X": 0.19775189459323883, "Y": 0.36956870555877686}, {"X": 0.1422625035047531, "Y": 0.36956897377967834}]}, "Id": "187d6aec-e6b8-41ad-bdae-344d27dc391e"}, {"BlockType": "WORD", "Confidence": 99.3359375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022576121613383293, "Height": 0.01141516026109457, "Left": 0.20773808658123016, "Top": 0.3579596281051636}, "Polygon": [{"X": 0.20773808658123016, "Y": 0.35795971751213074}, {"X": 0.2303130030632019, "Y": 0.3579596281051636}, {"X": 0.2303142100572586, "Y": 0.36937466263771057}, {"X": 0.20773929357528687, "Y": 0.3693747818470001}]}, "Id": "57e1a924-98ab-4052-bc66-9b97d072447f"}, {"BlockType": "WORD", "Confidence": 98.6756591796875, "Text": "7507418921", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21574129164218903, "Height": 0.016964301466941833, "Left": 0.6466652750968933, "Top": 0.3529658615589142}, "Polygon": [{"X": 0.6466652750968933, "Y": 0.35296696424484253}, {"X": 0.8624052405357361, "Y": 0.3529658615589142}, {"X": 0.8624066114425659, "Y": 0.36992913484573364}, {"X": 0.6466668248176575, "Y": 0.3699301779270172}]}, "Id": "b154dfe2-a984-405a-afb3-b6aa7ff043de"}, {"BlockType": "WORD", "Confidence": 99.013671875, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09974201023578644, "Height": 0.012015053071081638, "Left": 0.14428658783435822, "Top": 0.3778558671474457}, "Polygon": [{"X": 0.14428658783435822, "Y": 0.3778563141822815}, {"X": 0.24402734637260437, "Y": 0.3778558671474457}, {"X": 0.24402859807014465, "Y": 0.38987046480178833}, {"X": 0.14428788423538208, "Y": 0.38987091183662415}]}, "Id": "3f631572-daa8-4686-ba71-5b28b8816e48"}, {"BlockType": "WORD", "Confidence": 99.365234375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025024201720952988, "Height": 0.01248437911272049, "Left": 0.25288084149360657, "Top": 0.3771931827068329}, "Polygon": [{"X": 0.25288084149360657, "Y": 0.37719330191612244}, {"X": 0.2779037654399872, "Y": 0.3771931827068329}, {"X": 0.27790504693984985, "Y": 0.3896774649620056}, {"X": 0.2528821527957916, "Y": 0.3896775543689728}]}, "Id": "fa8864c7-0ea6-4154-a494-ecca8c8ac498"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "101", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06180214136838913, "Height": 0.015584289096295834, "Left": 0.8016756772994995, "Top": 0.37275105714797974}, "Polygon": [{"X": 0.8016756772994995, "Y": 0.3727513551712036}, {"X": 0.863476574420929, "Y": 0.37275105714797974}, {"X": 0.8634778261184692, "Y": 0.38833507895469666}, {"X": 0.8016769289970398, "Y": 0.38833534717559814}]}, "Id": "49f12429-977b-4335-9288-ad9d9ed4dd23"}, {"BlockType": "WORD", "Confidence": 98.05484771728516, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11645343154668808, "Height": 0.01223898958414793, "Left": 0.14484895765781403, "Top": 0.3990103304386139}, "Polygon": [{"X": 0.14484895765781403, "Y": 0.3990108072757721}, {"X": 0.2613011300563812, "Y": 0.3990103304386139}, {"X": 0.2613024115562439, "Y": 0.4112488627433777}, {"X": 0.14485028386116028, "Y": 0.4112493097782135}]}, "Id": "52619a63-78a7-4b0b-8c6b-c7a04067e9b1"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02246459759771824, "Height": 0.011130369268357754, "Left": 0.2695062458515167, "Top": 0.3993787169456482}, "Polygon": [{"X": 0.2695062458515167, "Y": 0.39937883615493774}, {"X": 0.2919696867465973, "Y": 0.3993787169456482}, {"X": 0.2919708490371704, "Y": 0.41050902009010315}, {"X": 0.26950737833976746, "Y": 0.4105091094970703}]}, "Id": "45ad40e3-7fb1-4f7d-84fb-7155c97ed12d"}, {"BlockType": "WORD", "Confidence": 88.96424865722656, "Text": "()--288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12617294490337372, "Height": 0.019286949187517166, "Left": 0.7425933480262756, "Top": 0.39464476704597473}, "Polygon": [{"X": 0.7425933480262756, "Y": 0.3946453034877777}, {"X": 0.8687646985054016, "Y": 0.39464476704597473}, {"X": 0.8687662482261658, "Y": 0.4139312207698822}, {"X": 0.7425949573516846, "Y": 0.4139316976070404}]}, "Id": "036fa178-adb0-4ca6-890f-f38d61af75a6"}, {"BlockType": "WORD", "Confidence": 99.05193328857422, "Text": "Door", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08543319255113602, "Height": 0.01184589508920908, "Left": 0.14406432211399078, "Top": 0.420370489358902}, "Polygon": [{"X": 0.14406432211399078, "Y": 0.42037081718444824}, {"X": 0.2294962853193283, "Y": 0.420370489358902}, {"X": 0.2294975221157074, "Y": 0.4322161078453064}, {"X": 0.14406560361385345, "Y": 0.4322164058685303}]}, "Id": "8c63c056-70bc-419a-90d7-aa9c858bcac4"}, {"BlockType": "WORD", "Confidence": 98.828125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02272077277302742, "Height": 0.011581983417272568, "Left": 0.237057164311409, "Top": 0.4203850328922272}, "Polygon": [{"X": 0.237057164311409, "Y": 0.42038512229919434}, {"X": 0.2597767412662506, "Y": 0.4203850328922272}, {"X": 0.2597779333591461, "Y": 0.43196696043014526}, {"X": 0.2370583862066269, "Y": 0.43196702003479004}]}, "Id": "f43590a3-95c5-449f-81eb-bc6a2a37bce9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04900645464658737, "Height": 0.01625366322696209, "Left": 0.8215357661247253, "Top": 0.4149540066719055}, "Polygon": [{"X": 0.8215357661247253, "Y": 0.41495418548583984}, {"X": 0.8705409169197083, "Y": 0.4149540066719055}, {"X": 0.8705422282218933, "Y": 0.4312075078487396}, {"X": 0.8215371370315552, "Y": 0.43120765686035156}]}, "Id": "59fe1314-e9c3-49bf-9852-0597427ddd92"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "Client's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12869594991207123, "Height": 0.012451906688511372, "Left": 0.14407885074615479, "Top": 0.4415885806083679}, "Polygon": [{"X": 0.14407885074615479, "Y": 0.44158899784088135}, {"X": 0.27277350425720215, "Y": 0.4415885806083679}, {"X": 0.2727747857570648, "Y": 0.4540401101112366}, {"X": 0.14408019185066223, "Y": 0.4540404975414276}]}, "Id": "69ed1edf-9c61-4fd1-a471-74711d49a2e5"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "Dept", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08303327113389969, "Height": 0.013751579448580742, "Left": 0.28482168912887573, "Top": 0.44123655557632446}, "Polygon": [{"X": 0.28482168912887573, "Y": 0.44123682379722595}, {"X": 0.367853581905365, "Y": 0.44123655557632446}, {"X": 0.3678549528121948, "Y": 0.45498788356781006}, {"X": 0.28482311964035034, "Y": 0.45498812198638916}]}, "Id": "3bf97b49-ecef-43b9-b78e-6e618488824b"}, {"BlockType": "WORD", "Confidence": 68.30974578857422, "Text": "Unipading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1949533075094223, "Height": 0.01763341948390007, "Left": 0.5517192482948303, "Top": 0.4387663006782532}, "Polygon": [{"X": 0.5517192482948303, "Y": 0.4387669563293457}, {"X": 0.7466710805892944, "Y": 0.4387663006782532}, {"X": 0.7466725707054138, "Y": 0.456399142742157}, {"X": 0.551720917224884, "Y": 0.45639973878860474}]}, "Id": "6ce146fb-8413-474c-bafa-9c53c62a638f"}, {"BlockType": "WORD", "Confidence": 98.76594543457031, "Text": "(GRP)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1080993041396141, "Height": 0.017862476408481598, "Left": 0.7589293718338013, "Top": 0.43752774596214294}, "Polygon": [{"X": 0.7589293718338013, "Y": 0.4375281035900116}, {"X": 0.867027223110199, "Y": 0.43752774596214294}, {"X": 0.8670286536216736, "Y": 0.4553898870944977}, {"X": 0.7589308619499207, "Y": 0.45539021492004395}]}, "Id": "4c93c144-741b-409d-aea4-fdb487c03c8f"}, {"BlockType": "WORD", "Confidence": 99.24745178222656, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14044232666492462, "Height": 0.011927680112421513, "Left": 0.14443199336528778, "Top": 0.4618944227695465}, "Polygon": [{"X": 0.14443199336528778, "Y": 0.46189481019973755}, {"X": 0.2848730981349945, "Y": 0.4618944227695465}, {"X": 0.2848743200302124, "Y": 0.4738217294216156}, {"X": 0.14443328976631165, "Y": 0.47382208704948425}]}, "Id": "1b38080b-6352-4858-a4a4-bcff223c<PERSON><PERSON>"}, {"BlockType": "WORD", "Confidence": 99.15876770019531, "Text": "GM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06667549163103104, "Height": 0.0152316028252244, "Left": 0.8020117282867432, "Top": 0.4576770067214966}, "Polygon": [{"X": 0.8020117282867432, "Y": 0.4576772153377533}, {"X": 0.8686860203742981, "Y": 0.4576770067214966}, {"X": 0.8686872124671936, "Y": 0.4729084372520447}, {"X": 0.8020129799842834, "Y": 0.472908616065979}]}, "Id": "e5c2d825-2cf4-4b7e-be91-9837ffbe79b0"}, {"BlockType": "WORD", "Confidence": 98.73505401611328, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12628546357154846, "Height": 0.011748413555324078, "Left": 0.1465824842453003, "Top": 0.4827662408351898}, "Polygon": [{"X": 0.1465824842453003, "Y": 0.4827665388584137}, {"X": 0.27286672592163086, "Y": 0.4827662408351898}, {"X": 0.27286794781684875, "Y": 0.4945143759250641}, {"X": 0.14658375084400177, "Y": 0.4945146441459656}]}, "Id": "df19eb64-3930-4ad3-b8e0-2571595159fb"}, {"BlockType": "WORD", "Confidence": 97.0948257446289, "Text": "PREMIUM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17048752307891846, "Height": 0.01445438340306282, "Left": 0.4460369944572449, "Top": 0.479751318693161}, "Polygon": [{"X": 0.4460369944572449, "Y": 0.47975173592567444}, {"X": 0.6165232062339783, "Y": 0.479751318693161}, {"X": 0.6165245175361633, "Y": 0.4942053258419037}, {"X": 0.4460383951663971, "Y": 0.49420568346977234}]}, "Id": "0e6b17f0-4271-412a-ae6c-02bcec079907"}, {"BlockType": "WORD", "Confidence": 98.984375, "Text": "WATERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1675719916820526, "Height": 0.014728314243257046, "Left": 0.6264291405677795, "Top": 0.47906169295310974}, "Polygon": [{"X": 0.6264291405677795, "Y": 0.47906211018562317}, {"X": 0.7939999103546143, "Y": 0.47906169295310974}, {"X": 0.7940011024475098, "Y": 0.4937896430492401}, {"X": 0.6264304518699646, "Y": 0.49379000067710876}]}, "Id": "5d44a955-0e44-454c-b240-d5e0d5a76878"}, {"BlockType": "WORD", "Confidence": 99.49876403808594, "Text": "INC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06712885200977325, "Height": 0.01506961788982153, "Left": 0.8032369017601013, "Top": 0.47775039076805115}, "Polygon": [{"X": 0.8032369017601013, "Y": 0.4777505397796631}, {"X": 0.8703645467758179, "Y": 0.47775039076805115}, {"X": 0.8703657388687134, "Y": 0.4928198456764221}, {"X": 0.8032381534576416, "Y": 0.49281999468803406}]}, "Id": "94ef6dc5-e219-4560-b9f3-903a8b1d2a36"}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12479837983846664, "Height": 0.012299999594688416, "Left": 0.1439296156167984, "Top": 0.5031832456588745}, "Polygon": [{"X": 0.1439296156167984, "Y": 0.5031834840774536}, {"X": 0.26872673630714417, "Y": 0.5031832456588745}, {"X": 0.26872798800468445, "Y": 0.515483021736145}, {"X": 0.14393095672130585, "Y": 0.5154832005500793}]}, "Id": "2c32408d-309a-4c8e-b762-2ff4aca917c0"}, {"BlockType": "WORD", "Confidence": 95.7276382446289, "Text": "3A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05320126563310623, "Height": 0.01563923805952072, "Left": 0.5707413554191589, "Top": 0.500748336315155}, "Polygon": [{"X": 0.5707413554191589, "Y": 0.5007484555244446}, {"X": 0.62394118309021, "Y": 0.500748336315155}, {"X": 0.6239426136016846, "Y": 0.5163874626159668}, {"X": 0.5707427859306335, "Y": 0.5163875818252563}]}, "Id": "38be4da0-3640-415d-8670-06cb40c80927"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24108321964740753, "Height": 0.01743924617767334, "Left": 0.6299802660942078, "Top": 0.4987722635269165}, "Polygon": [{"X": 0.6299802660942078, "Y": 0.4987727403640747}, {"X": 0.8710620999336243, "Y": 0.4987722635269165}, {"X": 0.8710635304450989, "Y": 0.5162110924720764}, {"X": 0.6299818158149719, "Y": 0.5162115097045898}]}, "Id": "6d60a897-05aa-46bb-b017-668077bb0994"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05958958715200424, "Height": 0.012090464122593403, "Left": 0.14364959299564362, "Top": 0.5259847044944763}, "Polygon": [{"X": 0.14364959299564362, "Y": 0.5259847640991211}, {"X": 0.20323790609836578, "Y": 0.5259847044944763}, {"X": 0.20323918759822845, "Y": 0.5380750894546509}, {"X": 0.14365090429782867, "Y": 0.5380751490592957}]}, "Id": "02b2ece0-f82a-4467-a02d-190fd30d0360"}, {"BlockType": "WORD", "Confidence": 99.59402465820312, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043086618185043335, "Height": 0.011830746196210384, "Left": 0.21406413614749908, "Top": 0.5256401300430298}, "Polygon": [{"X": 0.21406413614749908, "Y": 0.5256401896476746}, {"X": 0.25714951753616333, "Y": 0.5256401300430298}, {"X": 0.2571507394313812, "Y": 0.537470817565918}, {"X": 0.21406538784503937, "Y": 0.5374708771705627}]}, "Id": "ad0400ba-f1e6-43da-acc3-2f683b089129"}, {"BlockType": "WORD", "Confidence": 99.25642395019531, "Text": "3A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05361201986670494, "Height": 0.014675240032374859, "Left": 0.5708646774291992, "Top": 0.522225558757782}, "Polygon": [{"X": 0.5708646774291992, "Y": 0.5222256183624268}, {"X": 0.624475359916687, "Y": 0.522225558757782}, {"X": 0.6244766712188721, "Y": 0.5369006991386414}, {"X": 0.5708659887313843, "Y": 0.5369007587432861}]}, "Id": "68658683-8e05-4235-a246-0657279455db"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2408258467912674, "Height": 0.015583988279104233, "Left": 0.6309599876403809, "Top": 0.5210983157157898}, "Polygon": [{"X": 0.6309599876403809, "Y": 0.5210987329483032}, {"X": 0.8717845678329468, "Y": 0.5210983157157898}, {"X": 0.8717858195304871, "Y": 0.5366820096969604}, {"X": 0.6309613585472107, "Y": 0.5366823077201843}]}, "Id": "4ff391cb-c969-4cd5-ae8c-19ec40d077fa"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "QTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07920538634061813, "Height": 0.014591755345463753, "Left": 0.14367572963237762, "Top": 0.5463899970054626}, "Polygon": [{"X": 0.14367572963237762, "Y": 0.5463900566101074}, {"X": 0.2228795886039734, "Y": 0.5463899970054626}, {"X": 0.22288112342357635, "Y": 0.5609816908836365}, {"X": 0.14367730915546417, "Y": 0.5609817504882812}]}, "Id": "dccf91b6-d473-4878-b72e-67f50fbd6474"}, {"BlockType": "WORD", "Confidence": 92.7648696899414, "Text": "<", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02094186097383499, "Height": 0.009188602678477764, "Left": 0.23336319625377655, "Top": 0.5478973984718323}, "Polygon": [{"X": 0.23336319625377655, "Y": 0.5478973984718323}, {"X": 0.25430411100387573, "Y": 0.5478973984718323}, {"X": 0.25430506467819214, "Y": 0.5570859313011169}, {"X": 0.23336416482925415, "Y": 0.5570859909057617}]}, "Id": "6eb016d6-d81d-441c-bd73-eeb01f47f2f4"}, {"BlockType": "WORD", "Confidence": 94.99302673339844, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08773268759250641, "Height": 0.012426801957190037, "Left": 0.26646989583969116, "Top": 0.5462460517883301}, "Polygon": [{"X": 0.26646989583969116, "Y": 0.5462461709976196}, {"X": 0.3542013466358185, "Y": 0.5462460517883301}, {"X": 0.35420259833335876, "Y": 0.5586727857589722}, {"X": 0.2664712071418762, "Y": 0.5586728453636169}]}, "Id": "470c423b-7715-44da-a834-259968e62e18"}, {"BlockType": "WORD", "Confidence": 94.919677734375, "Text": ">", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022970275953412056, "Height": 0.008907956071197987, "Left": 0.3656720817089081, "Top": 0.5470844507217407}, "Polygon": [{"X": 0.3656720817089081, "Y": 0.5470844507217407}, {"X": 0.38864147663116455, "Y": 0.5470844507217407}, {"X": 0.3886423707008362, "Y": 0.5559923648834229}, {"X": 0.3656729757785797, "Y": 0.5559923648834229}]}, "Id": "5ac813e4-72f2-4992-9bb8-341f2cfc9fd0"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02006562240421772, "Height": 0.017020318657159805, "Left": 0.8509932160377502, "Top": 0.5419660210609436}, "Polygon": [{"X": 0.8509932160377502, "Y": 0.5419660806655884}, {"X": 0.8710575103759766, "Y": 0.5419660210609436}, {"X": 0.8710588812828064, "Y": 0.5589863657951355}, {"X": 0.8509945869445801, "Y": 0.5589863657951355}]}, "Id": "d2f12a34-fd08-4d24-b21c-6b6fe35ae699"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13567721843719482, "Height": 0.014926869422197342, "Left": 0.14278826117515564, "Top": 0.5681006908416748}, "Polygon": [{"X": 0.14278826117515564, "Y": 0.5681008100509644}, {"X": 0.2784639298915863, "Y": 0.5681006908416748}, {"X": 0.27846547961235046, "Y": 0.5830275416374207}, {"X": 0.14278988540172577, "Y": 0.5830276012420654}]}, "Id": "937b15fe-e905-4b55-af3e-89fac5012a98"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$91.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12356474250555038, "Height": 0.01906082034111023, "Left": 0.7518740296363831, "Top": 0.5626896023750305}, "Polygon": [{"X": 0.7518740296363831, "Y": 0.5626896619796753}, {"X": 0.8754372596740723, "Y": 0.5626896023750305}, {"X": 0.8754388093948364, "Y": 0.5817503333091736}, {"X": 0.751875638961792, "Y": 0.5817503929138184}]}, "Id": "b71e4db9-bc90-45dd-944b-b46142adce55"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09174610674381256, "Height": 0.013595961965620518, "Left": 0.14421646296977997, "Top": 0.5884254574775696}, "Polygon": [{"X": 0.14421646296977997, "Y": 0.5884254574775696}, {"X": 0.23596113920211792, "Y": 0.5884254574775696}, {"X": 0.23596256971359253, "Y": 0.6020213961601257}, {"X": 0.14421793818473816, "Y": 0.6020213961601257}]}, "Id": "cbc5c00b-9208-4a12-8010-53a5cc636147"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10387392342090607, "Height": 0.013472368009388447, "Left": 0.24729061126708984, "Top": 0.588137686252594}, "Polygon": [{"X": 0.24729061126708984, "Y": 0.5881377458572388}, {"X": 0.35116317868232727, "Y": 0.588137686252594}, {"X": 0.3511645197868347, "Y": 0.6016100645065308}, {"X": 0.24729201197624207, "Y": 0.6016100645065308}]}, "Id": "99fdee98-9a86-489b-aede-9e9cbd9578e1"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Charge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12417999655008316, "Height": 0.015810536220669746, "Left": 0.3603871762752533, "Top": 0.587481677532196}, "Polygon": [{"X": 0.3603871762752533, "Y": 0.5874817371368408}, {"X": 0.4845656752586365, "Y": 0.587481677532196}, {"X": 0.48456716537475586, "Y": 0.6032922267913818}, {"X": 0.36038875579833984, "Y": 0.6032922267913818}]}, "Id": "fe74e89a-e608-4b0b-927d-9044ff297d30"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$91.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12454274296760559, "Height": 0.01950083300471306, "Left": 0.7530006170272827, "Top": 0.5844067335128784}, "Polygon": [{"X": 0.7530006170272827, "Y": 0.5844067335128784}, {"X": 0.8775417804718018, "Y": 0.5844067335128784}, {"X": 0.8775433301925659, "Y": 0.6039075255393982}, {"X": 0.7530022263526917, "Y": 0.6039075255393982}]}, "Id": "7a47a989-b308-4581-8573-1a6ffee7cac5"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1390913426876068, "Height": 0.012761499732732773, "Left": 0.14305247366428375, "Top": 0.6114959120750427}, "Polygon": [{"X": 0.14305247366428375, "Y": 0.6114959120750427}, {"X": 0.2821424901485443, "Y": 0.6114959716796875}, {"X": 0.28214380145072937, "Y": 0.6242574453353882}, {"X": 0.14305385947227478, "Y": 0.6242573261260986}]}, "Id": "c2dbe2df-2663-42ee-949c-50ab6abe6907"}, {"BlockType": "WORD", "Confidence": 99.17350769042969, "Text": "Charge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1269756257534027, "Height": 0.014770295470952988, "Left": 0.2939199209213257, "Top": 0.6108127236366272}, "Polygon": [{"X": 0.2939199209213257, "Y": 0.6108127236366272}, {"X": 0.4208940863609314, "Y": 0.6108127236366272}, {"X": 0.4208955466747284, "Y": 0.6255829930305481}, {"X": 0.29392141103744507, "Y": 0.6255829334259033}]}, "Id": "3280eb44-2199-48ab-954d-976e6ae346d6"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "$8.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10247338563203812, "Height": 0.017269406467676163, "Left": 0.7763200998306274, "Top": 0.6070967316627502}, "Polygon": [{"X": 0.7763200998306274, "Y": 0.6070967316627502}, {"X": 0.8787921071052551, "Y": 0.607096791267395}, {"X": 0.878793478012085, "Y": 0.6243661642074585}, {"X": 0.776321530342102, "Y": 0.6243661046028137}]}, "Id": "bddd52d7-07ec-4a3f-a40c-d51920e2cb22"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0894695296883583, "Height": 0.014490507543087006, "Left": 0.1441158652305603, "Top": 0.6322702169418335}, "Polygon": [{"X": 0.1441158652305603, "Y": 0.6322702169418335}, {"X": 0.23358388245105743, "Y": 0.6322702765464783}, {"X": 0.2335854023694992, "Y": 0.6467607021331787}, {"X": 0.14411742985248566, "Y": 0.6467606425285339}]}, "Id": "ef8c8e6c-b9c4-4acc-8a18-0107cda5676a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$99.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12457958608865738, "Height": 0.019586818292737007, "Left": 0.7548099756240845, "Top": 0.6282671093940735}, "Polygon": [{"X": 0.7548099756240845, "Y": 0.6282671093940735}, {"X": 0.8793879747390747, "Y": 0.6282671689987183}, {"X": 0.8793895244598389, "Y": 0.6478539109230042}, {"X": 0.7548116445541382, "Y": 0.6478537917137146}]}, "Id": "891d3d79-7f00-4da3-8864-893d95093be8"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16054531931877136, "Height": 0.015378644689917564, "Left": 0.14344261586666107, "Top": 0.6547008156776428}, "Polygon": [{"X": 0.14344261586666107, "Y": 0.6547008156776428}, {"X": 0.3039863705635071, "Y": 0.6547009944915771}, {"X": 0.30398792028427124, "Y": 0.6700794696807861}, {"X": 0.14344428479671478, "Y": 0.670079231262207}]}, "Id": "7c66f1d0-bdd1-4030-87d0-4f16a432b038"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10958477109670639, "Height": 0.018479187041521072, "Left": 0.7728049159049988, "Top": 0.6508646011352539}, "Polygon": [{"X": 0.7728049159049988, "Y": 0.6508646011352539}, {"X": 0.8823882341384888, "Y": 0.6508647799491882}, {"X": 0.8823896646499634, "Y": 0.6693438291549683}, {"X": 0.7728064656257629, "Y": 0.6693436503410339}]}, "Id": "829d336c-40a6-43ce-8033-af1247a2b289"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Authorization", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23951470851898193, "Height": 0.013458259403705597, "Left": 0.13951319456100464, "Top": 0.6752681136131287}, "Polygon": [{"X": 0.13951319456100464, "Y": 0.6752681136131287}, {"X": 0.3790265619754791, "Y": 0.6752685308456421}, {"X": 0.3790279030799866, "Y": 0.6887263655662537}, {"X": 0.13951465487480164, "Y": 0.6887259483337402}]}, "Id": "fcf5ab30-82b6-469e-93ec-54fcaf0598d3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "12007196", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.177385151386261, "Height": 0.015971913933753967, "Left": 0.7045323252677917, "Top": 0.6715840101242065}, "Polygon": [{"X": 0.7045323252677917, "Y": 0.6715840101242065}, {"X": 0.8819162249565125, "Y": 0.6715843081474304}, {"X": 0.8819174766540527, "Y": 0.6875559687614441}, {"X": 0.7045337557792664, "Y": 0.6875556111335754}]}, "Id": "c2db2bbd-39b9-4af2-8744-6b5a0a965697"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11279182881116867, "Height": 0.01331867091357708, "Left": 0.1428855061531067, "Top": 0.6973837018013}, "Polygon": [{"X": 0.1428855061531067, "Y": 0.6973837018013}, {"X": 0.25567594170570374, "Y": 0.6973839402198792}, {"X": 0.25567731261253357, "Y": 0.7107023596763611}, {"X": 0.1428869366645813, "Y": 0.710702121257782}]}, "Id": "d6ee9e6a-350f-415e-8311-fdaa38d7c09c"}, {"BlockType": "WORD", "Confidence": 99.45731353759766, "Text": "RRR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08374407887458801, "Height": 0.016965564340353012, "Left": 0.803195059299469, "Top": 0.6927899718284607}, "Polygon": [{"X": 0.803195059299469, "Y": 0.6927899718284607}, {"X": 0.8869377970695496, "Y": 0.692790150642395}, {"X": 0.8869391083717346, "Y": 0.709755539894104}, {"X": 0.8031964302062988, "Y": 0.7097553610801697}]}, "Id": "2046eae9-9cee-403d-9046-68de0a085bdb"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Driver's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14111576974391937, "Height": 0.013166619464755058, "Left": 0.3199102282524109, "Top": 0.736911416053772}, "Polygon": [{"X": 0.3199102282524109, "Y": 0.736911416053772}, {"X": 0.4610247313976288, "Y": 0.7369118332862854}, {"X": 0.46102598309516907, "Y": 0.750078022480011}, {"X": 0.31991156935691833, "Y": 0.7500776052474976}]}, "Id": "6db2dfd2-444a-484a-8ad1-08210b2b0bc6"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17171697318553925, "Height": 0.015891581773757935, "Left": 0.47413748502731323, "Top": 0.7359166741371155}, "Polygon": [{"X": 0.47413748502731323, "Y": 0.7359166741371155}, {"X": 0.6458530426025391, "Y": 0.7359171509742737}, {"X": 0.6458544731140137, "Y": 0.751808226108551}, {"X": 0.474139004945755, "Y": 0.751807689666748}]}, "Id": "4662330b-eda0-4dc0-8bd7-5863497d4975"}, {"BlockType": "WORD", "Confidence": 99.6021957397461, "Text": "Supervisor's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22632354497909546, "Height": 0.016284354031085968, "Left": 0.28128552436828613, "Top": 0.8315232992172241}, "Polygon": [{"X": 0.28128552436828613, "Y": 0.8315232992172241}, {"X": 0.5076075196266174, "Y": 0.8315243721008301}, {"X": 0.5076090693473816, "Y": 0.8478076457977295}, {"X": 0.28128719329833984, "Y": 0.847806453704834}]}, "Id": "751cc41a-cb79-4a75-9e78-6dee1b5dc9c4"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17173366248607635, "Height": 0.015085752122104168, "Left": 0.5211144685745239, "Top": 0.8308393359184265}, "Polygon": [{"X": 0.5211144685745239, "Y": 0.8308393359184265}, {"X": 0.6928467750549316, "Y": 0.8308401703834534}, {"X": 0.6928480863571167, "Y": 0.8459250926971436}, {"X": 0.5211158394813538, "Y": 0.8459241986274719}]}, "Id": "12fb3058-da75-4e94-bb23-fede2dc1b199"}, {"BlockType": "WORD", "Confidence": 70.44145202636719, "Text": "29", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.25587156414985657, "Height": 0.03180857002735138, "Left": 0.3889331519603729, "Top": 0.8613988757133484}, "Polygon": [{"X": 0.3889331519603729, "Y": 0.8613988757133484}, {"X": 0.644801914691925, "Y": 0.861400306224823}, {"X": 0.6448047161102295, "Y": 0.8932074308395386}, {"X": 0.38893628120422363, "Y": 0.8932058811187744}]}, "Id": "4bcaa9c7-a94a-45d3-8e9a-df3e8b6557b6"}, {"BlockType": "WORD", "Confidence": 98.142333984375, "Text": "07/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1928347945213318, "Height": 0.011245655827224255, "Left": 0.3676324188709259, "Top": 0.9589479565620422}, "Polygon": [{"X": 0.3676324188709259, "Y": 0.9589479565620422}, {"X": 0.5604661703109741, "Y": 0.9589494466781616}, {"X": 0.5604672431945801, "Y": 0.97019362449646}, {"X": 0.36763355135917664, "Y": 0.9701921343803406}]}, "Id": "3dc274a7-977a-4768-94e4-09149ff64c9e"}, {"BlockType": "WORD", "Confidence": 94.70962524414062, "Text": "00:35", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09949705004692078, "Height": 0.011365711688995361, "Left": 0.5679749250411987, "Top": 0.9582560062408447}, "Polygon": [{"X": 0.5679749250411987, "Y": 0.9582560062408447}, {"X": 0.6674709320068359, "Y": 0.9582567811012268}, {"X": 0.6674719452857971, "Y": 0.9696217179298401}, {"X": 0.5679759383201599, "Y": 0.969620943069458}]}, "Id": "c87f8534-9331-492d-ba04-51a3dee0f9e4"}, {"BlockType": "WORD", "Confidence": 30.004981994628906, "Text": "www.merittogistics.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.4804471433162689, "Height": 0.012499780394136906, "Left": 0.27515482902526855, "Top": 0.9721404910087585}, "Polygon": [{"X": 0.27515482902526855, "Y": 0.9721404910087585}, {"X": 0.7556009292602539, "Y": 0.9721442461013794}, {"X": 0.7556019425392151, "Y": 0.9846402406692505}, {"X": 0.2751561105251312, "Y": 0.9846363663673401}]}, "Id": "51c3e458-d06c-46d9-a5d6-7f3d45e20a6b"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "e9db3bdf-90e7-4245-94d9-b08c147ca06d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "e9db3bdf-90e7-4245-94d9-b08c147ca06d", "content-type": "application/x-amz-json-1.1", "content-length": "66435", "date": "Thu, 18 Sep 2025 18:07:06 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "f98a36c1-e300-4d98-b293-c344c4e67908", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 18:07:10 GMT", "content-type": "application/json", "content-length": "2529", "connection": "keep-alive", "x-amzn-requestid": "f98a36c1-e300-4d98-b293-c344c4e67908"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify document type. The page shows a \"MERIT LOGISTIC\" header, includes PO #, Truck #, Trailer #, Door #. Activity line with charges: Work Order Charge, Service Charge, Total $99.00. There's driver signature, supervisor signature. This appears to be a \"lumper receipt\" or \"invoice\"? It says \"Bill To 3A TRANSPORT\" but it's from MERIT LOGISTIC to 3A TRANSPORT? Actually Bill To is 3A TRANSPORT; Carrier is 3A Transport. It includes work order charge, service charge. This looks like a \"lumper receipt\" (invoice for services like loading/unloading labor). The doc type \"lumper_receipt\" is defined as invoice or receipt for services provided, with PO, door number, date-time, initial pallets, finished pallets, carrier, service charge, total amount. The page has PO #, Door #, date-time, service charge, total amount. So likely lumper_receipt. Alternatively could be \"invoice\" but lumper receipt is a type of invoice for loading/unloading. The doc type doc_type list has lumper_receipt separate from invoice. The rule: \"Note: Difference between invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\" Here we have \"Work Order Charge\" and \"Service Charge\". Not explicitly \"Lumper\". But this appears to be a billing for work order. The presence of \"Work Order Charge\" and \"Service Charge\" and \"Total\" and \"Bill To\" indicates invoice. But the presence of \"PO #\" and \"Truck #\" \"Door #\" indicates maybe a lumper receipt. Let's examine definition: lumper receipt — Invoice or Receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page has PO#, Door#, date-time, carrier, service charge, total amount. But no \"Initial pallets\" or \"finished pallets\" but those are optional. So it meets criteria.\n\nThus classify as lumper_receipt.\n\nNo continuation. So output exactly one object with page_no 1 and doc_type lumper_receipt.\n\nWe must use tool call."}}}, {"toolUse": {"toolUseId": "tooluse_AZHJuPt7TQWndvohJTEw9w", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2354, "outputTokens": 519, "totalTokens": 2873}, "metrics": {"latencyMs": 2088}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/c87b52ed_21_lumper_receipt_11.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nMERIT\nLOGISTIC\n(949) 481-0685\n<PERSON><PERSON> (ALDFRE), <PERSON>, MD\nTax Id\n*********\nWO #\nALDFRE9734307252025203958\nTime\n7/25/2025 8:39:00 PM\nPO #\n7507418921\nTruck #\n101\nTrailer #\n()--288\nDoor #\n34\nClient's Dept\nUnipading (GRP)\nProduct\nGM\nVendor\nPREMIUM WATERS INC\nCarrier\n3A TRANSPORT\nBill To\n3A TRANSPORT\nQTY < Case >\n1\nActivity\n$91.00\nWork Order Charge\n$91.00\nService Charge\n$8.00\nTotal\n$99.00\nPayment\nRelay\nAuthorization\n12007196\nDriver\nRRR\nDriver's Signature\nSupervisor's Signature\n29\n07/26/2025 00:35\nwww.merittogistics.com\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}