{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 7.16363324215763e-09}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "392a27d8-fb42-43b9-a82d-fd9de8954fe1", "Relationships": [{"Type": "CHILD", "Ids": ["bfc22782-222a-4608-8841-0f0ec60f186e", "edde27f4-ed7a-4b13-9173-a1d7e16d111a", "7d5c810e-d35b-4922-b322-1bb567940f21", "6ce1d066-1b73-439c-a9d4-530f2f233a3a", "00e56cec-e595-494e-8f9b-813cc1cb022d", "c7a984e5-1975-4026-bfa4-ac905df60875", "ae4069df-212f-431b-befd-babbbdd700a0", "6a8ec435-e59d-4f1c-af6e-fd603c38b5af", "3ab518e3-40ea-47db-95aa-bb301e2bed8c", "40cd495e-0e82-4e08-8717-f06ec7c7d313", "c077c6d2-53fa-4c66-8d5f-8e2f85a011a4", "a490d96c-486b-4e37-8ef8-ab38e1e8fc00", "c139cee8-e2f3-4b6c-8de3-fa597ffce1bb", "a86de19e-84e0-4924-b5b2-1f6b055e0263", "fb404f88-f0bd-4a72-84a6-5d87b6015241", "80fb2dab-cea2-422f-b65b-5ab5614d3d7e", "486bde90-deda-47e5-a620-e82fe1e52d40", "53f07544-cfb7-408f-b65c-0a60819a8349", "44362db3-80f5-4b50-b229-7073527a8273", "98c92b12-33b4-45a6-966d-0eddd90a168b", "95075af8-fcf5-4961-95f3-5e1579ea29bc", "50440fa1-a95b-4d20-99cf-330afd66154a", "1b973f20-c87b-4c0d-9212-07278dd953d0", "73837c52-3cda-47c4-9d9e-18e81d8666c2", "9edbc219-0ad8-4883-b458-0e3e6d932776", "48e535e4-a0b9-46b8-b29c-473ba7516cbe", "7956ee30-48bb-41da-9ce3-38694888ec87", "b73a0755-e17d-458f-8243-de524c031326"]}]}, {"BlockType": "LINE", "Confidence": 94.17201232910156, "Text": "Forward Air", "Geometry": {"BoundingBox": {"Width": 0.15412963926792145, "Height": 0.013688451610505581, "Left": 0.16126684844493866, "Top": 0.034402117133140564}, "Polygon": [{"X": 0.16126687824726105, "Y": 0.034402117133140564}, {"X": 0.3153964877128601, "Y": 0.034403085708618164}, {"X": 0.3153964877128601, "Y": 0.04809056967496872}, {"X": 0.16126684844493866, "Y": 0.048089612275362015}]}, "Id": "bfc22782-222a-4608-8841-0f0ec60f186e", "Relationships": [{"Type": "CHILD", "Ids": ["ca88c486-b446-40e8-b54d-393976713d9b", "0854e617-7ca2-441c-b288-d561c3145430"]}]}, {"BlockType": "LINE", "Confidence": 99.34197998046875, "Text": "Forward Air W & R Certificate", "Geometry": {"BoundingBox": {"Width": 0.3193495273590088, "Height": 0.014665469527244568, "Left": 0.3025369942188263, "Top": 0.10525936633348465}, "Polygon": [{"X": 0.3025369942188263, "Y": 0.10525936633348465}, {"X": 0.6218864917755127, "Y": 0.10526128113269806}, {"X": 0.6218864917755127, "Y": 0.11992483586072922}, {"X": 0.3025369942188263, "Y": 0.1199229434132576}]}, "Id": "edde27f4-ed7a-4b13-9173-a1d7e16d111a", "Relationships": [{"Type": "CHILD", "Ids": ["8700c75a-962c-489e-97c7-bd826fa1b159", "cda94c7b-79b4-44a5-9864-91be3c29e651", "8abdfa96-0ac0-44e4-aca9-a869f2e7f1a1", "b9e9c6d7-7d5d-4221-b925-18aed67e03d5", "03922d7f-02a1-4dd9-8a14-111453c5944a", "e80a9fd5-a11a-4b63-92c9-c0f3ae2bf049"]}]}, {"BlockType": "LINE", "Confidence": 99.6872329711914, "Text": "Airbill #: ********", "Geometry": {"BoundingBox": {"Width": 0.15737362205982208, "Height": 0.011278245598077774, "Left": 0.09197533130645752, "Top": 0.18528351187705994}, "Polygon": [{"X": 0.0919753685593605, "Y": 0.18528351187705994}, {"X": 0.2493489533662796, "Y": 0.18528440594673157}, {"X": 0.2493489384651184, "Y": 0.1965617537498474}, {"X": 0.09197533130645752, "Y": 0.19656087458133698}]}, "Id": "7d5c810e-d35b-4922-b322-1bb567940f21", "Relationships": [{"Type": "CHILD", "Ids": ["6fa6f8f4-5d34-412f-8bf9-4dfaa382488d", "2fa65513-facb-49ff-b5f1-001be70dd6cb", "23a65cba-2036-4ef7-a7f8-95eafe9435fd"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Date:", "Geometry": {"BoundingBox": {"Width": 0.038359638303518295, "Height": 0.009832717478275299, "Left": 0.6174425482749939, "Top": 0.18658235669136047}, "Polygon": [{"X": 0.6174425482749939, "Y": 0.18658235669136047}, {"X": 0.6558021903038025, "Y": 0.1865825653076172}, {"X": 0.6558021903038025, "Y": 0.19641506671905518}, {"X": 0.6174426078796387, "Y": 0.19641485810279846}]}, "Id": "6ce1d066-1b73-439c-a9d4-530f2f233a3a", "Relationships": [{"Type": "CHILD", "Ids": ["89ec67cd-20d5-42f9-b506-87b59847e5ab"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1/25/2024", "Geometry": {"BoundingBox": {"Width": 0.07774744927883148, "Height": 0.012776292860507965, "Left": 0.7072635293006897, "Top": 0.1853291094303131}, "Polygon": [{"X": 0.7072635293006897, "Y": 0.1853291094303131}, {"X": 0.7850109338760376, "Y": 0.18532955646514893}, {"X": 0.7850109934806824, "Y": 0.19810540974140167}, {"X": 0.7072635889053345, "Y": 0.19810496270656586}]}, "Id": "00e56cec-e595-494e-8f9b-813cc1cb022d", "Relationships": [{"Type": "CHILD", "Ids": ["61a3e397-88e0-4c07-a0e7-5b36c7fd3492"]}]}, {"BlockType": "LINE", "Confidence": 99.94941711425781, "Text": "Shipper: MRB LOGISTICS LLC ROAD ARMOR", "Geometry": {"BoundingBox": {"Width": 0.3236578702926636, "Height": 0.01277223601937294, "Left": 0.09232305735349655, "Top": 0.22268018126487732}, "Polygon": [{"X": 0.09232309460639954, "Y": 0.22268018126487732}, {"X": 0.41598090529441833, "Y": 0.22268196940422058}, {"X": 0.41598090529441833, "Y": 0.23545241355895996}, {"X": 0.09232305735349655, "Y": 0.2354506403207779}]}, "Id": "c7a984e5-1975-4026-bfa4-ac905df60875", "Relationships": [{"Type": "CHILD", "Ids": ["06c1a4b4-5a2d-47e6-8cf9-d87f50d23acb", "90d9dfd6-e156-4f94-8ca4-428a5b391da5", "b65428a8-712e-437e-a194-fd6c906dee0d", "9392eeb6-3335-41c3-8c9f-e70270244055", "94e48301-b34f-4742-b801-091408fe8275", "3abc69f7-e71c-42f4-96ed-8507e52676ba"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Origin:", "Geometry": {"BoundingBox": {"Width": 0.04813851788640022, "Height": 0.012443787418305874, "Left": 0.6120847463607788, "Top": 0.22298645973205566}, "Polygon": [{"X": 0.6120847463607788, "Y": 0.22298645973205566}, {"X": 0.6602232456207275, "Y": 0.22298672795295715}, {"X": 0.6602233052253723, "Y": 0.2354302555322647}, {"X": 0.6120848059654236, "Y": 0.23542998731136322}]}, "Id": "ae4069df-212f-431b-befd-babbbdd700a0", "Relationships": [{"Type": "CHILD", "Ids": ["7470b072-94e0-4de5-9d64-180bd0a6a104"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "DFW", "Geometry": {"BoundingBox": {"Width": 0.034777652472257614, "Height": 0.009693896397948265, "Left": 0.7075534462928772, "Top": 0.22326555848121643}, "Polygon": [{"X": 0.7075534462928772, "Y": 0.22326555848121643}, {"X": 0.7423310279846191, "Y": 0.22326575219631195}, {"X": 0.7423310875892639, "Y": 0.23295946419239044}, {"X": 0.7075534462928772, "Y": 0.23295927047729492}]}, "Id": "6a8ec435-e59d-4f1c-af6e-fd603c38b5af", "Relationships": [{"Type": "CHILD", "Ids": ["fac18b0b-9273-4f68-bdfb-272d9ba65b19"]}]}, {"BlockType": "LINE", "Confidence": 99.94764709472656, "Text": "Bill To: 2836616", "Geometry": {"BoundingBox": {"Width": 0.12989787757396698, "Height": 0.010404873639345169, "Left": 0.09862078726291656, "Top": 0.25932803750038147}, "Polygon": [{"X": 0.09862080961465836, "Y": 0.25932803750038147}, {"X": 0.22851866483688354, "Y": 0.2593287527561188}, {"X": 0.22851864993572235, "Y": 0.26973292231559753}, {"X": 0.09862078726291656, "Y": 0.2697322368621826}]}, "Id": "3ab518e3-40ea-47db-95aa-bb301e2bed8c", "Relationships": [{"Type": "CHILD", "Ids": ["393a78ee-731e-4c41-9f4c-e83f88e4c11c", "00960919-2a19-482b-86ae-fcf099ab280d", "b9c304d2-3b6c-4a0a-98ea-862ca26643fb"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "Destination:", "Geometry": {"BoundingBox": {"Width": 0.0881313905119896, "Height": 0.00998605415225029, "Left": 0.5923346877098083, "Top": 0.25968414545059204}, "Polygon": [{"X": 0.5923346877098083, "Y": 0.25968414545059204}, {"X": 0.6804660558700562, "Y": 0.25968462228775024}, {"X": 0.6804661154747009, "Y": 0.2696702182292938}, {"X": 0.5923347473144531, "Y": 0.2696697413921356}]}, "Id": "40cd495e-0e82-4e08-8717-f06ec7c7d313", "Relationships": [{"Type": "CHILD", "Ids": ["680cbab0-7ed7-4b48-8f83-c29168f30520"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "ATL", "Geometry": {"BoundingBox": {"Width": 0.0275449026376009, "Height": 0.00968901626765728, "Left": 0.70647794008255, "Top": 0.25991469621658325}, "Polygon": [{"X": 0.70647794008255, "Y": 0.25991469621658325}, {"X": 0.7340227961540222, "Y": 0.2599148452281952}, {"X": 0.734022855758667, "Y": 0.2696036994457245}, {"X": 0.70647794008255, "Y": 0.26960355043411255}]}, "Id": "c077c6d2-53fa-4c66-8d5f-8e2f85a011a4", "Relationships": [{"Type": "CHILD", "Ids": ["be1d4785-4de8-409b-80ef-c90c40dbb4b5"]}]}, {"BlockType": "LINE", "Confidence": 99.89546203613281, "Text": "Class Adjustments", "Geometry": {"BoundingBox": {"Width": 0.1678381860256195, "Height": 0.015858998522162437, "Left": 0.34880879521369934, "Top": 0.2969183623790741}, "Polygon": [{"X": 0.34880879521369934, "Y": 0.2969183623790741}, {"X": 0.5166469812393188, "Y": 0.2969192564487457}, {"X": 0.5166469812393188, "Y": 0.3127773702144623}, {"X": 0.34880879521369934, "Y": 0.31277650594711304}]}, "Id": "a490d96c-486b-4e37-8ef8-ab38e1e8fc00", "Relationships": [{"Type": "CHILD", "Ids": ["51af299f-777f-4c72-b027-558f479f65cc", "d386a1ba-c091-40ed-98a1-f83fb4e5c5fd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Booked", "Geometry": {"BoundingBox": {"Width": 0.055543702095746994, "Height": 0.01030503585934639, "Left": 0.08692061901092529, "Top": 0.3187428414821625}, "Polygon": [{"X": 0.08692064881324768, "Y": 0.3187428414821625}, {"X": 0.14246432483196259, "Y": 0.31874310970306396}, {"X": 0.1424642950296402, "Y": 0.3290478587150574}, {"X": 0.08692061901092529, "Y": 0.3290475904941559}]}, "Id": "c139cee8-e2f3-4b6c-8de3-fa597ffce1bb", "Relationships": [{"Type": "CHILD", "Ids": ["d1dea2ae-6840-4cc8-9ece-c70d55d14a56"]}]}, {"BlockType": "LINE", "Confidence": 99.76403045654297, "Text": "REWWeight", "Geometry": {"BoundingBox": {"Width": 0.08924861997365952, "Height": 0.012708719819784164, "Left": 0.18453900516033173, "Top": 0.32840272784233093}, "Polygon": [{"X": 0.18453902006149292, "Y": 0.32840272784233093}, {"X": 0.27378761768341064, "Y": 0.32840317487716675}, {"X": 0.27378761768341064, "Y": 0.3411114513874054}, {"X": 0.18453900516033173, "Y": 0.3411110043525696}]}, "Id": "a86de19e-84e0-4924-b5b2-1f6b055e0263", "Relationships": [{"Type": "CHILD", "Ids": ["64123cb4-9b52-4452-8a4f-b98e6a2fe4f7"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Booked Class", "Geometry": {"BoundingBox": {"Width": 0.09739977866411209, "Height": 0.010364463552832603, "Left": 0.31590449810028076, "Top": 0.32809266448020935}, "Polygon": [{"X": 0.31590449810028076, "Y": 0.32809266448020935}, {"X": 0.41330426931381226, "Y": 0.32809317111968994}, {"X": 0.41330426931381226, "Y": 0.3384571373462677}, {"X": 0.31590449810028076, "Y": 0.3384566307067871}]}, "Id": "fb404f88-f0bd-4a72-84a6-5d87b6015241", "Relationships": [{"Type": "CHILD", "Ids": ["120f269a-298b-4e4b-a5b7-cbffda7f547d", "7f5a0a28-c6ff-4aee-8ef7-943cb8b4e003"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Adjusted Class", "Geometry": {"BoundingBox": {"Width": 0.1088501513004303, "Height": 0.012754678726196289, "Left": 0.4453728199005127, "Top": 0.3281421363353729}, "Polygon": [{"X": 0.4453728199005127, "Y": 0.3281421363353729}, {"X": 0.5542229413986206, "Y": 0.3281427025794983}, {"X": 0.5542230010032654, "Y": 0.3408968150615692}, {"X": 0.4453728199005127, "Y": 0.34089627861976624}]}, "Id": "80fb2dab-cea2-422f-b65b-5ab5614d3d7e", "Relationships": [{"Type": "CHILD", "Ids": ["a8d22b3a-fb81-43ae-b006-2c2f9b6c7639", "eb7330da-df03-4aef-83ac-8061fefaa4f0"]}]}, {"BlockType": "LINE", "Confidence": 99.9072265625, "Text": "NMFC Item", "Geometry": {"BoundingBox": {"Width": 0.08338245004415512, "Height": 0.009787711314857006, "Left": 0.594211995601654, "Top": 0.3286419212818146}, "Polygon": [{"X": 0.594211995601654, "Y": 0.3286419212818146}, {"X": 0.6775944232940674, "Y": 0.328642338514328}, {"X": 0.6775944828987122, "Y": 0.33842962980270386}, {"X": 0.5942120552062988, "Y": 0.33842918276786804}]}, "Id": "486bde90-deda-47e5-a620-e82fe1e52d40", "Relationships": [{"Type": "CHILD", "Ids": ["2f78eade-dd4e-447a-8137-7a8a525864c0", "2b5c06ac-c968-4c5c-9102-304f05d3e207"]}]}, {"BlockType": "LINE", "Confidence": 96.59249114990234, "Text": "P.C. F.", "Geometry": {"BoundingBox": {"Width": 0.04510556906461716, "Height": 0.009790481999516487, "Left": 0.7489345073699951, "Top": 0.32866254448890686}, "Polygon": [{"X": 0.7489345073699951, "Y": 0.32866254448890686}, {"X": 0.7940400242805481, "Y": 0.32866278290748596}, {"X": 0.7940400838851929, "Y": 0.3384530246257782}, {"X": 0.7489345073699951, "Y": 0.3384527862071991}]}, "Id": "53f07544-cfb7-408f-b65c-0a60819a8349", "Relationships": [{"Type": "CHILD", "Ids": ["cec7b3a2-3fdc-4d99-ba5e-5412fc87148d", "16bea380-1a85-459a-b617-0b478f26d02d"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Weight", "Geometry": {"BoundingBox": {"Width": 0.05425461009144783, "Height": 0.012347626499831676, "Left": 0.08622292429208755, "Top": 0.33718809485435486}, "Polygon": [{"X": 0.08622295409440994, "Y": 0.33718809485435486}, {"X": 0.14047753810882568, "Y": 0.33718836307525635}, {"X": 0.1404775083065033, "Y": 0.34953573346138}, {"X": 0.08622292429208755, "Y": 0.34953543543815613}]}, "Id": "44362db3-80f5-4b50-b229-7073527a8273", "Relationships": [{"Type": "CHILD", "Ids": ["834b296d-addc-492b-becc-13babcd3cc74"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "100", "Geometry": {"BoundingBox": {"Width": 0.026616014540195465, "Height": 0.00987878255546093, "Left": 0.10967487841844559, "Top": 0.3559759259223938}, "Polygon": [{"X": 0.10967490077018738, "Y": 0.3559759259223938}, {"X": 0.13629089295864105, "Y": 0.35597604513168335}, {"X": 0.13629086315631866, "Y": 0.3658547103404999}, {"X": 0.10967487841844559, "Y": 0.36585456132888794}]}, "Id": "98c92b12-33b4-45a6-966d-0eddd90a168b", "Relationships": [{"Type": "CHILD", "Ids": ["6377ab82-8d5e-4f97-9cd4-5ed73ce00ea5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "345", "Geometry": {"BoundingBox": {"Width": 0.026320550590753555, "Height": 0.00968103762716055, "Left": 0.2160252332687378, "Top": 0.35600343346595764}, "Polygon": [{"X": 0.216025248169899, "Y": 0.35600343346595764}, {"X": 0.24234578013420105, "Y": 0.3560035824775696}, {"X": 0.24234576523303986, "Y": 0.36568447947502136}, {"X": 0.2160252332687378, "Y": 0.3656843602657318}]}, "Id": "95075af8-fcf5-4961-95f3-5e1579ea29bc", "Relationships": [{"Type": "CHILD", "Ids": ["55c7959d-aa3d-4cbc-9a5a-efd4294f5ba7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "300", "Geometry": {"BoundingBox": {"Width": 0.0272364504635334, "Height": 0.010146199725568295, "Left": 0.3514963388442993, "Top": 0.3557758629322052}, "Polygon": [{"X": 0.3514963388442993, "Y": 0.3557758629322052}, {"X": 0.3787328004837036, "Y": 0.35577598214149475}, {"X": 0.3787328004837036, "Y": 0.36592206358909607}, {"X": 0.3514963388442993, "Y": 0.36592191457748413}]}, "Id": "50440fa1-a95b-4d20-99cf-330afd66154a", "Relationships": [{"Type": "CHILD", "Ids": ["c5912f0f-8fbf-451b-a225-8eb79bd51b5c"]}]}, {"BlockType": "LINE", "Confidence": 98.65852355957031, "Text": "1PC: 40*48*32", "Geometry": {"BoundingBox": {"Width": 0.07949396222829819, "Height": 0.007710338570177555, "Left": 0.16371987760066986, "Top": 0.4023709297180176}, "Polygon": [{"X": 0.16371989250183105, "Y": 0.4023709297180176}, {"X": 0.24321384727954865, "Y": 0.4023713171482086}, {"X": 0.24321383237838745, "Y": 0.41008126735687256}, {"X": 0.16371987760066986, "Y": 0.4100808799266815}]}, "Id": "1b973f20-c87b-4c0d-9212-07278dd953d0", "Relationships": [{"Type": "CHILD", "Ids": ["7ea76642-fab0-40b5-b60e-3d0c46dad912", "6a831407-5fda-49e9-8f17-651af989545a"]}]}, {"BlockType": "LINE", "Confidence": 98.1207275390625, "Text": "The above shipment was inspected by the Forward Air W & R department. The Airbill number, Shipper", "Geometry": {"BoundingBox": {"Width": 0.7500714659690857, "Height": 0.012996169738471508, "Left": 0.08598431944847107, "Top": 0.4355888068675995}, "Polygon": [{"X": 0.08598435670137405, "Y": 0.4355888068675995}, {"X": 0.8360557556152344, "Y": 0.43559232354164124}, {"X": 0.8360558152198792, "Y": 0.44858497381210327}, {"X": 0.08598431944847107, "Y": 0.4485814869403839}]}, "Id": "73837c52-3cda-47c4-9d9e-18e81d8666c2", "Relationships": [{"Type": "CHILD", "Ids": ["31a777f9-7e71-4c2d-8e30-395b03341823", "afb096c8-a714-4671-8bd4-3bb37176d154", "d26f1617-53bd-4c62-9626-9e1a111cacdb", "dc250070-9732-4c80-abc5-679aee114336", "bed66289-cd52-4e4c-9952-97d2cc7d0e0f", "e19f961b-2e42-4c07-b952-8e303f7e701d", "92b149e6-0540-4d69-867f-3e17f1b95d10", "5a5a7ea5-d1ee-4302-a3fc-be253ccb44bb", "8aa94bc0-337d-4e9f-86c2-279f56d85478", "a0667e18-e3ee-4f1e-8dd5-b41d1a78ad6c", "64a04be0-da4e-4a69-b56e-0a8edb1c1794", "5279bf46-8670-4a14-bbf3-710256ed99b3", "70b71783-c1e6-4f81-bb0a-150737eff363", "8f8f6530-f23d-4b94-88f9-b5e0fd6908dc", "6452d4b2-fbda-4639-a8fe-392cf75779d3", "f135e208-46d7-40cc-a19c-************"]}]}, {"BlockType": "LINE", "Confidence": 99.**************, "Text": "and Bill To account listed above on the Certificate. The above information shows changes made to this", "Geometry": {"BoundingBox": {"Width": 0.***************, "Height": 0.012986267916858196, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "9edbc219-0ad8-4883-b458-0e3e6d932776", "Relationships": [{"Type": "CHILD", "Ids": ["7bf69501-ad45-4909-be0e-8e64fe6f1206", "242ca59d-75ed-413c-9c2c-b2d5b67bfddc", "a31b31df-32b7-4c49-bab6-2b3598c66099", "6c991a27-7c50-4db1-88d9-f0a75d43e99d", "386d14db-91dc-4bde-9fa3-ccbe04e59d33", "73471b66-d999-45c1-8dd9-3c8fc8ecfb30", "7e1ba0ba-b2c3-42da-9581-c0702c890409", "9d592dfd-2cf2-4956-a88a-9bbbd961b480", "1a5d7393-e844-4239-8224-239d58986467", "5a810f9f-e566-449b-b052-4cfbcf703d11", "d8b93118-af4c-439a-a396-c7ae4adc03b4", "68ff224d-f97c-4434-a71f-40fd0dac00c8", "4ba6f198-9fbe-4af3-b92d-f71bf3f09ced", "e772e79b-defb-47cc-9939-0faea45fbac7", "f354b836-b9be-496f-a8e2-72ff43c7ff6d", "50ded9e9-0045-428c-ab33-b572bcfbae0f", "579cc920-d0bd-4b15-a702-c26f3976d7b7"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "shipment.", "Geometry": {"BoundingBox": {"Width": 0.07235146313905716, "Height": 0.01266644336283207, "Left": 0.08650197088718414, "Top": 0.4725324213504791}, "Polygon": [{"X": 0.08650200814008713, "Y": 0.4725324213504791}, {"X": 0.1588534265756607, "Y": 0.4725327491760254}, {"X": 0.15885339677333832, "Y": 0.48519885540008545}, {"X": 0.08650197088718414, "Y": 0.4851985275745392}]}, "Id": "48e535e4-a0b9-46b8-b29c-473ba7516cbe", "Relationships": [{"Type": "CHILD", "Ids": ["84994e3c-9c0a-414d-be57-96ab72423455"]}]}, {"BlockType": "LINE", "Confidence": 99.98687744140625, "Text": "If you have any questions about this change please contact our Customer Service Center at", "Geometry": {"BoundingBox": {"Width": 0.6645308136940002, "Height": 0.013153977692127228, "Left": 0.12913711369037628, "Top": 0.5438684821128845}, "Polygon": [{"X": 0.12913714349269867, "Y": 0.5438684821128845}, {"X": 0.7936679124832153, "Y": 0.543871283531189}, {"X": 0.7936679124832153, "Y": 0.5570224523544312}, {"X": 0.12913711369037628, "Y": 0.5570196509361267}]}, "Id": "7956ee30-48bb-41da-9ce3-38694888ec87", "Relationships": [{"Type": "CHILD", "Ids": ["fc941e27-956a-417d-951e-92b7c345a213", "94396c65-a423-4841-99c7-689eb1bdd7ed", "633dabc9-46fd-446a-ae58-fbe5c3436f1b", "b6ae26bd-3fa3-4d99-9470-b0e6263b4132", "1cb3448d-a4c2-4a80-a2f1-d4626c11de8d", "685de4d3-a89f-4aa9-a03f-89a4e5f72bf0", "87020327-7841-47cc-a993-2544dcae8934", "cb09da28-d4fa-48e5-b42d-02f4752dfaea", "f184fdf3-5adf-44ef-801f-2afb3bf87362", "08ceea53-a0cf-4ac9-91a9-d8a45b2a208a", "fe46a701-35e5-47c4-9140-e3798c34db4a", "79a5400f-8f09-437e-a835-609d598bd81f", "8f66842b-cd80-4b0d-bfd6-b0887efc8112", "58fb00bb-7b2b-421f-bdf1-4c4344e329c0", "459fbc1a-1d60-4ccd-9e4d-0bb08ee5d01d"]}]}, {"BlockType": "LINE", "Confidence": 99.04296875, "Text": "(800) 726-6654", "Geometry": {"BoundingBox": {"Width": 0.11204735189676285, "Height": 0.013037525117397308, "Left": 0.405710369348526, "Top": 0.5620744824409485}, "Polygon": [{"X": 0.405710369348526, "Y": 0.5620744824409485}, {"X": 0.5177577137947083, "Y": 0.5620749592781067}, {"X": 0.5177577137947083, "Y": 0.5751120448112488}, {"X": 0.405710369348526, "Y": 0.5751115679740906}]}, "Id": "b73a0755-e17d-458f-8243-de524c031326", "Relationships": [{"Type": "CHILD", "Ids": ["303667fa-3104-42a6-addb-23cbe7a07015", "31d87806-8d61-4642-9eb9-9141dfa11595"]}]}, {"BlockType": "WORD", "Confidence": 95.93012237548828, "Text": "Forward", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11317352205514908, "Height": 0.013688197359442711, "Left": 0.16126684844493866, "Top": 0.034402117133140564}, "Polygon": [{"X": 0.16126687824726105, "Y": 0.034402117133140564}, {"X": 0.27444037795066833, "Y": 0.03440282866358757}, {"X": 0.27444034814834595, "Y": 0.048090316355228424}, {"X": 0.16126684844493866, "Y": 0.048089612275362015}]}, "Id": "ca88c486-b446-40e8-b54d-393976713d9b"}, {"BlockType": "WORD", "Confidence": 92.41390228271484, "Text": "Air", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04157983884215355, "Height": 0.013339693658053875, "Left": 0.27381664514541626, "Top": 0.03455682843923569}, "Polygon": [{"X": 0.27381664514541626, "Y": 0.03455682843923569}, {"X": 0.3153964877128601, "Y": 0.03455708920955658}, {"X": 0.3153964877128601, "Y": 0.04789652302861214}, {"X": 0.27381664514541626, "Y": 0.047896262258291245}]}, "Id": "0854e617-7ca2-441c-b288-d561c3145430"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Forward", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08887369185686111, "Height": 0.014394747093319893, "Left": 0.3025369942188263, "Top": 0.105385921895504}, "Polygon": [{"X": 0.3025369942188263, "Y": 0.105385921895504}, {"X": 0.3914106786251068, "Y": 0.10538645833730698}, {"X": 0.3914106786251068, "Y": 0.11978067457675934}, {"X": 0.3025369942188263, "Y": 0.11978014558553696}]}, "Id": "8700c75a-962c-489e-97c7-bd826fa1b159"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Air", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03195105493068695, "Height": 0.01409904845058918, "Left": 0.39772143959999084, "Top": 0.10554437339305878}, "Polygon": [{"X": 0.39772143959999084, "Y": 0.10554437339305878}, {"X": 0.429672509431839, "Y": 0.1055445671081543}, {"X": 0.429672509431839, "Y": 0.11964341998100281}, {"X": 0.39772143959999084, "Y": 0.11964323371648788}]}, "Id": "cda94c7b-79b4-44a5-9864-91be3c29e651"}, {"BlockType": "WORD", "Confidence": 97.8698959350586, "Text": "W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0236823707818985, "Height": 0.013685408979654312, "Left": 0.43543264269828796, "Top": 0.10601037740707397}, "Polygon": [{"X": 0.43543264269828796, "Y": 0.10601037740707397}, {"X": 0.45911499857902527, "Y": 0.10601051896810532}, {"X": 0.45911502838134766, "Y": 0.11969579011201859}, {"X": 0.43543267250061035, "Y": 0.11969564855098724}]}, "Id": "8abdfa96-0ac0-44e4-aca9-a869f2e7f1a1"}, {"BlockType": "WORD", "Confidence": 98.818359375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017722727730870247, "Height": 0.014297186397016048, "Left": 0.4653174877166748, "Top": 0.10562682896852493}, "Polygon": [{"X": 0.4653174877166748, "Y": 0.10562682896852493}, {"X": 0.4830401837825775, "Y": 0.10562693327665329}, {"X": 0.4830402135848999, "Y": 0.11992401629686356}, {"X": 0.4653174877166748, "Y": 0.1199239045381546}]}, "Id": "b9e9c6d7-7d5d-4221-b925-18aed67e03d5"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013600464910268784, "Height": 0.01348913460969925, "Left": 0.49038413166999817, "Top": 0.1062040701508522}, "Polygon": [{"X": 0.49038413166999817, "Y": 0.1062040701508522}, {"X": 0.5039845705032349, "Y": 0.10620415210723877}, {"X": 0.5039845705032349, "Y": 0.11969320476055145}, {"X": 0.49038413166999817, "Y": 0.11969312280416489}]}, "Id": "03922d7f-02a1-4dd9-8a14-111453c5944a"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1117023304104805, "Height": 0.014597462490200996, "Left": 0.5101841688156128, "Top": 0.10526061058044434}, "Polygon": [{"X": 0.5101841688156128, "Y": 0.10526061058044434}, {"X": 0.6218864917755127, "Y": 0.10526128113269806}, {"X": 0.6218864917755127, "Y": 0.11985807120800018}, {"X": 0.5101842284202576, "Y": 0.11985740810632706}]}, "Id": "e80a9fd5-a11a-4b63-92c9-c0f3ae2bf049"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "Airbill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04221639409661293, "Height": 0.010361174121499062, "Left": 0.09197533875703812, "Top": 0.1859847456216812}, "Polygon": [{"X": 0.09197536110877991, "Y": 0.1859847456216812}, {"X": 0.13419173657894135, "Y": 0.18598498404026031}, {"X": 0.13419170677661896, "Y": 0.19634592533111572}, {"X": 0.09197533875703812, "Y": 0.19634568691253662}]}, "Id": "6fa6f8f4-5d34-412f-8bf9-4dfaa382488d"}, {"BlockType": "WORD", "Confidence": 99.228515625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013339610770344734, "Height": 0.00980157870799303, "Left": 0.13883639872074127, "Top": 0.18652842938899994}, "Polygon": [{"X": 0.13883641362190247, "Y": 0.18652842938899994}, {"X": 0.15217600762844086, "Y": 0.1865285038948059}, {"X": 0.15217597782611847, "Y": 0.1963300108909607}, {"X": 0.13883639872074127, "Y": 0.19632993638515472}]}, "Id": "2fa65513-facb-49ff-b5f1-001be70dd6cb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08450331538915634, "Height": 0.011277832090854645, "Left": 0.16484564542770386, "Top": 0.18528392910957336}, "Polygon": [{"X": 0.16484566032886505, "Y": 0.18528392910957336}, {"X": 0.2493489533662796, "Y": 0.18528440594673157}, {"X": 0.2493489384651184, "Y": 0.1965617537498474}, {"X": 0.16484564542770386, "Y": 0.1965612769126892}]}, "Id": "23a65cba-2036-4ef7-a7f8-95eafe9435fd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038359638303518295, "Height": 0.009832717478275299, "Left": 0.6174425482749939, "Top": 0.18658235669136047}, "Polygon": [{"X": 0.6174425482749939, "Y": 0.18658235669136047}, {"X": 0.6558021903038025, "Y": 0.1865825653076172}, {"X": 0.6558021903038025, "Y": 0.19641506671905518}, {"X": 0.6174426078796387, "Y": 0.19641485810279846}]}, "Id": "89ec67cd-20d5-42f9-b506-87b59847e5ab"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1/25/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07774744927883148, "Height": 0.012776292860507965, "Left": 0.7072635293006897, "Top": 0.1853291094303131}, "Polygon": [{"X": 0.7072635293006897, "Y": 0.1853291094303131}, {"X": 0.7850109338760376, "Y": 0.18532955646514893}, {"X": 0.7850109934806824, "Y": 0.19810540974140167}, {"X": 0.7072635889053345, "Y": 0.19810496270656586}]}, "Id": "61a3e397-88e0-4c07-a0e7-5b36c7fd3492"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shipper:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059961751103401184, "Height": 0.012770789675414562, "Left": 0.09232305735349655, "Top": 0.22268018126487732}, "Polygon": [{"X": 0.09232309460639954, "Y": 0.22268018126487732}, {"X": 0.15228481590747833, "Y": 0.22268050909042358}, {"X": 0.15228478610515594, "Y": 0.23545096814632416}, {"X": 0.09232305735349655, "Y": 0.2354506403207779}]}, "Id": "06c1a4b4-5a2d-47e6-8cf9-d87f50d23acb"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "MRB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399498015642166, "Height": 0.00974342878907919, "Left": 0.1647477000951767, "Top": 0.22325244545936584}, "Polygon": [{"X": 0.16474772989749908, "Y": 0.22325244545936584}, {"X": 0.19874268770217896, "Y": 0.22325263917446136}, {"X": 0.19874267280101776, "Y": 0.2329958826303482}, {"X": 0.1647477000951767, "Y": 0.23299568891525269}]}, "Id": "90d9dfd6-e156-4f94-8ca4-428a5b391da5"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "LOGISTICS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07448867708444595, "Height": 0.010012395679950714, "Left": 0.20359404385089874, "Top": 0.2231440246105194}, "Polygon": [{"X": 0.20359405875205994, "Y": 0.2231440246105194}, {"X": 0.2780827283859253, "Y": 0.22314444184303284}, {"X": 0.2780827283859253, "Y": 0.23315641283988953}, {"X": 0.20359404385089874, "Y": 0.2331560105085373}]}, "Id": "b65428a8-712e-437e-a194-fd6c906dee0d"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024362636730074883, "Height": 0.009804081171751022, "Left": 0.282887727022171, "Top": 0.22315777838230133}, "Polygon": [{"X": 0.2828877568244934, "Y": 0.22315777838230133}, {"X": 0.30725038051605225, "Y": 0.22315791249275208}, {"X": 0.30725035071372986, "Y": 0.23296184837818146}, {"X": 0.282887727022171, "Y": 0.2329617142677307}]}, "Id": "9392eeb6-3335-41c3-8c9f-e70270244055"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04254766181111336, "Height": 0.009776907972991467, "Left": 0.31169989705085754, "Top": 0.22327262163162231}, "Polygon": [{"X": 0.31169989705085754, "Y": 0.22327262163162231}, {"X": 0.3542475402355194, "Y": 0.22327284514904022}, {"X": 0.3542475402355194, "Y": 0.23304952681064606}, {"X": 0.31169989705085754, "Y": 0.23304928839206696}]}, "Id": "94e48301-b34f-4742-b801-091408fe8275"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ARMOR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057736750692129135, "Height": 0.009823921136558056, "Left": 0.3582441806793213, "Top": 0.22319070994853973}, "Polygon": [{"X": 0.3582441806793213, "Y": 0.22319070994853973}, {"X": 0.41598090529441833, "Y": 0.223191037774086}, {"X": 0.41598090529441833, "Y": 0.23301462829113007}, {"X": 0.3582441806793213, "Y": 0.233014315366745}]}, "Id": "3abc69f7-e71c-42f4-96ed-8507e52676ba"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Origin:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04813851788640022, "Height": 0.012443787418305874, "Left": 0.6120847463607788, "Top": 0.22298645973205566}, "Polygon": [{"X": 0.6120847463607788, "Y": 0.22298645973205566}, {"X": 0.6602232456207275, "Y": 0.22298672795295715}, {"X": 0.6602233052253723, "Y": 0.2354302555322647}, {"X": 0.6120848059654236, "Y": 0.23542998731136322}]}, "Id": "7470b072-94e0-4de5-9d64-180bd0a6a104"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DFW", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034777652472257614, "Height": 0.009693896397948265, "Left": 0.7075534462928772, "Top": 0.22326555848121643}, "Polygon": [{"X": 0.7075534462928772, "Y": 0.22326555848121643}, {"X": 0.7423310279846191, "Y": 0.22326575219631195}, {"X": 0.7423310875892639, "Y": 0.23295946419239044}, {"X": 0.7075534462928772, "Y": 0.23295927047729492}]}, "Id": "fac18b0b-9273-4f68-bdfb-272d9ba65b19"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020643670111894608, "Height": 0.010183368809521198, "Left": 0.09862078726291656, "Top": 0.25932803750038147}, "Polygon": [{"X": 0.09862080961465836, "Y": 0.25932803750038147}, {"X": 0.11926445364952087, "Y": 0.259328156709671}, {"X": 0.11926443129777908, "Y": 0.2695114016532898}, {"X": 0.09862078726291656, "Y": 0.26951131224632263}]}, "Id": "393a78ee-731e-4c41-9f4c-e83f88e4c11c"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "To:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022879650816321373, "Height": 0.009866969659924507, "Left": 0.12359514087438583, "Top": 0.25986552238464355}, "Polygon": [{"X": 0.12359517067670822, "Y": 0.25986552238464355}, {"X": 0.14647479355335236, "Y": 0.2598656415939331}, {"X": 0.14647477865219116, "Y": 0.2697324752807617}, {"X": 0.12359514087438583, "Y": 0.26973235607147217}]}, "Id": "00960919-2a19-482b-86ae-fcf099ab280d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "2836616", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06406258791685104, "Height": 0.009838614612817764, "Left": 0.1644560694694519, "Top": 0.25967320799827576}, "Polygon": [{"X": 0.1644560843706131, "Y": 0.25967320799827576}, {"X": 0.22851866483688354, "Y": 0.259673535823822}, {"X": 0.22851864993572235, "Y": 0.2695118188858032}, {"X": 0.1644560694694519, "Y": 0.26951146125793457}]}, "Id": "b9c304d2-3b6c-4a0a-98ea-862ca26643fb"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Destination:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0881313905119896, "Height": 0.00998605415225029, "Left": 0.5923346877098083, "Top": 0.25968414545059204}, "Polygon": [{"X": 0.5923346877098083, "Y": 0.25968414545059204}, {"X": 0.6804660558700562, "Y": 0.25968462228775024}, {"X": 0.6804661154747009, "Y": 0.2696702182292938}, {"X": 0.5923347473144531, "Y": 0.2696697413921356}]}, "Id": "680cbab0-7ed7-4b48-8f83-c29168f30520"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "ATL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0275449026376009, "Height": 0.00968901626765728, "Left": 0.70647794008255, "Top": 0.25991469621658325}, "Polygon": [{"X": 0.70647794008255, "Y": 0.25991469621658325}, {"X": 0.7340227961540222, "Y": 0.2599148452281952}, {"X": 0.734022855758667, "Y": 0.2696036994457245}, {"X": 0.70647794008255, "Y": 0.26960355043411255}]}, "Id": "be1d4785-4de8-409b-80ef-c90c40dbb4b5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Class", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04532907158136368, "Height": 0.012770010158419609, "Left": 0.34880879521369934, "Top": 0.29697686433792114}, "Polygon": [{"X": 0.34880879521369934, "Y": 0.29697686433792114}, {"X": 0.3941378593444824, "Y": 0.29697710275650024}, {"X": 0.3941378593444824, "Y": 0.3097468912601471}, {"X": 0.34880879521369934, "Y": 0.309746652841568}]}, "Id": "51af299f-777f-4c72-b027-558f479f65cc"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Adjustments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11681204289197922, "Height": 0.015858732163906097, "Left": 0.39983493089675903, "Top": 0.2969186305999756}, "Polygon": [{"X": 0.39983493089675903, "Y": 0.2969186305999756}, {"X": 0.5166469812393188, "Y": 0.2969192564487457}, {"X": 0.5166469812393188, "Y": 0.3127773702144623}, {"X": 0.39983493089675903, "Y": 0.31277674436569214}]}, "Id": "d386a1ba-c091-40ed-98a1-f83fb4e5c5fd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Booked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055543702095746994, "Height": 0.01030503585934639, "Left": 0.08692061901092529, "Top": 0.3187428414821625}, "Polygon": [{"X": 0.08692064881324768, "Y": 0.3187428414821625}, {"X": 0.14246432483196259, "Y": 0.31874310970306396}, {"X": 0.1424642950296402, "Y": 0.3290478587150574}, {"X": 0.08692061901092529, "Y": 0.3290475904941559}]}, "Id": "d1dea2ae-6840-4cc8-9ece-c70d55d14a56"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "REWWeight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08924861997365952, "Height": 0.012708719819784164, "Left": 0.18453900516033173, "Top": 0.32840272784233093}, "Polygon": [{"X": 0.18453902006149292, "Y": 0.32840272784233093}, {"X": 0.27378761768341064, "Y": 0.32840317487716675}, {"X": 0.27378761768341064, "Y": 0.3411114513874054}, {"X": 0.18453900516033173, "Y": 0.3411110043525696}]}, "Id": "64123cb4-9b52-4452-8a4f-b98e6a2fe4f7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Booked", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05549414083361626, "Height": 0.010364250279963017, "Left": 0.31590449810028076, "Top": 0.32809266448020935}, "Polygon": [{"X": 0.31590449810028076, "Y": 0.32809266448020935}, {"X": 0.3713986277580261, "Y": 0.3280929625034332}, {"X": 0.3713986277580261, "Y": 0.338456928730011}, {"X": 0.31590449810028076, "Y": 0.3384566307067871}]}, "Id": "120f269a-298b-4e4b-a5b7-cbffda7f547d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Class", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037064824253320694, "Height": 0.010313152335584164, "Left": 0.37623944878578186, "Top": 0.3281327188014984}, "Polygon": [{"X": 0.37623944878578186, "Y": 0.3281327188014984}, {"X": 0.41330426931381226, "Y": 0.32813289761543274}, {"X": 0.41330426931381226, "Y": 0.33844587206840515}, {"X": 0.37623944878578186, "Y": 0.33844566345214844}]}, "Id": "7f5a0a28-c6ff-4aee-8ef7-943cb8b4e003"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Adjusted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06714312732219696, "Height": 0.012754467315971851, "Left": 0.4453728199005127, "Top": 0.3281421363353729}, "Polygon": [{"X": 0.4453728199005127, "Y": 0.3281421363353729}, {"X": 0.5125159025192261, "Y": 0.3281424939632416}, {"X": 0.5125159621238708, "Y": 0.3408966064453125}, {"X": 0.4453728199005127, "Y": 0.34089627861976624}]}, "Id": "a8d22b3a-fb81-43ae-b006-2c2f9b6c7639"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Class", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03696746006608009, "Height": 0.010376053862273693, "Left": 0.5172554850578308, "Top": 0.32815271615982056}, "Polygon": [{"X": 0.5172554850578308, "Y": 0.32815271615982056}, {"X": 0.5542229413986206, "Y": 0.32815292477607727}, {"X": 0.5542230010032654, "Y": 0.3385287821292877}, {"X": 0.5172555446624756, "Y": 0.3385286033153534}]}, "Id": "eb7330da-df03-4aef-83ac-8061fefaa4f0"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "NMFC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045012302696704865, "Height": 0.009787515737116337, "Left": 0.594211995601654, "Top": 0.3286419212818146}, "Polygon": [{"X": 0.594211995601654, "Y": 0.3286419212818146}, {"X": 0.6392242908477783, "Y": 0.3286421298980713}, {"X": 0.6392243504524231, "Y": 0.33842942118644714}, {"X": 0.5942120552062988, "Y": 0.33842918276786804}]}, "Id": "2f78eade-dd4e-447a-8137-7a8a525864c0"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03395155072212219, "Height": 0.009637260809540749, "Left": 0.6436429023742676, "Top": 0.32878127694129944}, "Polygon": [{"X": 0.6436429023742676, "Y": 0.32878127694129944}, {"X": 0.6775944232940674, "Y": 0.32878145575523376}, {"X": 0.6775944828987122, "Y": 0.33841854333877563}, {"X": 0.6436429619789124, "Y": 0.3384183645248413}]}, "Id": "2b5c06ac-c968-4c5c-9102-304f05d3e207"}, {"BlockType": "WORD", "Confidence": 96.066650390625, "Text": "P.C.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028079824522137642, "Height": 0.009790395386517048, "Left": 0.7489345073699951, "Top": 0.32866254448890686}, "Polygon": [{"X": 0.7489345073699951, "Y": 0.32866254448890686}, {"X": 0.7770143151283264, "Y": 0.3286626935005188}, {"X": 0.7770143151283264, "Y": 0.33845293521881104}, {"X": 0.7489345073699951, "Y": 0.3384527862071991}]}, "Id": "cec7b3a2-3fdc-4d99-ba5e-5412fc87148d"}, {"BlockType": "WORD", "Confidence": 97.11833953857422, "Text": "F.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0121437544003129, "Height": 0.009592656046152115, "Left": 0.7818962931632996, "Top": 0.3286930024623871}, "Polygon": [{"X": 0.7818962931632996, "Y": 0.3286930024623871}, {"X": 0.7940400242805481, "Y": 0.32869309186935425}, {"X": 0.7940400838851929, "Y": 0.3382856845855713}, {"X": 0.7818963527679443, "Y": 0.3382856249809265}]}, "Id": "16bea380-1a85-459a-b617-0b478f26d02d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05425461009144783, "Height": 0.012347626499831676, "Left": 0.08622292429208755, "Top": 0.33718809485435486}, "Polygon": [{"X": 0.08622295409440994, "Y": 0.33718809485435486}, {"X": 0.14047753810882568, "Y": 0.33718836307525635}, {"X": 0.1404775083065033, "Y": 0.34953573346138}, {"X": 0.08622292429208755, "Y": 0.34953543543815613}]}, "Id": "834b296d-addc-492b-becc-13babcd3cc74"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "100", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026616014540195465, "Height": 0.00987878255546093, "Left": 0.10967487841844559, "Top": 0.3559759259223938}, "Polygon": [{"X": 0.10967490077018738, "Y": 0.3559759259223938}, {"X": 0.13629089295864105, "Y": 0.35597604513168335}, {"X": 0.13629086315631866, "Y": 0.3658547103404999}, {"X": 0.10967487841844559, "Y": 0.36585456132888794}]}, "Id": "6377ab82-8d5e-4f97-9cd4-5ed73ce00ea5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "345", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026320550590753555, "Height": 0.00968103762716055, "Left": 0.2160252332687378, "Top": 0.35600343346595764}, "Polygon": [{"X": 0.216025248169899, "Y": 0.35600343346595764}, {"X": 0.24234578013420105, "Y": 0.3560035824775696}, {"X": 0.24234576523303986, "Y": 0.36568447947502136}, {"X": 0.2160252332687378, "Y": 0.3656843602657318}]}, "Id": "55c7959d-aa3d-4cbc-9a5a-efd4294f5ba7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "300", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0272364504635334, "Height": 0.010146199725568295, "Left": 0.3514963388442993, "Top": 0.3557758629322052}, "Polygon": [{"X": 0.3514963388442993, "Y": 0.3557758629322052}, {"X": 0.3787328004837036, "Y": 0.35577598214149475}, {"X": 0.3787328004837036, "Y": 0.36592206358909607}, {"X": 0.3514963388442993, "Y": 0.36592191457748413}]}, "Id": "c5912f0f-8fbf-451b-a225-8eb79bd51b5c"}, {"BlockType": "WORD", "Confidence": 98.642578125, "Text": "1PC:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02352069690823555, "Height": 0.007554987911134958, "Left": 0.16371987760066986, "Top": 0.40252602100372314}, "Polygon": [{"X": 0.16371989250183105, "Y": 0.40252602100372314}, {"X": 0.1872405707836151, "Y": 0.4025261104106903}, {"X": 0.18724055588245392, "Y": 0.41008099913597107}, {"X": 0.16371987760066986, "Y": 0.4100808799266815}]}, "Id": "7ea76642-fab0-40b5-b60e-3d0c46dad912"}, {"BlockType": "WORD", "Confidence": 98.6744613647461, "Text": "40*48*32", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05326545611023903, "Height": 0.007396589033305645, "Left": 0.18994837999343872, "Top": 0.40237104892730713}, "Polygon": [{"X": 0.18994839489459991, "Y": 0.40237104892730713}, {"X": 0.24321384727954865, "Y": 0.4023713171482086}, {"X": 0.24321383237838745, "Y": 0.40976762771606445}, {"X": 0.18994837999343872, "Y": 0.40976738929748535}]}, "Id": "6a831407-5fda-49e9-8f17-651af989545a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02707354724407196, "Height": 0.010189932771027088, "Left": 0.08598432689905167, "Top": 0.4358912408351898}, "Polygon": [{"X": 0.08598435670137405, "Y": 0.4358912408351898}, {"X": 0.11305787414312363, "Y": 0.43589136004447937}, {"X": 0.11305785179138184, "Y": 0.44608116149902344}, {"X": 0.08598432689905167, "Y": 0.4460810422897339}]}, "Id": "31a777f9-7e71-4c2d-8e30-395b03341823"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04418496787548065, "Height": 0.01049219723790884, "Left": 0.11789325624704361, "Top": 0.4356459379196167}, "Polygon": [{"X": 0.1178932785987854, "Y": 0.4356459379196167}, {"X": 0.16207821667194366, "Y": 0.4356461465358734}, {"X": 0.16207820177078247, "Y": 0.4461381435394287}, {"X": 0.11789325624704361, "Y": 0.446137934923172}]}, "Id": "afb096c8-a714-4671-8bd4-3bb37176d154"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06902780383825302, "Height": 0.012804411351680756, "Left": 0.1666831523180008, "Top": 0.4357777535915375}, "Polygon": [{"X": 0.16668318212032318, "Y": 0.4357777535915375}, {"X": 0.2357109636068344, "Y": 0.43577808141708374}, {"X": 0.23571093380451202, "Y": 0.44858217239379883}, {"X": 0.1666831523180008, "Y": 0.44858184456825256}]}, "Id": "d26f1617-53bd-4c62-9626-9e1a111cacdb"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028617428615689278, "Height": 0.007590871769934893, "Left": 0.2395896315574646, "Top": 0.4382779598236084}, "Polygon": [{"X": 0.2395896464586258, "Y": 0.4382779598236084}, {"X": 0.2682070732116699, "Y": 0.43827810883522034}, {"X": 0.26820704340934753, "Y": 0.4458688497543335}, {"X": 0.2395896315574646, "Y": 0.44586870074272156}]}, "Id": "dc250070-9732-4c80-abc5-679aee114336"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "inspected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07012801617383957, "Height": 0.012642980553209782, "Left": 0.2729226350784302, "Top": 0.4358727037906647}, "Polygon": [{"X": 0.27292266488075256, "Y": 0.4358727037906647}, {"X": 0.34305065870285034, "Y": 0.43587303161621094}, {"X": 0.34305065870285034, "Y": 0.4485156834125519}, {"X": 0.2729226350784302, "Y": 0.4485153555870056}]}, "Id": "bed66289-cd52-4e4c-9952-97d2cc7d0e0f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016935624182224274, "Height": 0.012645089067518711, "Left": 0.3483791649341583, "Top": 0.4357922673225403}, "Polygon": [{"X": 0.3483791649341583, "Y": 0.4357922673225403}, {"X": 0.365314781665802, "Y": 0.43579232692718506}, {"X": 0.365314781665802, "Y": 0.44843733310699463}, {"X": 0.3483791649341583, "Y": 0.44843727350234985}]}, "Id": "e19f961b-2e42-4c07-b952-8e303f7e701d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024278631433844566, "Height": 0.01017285417765379, "Left": 0.369215726852417, "Top": 0.4358058273792267}, "Polygon": [{"X": 0.369215726852417, "Y": 0.4358058273792267}, {"X": 0.3934943675994873, "Y": 0.43580594658851624}, {"X": 0.3934943675994873, "Y": 0.44597867131233215}, {"X": 0.369215726852417, "Y": 0.4459785521030426}]}, "Id": "92b149e6-0540-4d69-867f-3e17f1b95d10"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Forward", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05985117703676224, "Height": 0.010165203362703323, "Left": 0.398794949054718, "Top": 0.4358535408973694}, "Polygon": [{"X": 0.398794949054718, "Y": 0.4358535408973694}, {"X": 0.45864611864089966, "Y": 0.43585383892059326}, {"X": 0.45864611864089966, "Y": 0.4460187554359436}, {"X": 0.398794949054718, "Y": 0.4460184574127197}]}, "Id": "5a5a7ea5-d1ee-4302-a3fc-be253ccb44bb"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Air", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021279271692037582, "Height": 0.009739462286233902, "Left": 0.46310746669769287, "Top": 0.4362635314464569}, "Polygon": [{"X": 0.46310746669769287, "Y": 0.4362635314464569}, {"X": 0.48438674211502075, "Y": 0.4362636208534241}, {"X": 0.48438674211502075, "Y": 0.4460029900074005}, {"X": 0.46310746669769287, "Y": 0.44600290060043335}]}, "Id": "8aa94bc0-337d-4e9f-86c2-279f56d85478"}, {"BlockType": "WORD", "Confidence": 71.18144226074219, "Text": "W &", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03257843479514122, "Height": 0.010641632601618767, "Left": 0.4882550537586212, "Top": 0.4356192350387573}, "Polygon": [{"X": 0.4882550537586212, "Y": 0.4356192350387573}, {"X": 0.5208334922790527, "Y": 0.43561938405036926}, {"X": 0.5208334922790527, "Y": 0.44626086950302124}, {"X": 0.4882550835609436, "Y": 0.4462607204914093}]}, "Id": "a0667e18-e3ee-4f1e-8dd5-b41d1a78ad6c"}, {"BlockType": "WORD", "Confidence": 99.28551483154297, "Text": "R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008860025554895401, "Height": 0.009389716200530529, "Left": 0.525908887386322, "Top": 0.4365246593952179}, "Polygon": [{"X": 0.525908887386322, "Y": 0.4365246593952179}, {"X": 0.5347688794136047, "Y": 0.4365246891975403}, {"X": 0.5347688794136047, "Y": 0.4459143877029419}, {"X": 0.525908887386322, "Y": 0.4459143280982971}]}, "Id": "64a04be0-da4e-4a69-b56e-0a8edb1c1794"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "department.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09140530973672867, "Height": 0.012626987881958485, "Left": 0.5388520359992981, "Top": 0.4358516335487366}, "Polygon": [{"X": 0.5388520359992981, "Y": 0.4358516335487366}, {"X": 0.6302573084831238, "Y": 0.4358520805835724}, {"X": 0.6302573084831238, "Y": 0.448478639125824}, {"X": 0.5388520359992981, "Y": 0.44847819209098816}]}, "Id": "5279bf46-8670-4a14-bbf3-710256ed99b3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02772245556116104, "Height": 0.010309013538062572, "Left": 0.6347852945327759, "Top": 0.4357604384422302}, "Polygon": [{"X": 0.6347852945327759, "Y": 0.4357604384422302}, {"X": 0.6625077128410339, "Y": 0.4357605576515198}, {"X": 0.6625077724456787, "Y": 0.4460694491863251}, {"X": 0.6347852945327759, "Y": 0.44606930017471313}]}, "Id": "70b71783-c1e6-4f81-bb0a-150737eff363"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Airbill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042376987636089325, "Height": 0.010326338931918144, "Left": 0.6664206981658936, "Top": 0.43559154868125916}, "Polygon": [{"X": 0.6664206981658936, "Y": 0.43559154868125916}, {"X": 0.7087976932525635, "Y": 0.4355917274951935}, {"X": 0.7087976932525635, "Y": 0.44591787457466125}, {"X": 0.6664207577705383, "Y": 0.44591766595840454}]}, "Id": "8f8f6530-f23d-4b94-88f9-b5e0fd6908dc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "number,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06138201057910919, "Height": 0.012228350155055523, "Left": 0.7140734791755676, "Top": 0.43571603298187256}, "Polygon": [{"X": 0.7140734791755676, "Y": 0.43571603298187256}, {"X": 0.7754554152488708, "Y": 0.43571633100509644}, {"X": 0.7754554748535156, "Y": 0.44794437289237976}, {"X": 0.7140735387802124, "Y": 0.44794410467147827}]}, "Id": "6452d4b2-fbda-4639-a8fe-392cf75779d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05596683919429779, "Height": 0.01249432098120451, "Left": 0.7800889611244202, "Top": 0.43596193194389343}, "Polygon": [{"X": 0.7800889611244202, "Y": 0.43596193194389343}, {"X": 0.8360557556152344, "Y": 0.4359622001647949}, {"X": 0.8360558152198792, "Y": 0.4484562575817108}, {"X": 0.7800890207290649, "Y": 0.4484560191631317}]}, "Id": "f135e208-46d7-40cc-a19c-************"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026410043239593506, "Height": 0.010479824617505074, "Left": 0.08671103417873383, "Top": 0.45382919907569885}, "Polygon": [{"X": 0.*****************, "Y": 0.45382919907569885}, {"X": 0.11312107741832733, "Y": 0.4538293182849884}, {"X": 0.11312105506658554, "Y": 0.4643090069293976}, {"X": 0.08671103417873383, "Y": 0.46430888772010803}]}, "Id": "7bf69501-ad45-4909-be0e-8e64fe6f1206"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020923197269439697, "Height": 0.010083382949233055, "Left": 0.11858507990837097, "Top": 0.45407384634017944}, "Polygon": [{"X": 0.11858510971069336, "Y": 0.45407384634017944}, {"X": 0.13950827717781067, "Y": 0.4540739357471466}, {"X": 0.13950826227664948, "Y": 0.46415722370147705}, {"X": 0.11858507990837097, "Y": 0.4641571342945099}]}, "Id": "242ca59d-75ed-413c-9c2c-b2d5b67bfddc"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.*****************, "Height": 0.009982587769627571, "Left": 0.*****************, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "a31b31df-32b7-4c49-bab6-2b3598c66099"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "account", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.****************, "Height": 0.009506981819868088, "Left": 0.*****************, "Top": 0.***************}, "Polygon": [{"X": 0.*****************, "Y": 0.***************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.****************}]}, "Id": "6c991a27-7c50-4db1-88d9-f0a75d43e99d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "listed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038479775190353394, "Height": 0.*****************, "Left": 0.*****************, "Top": 0.*****************}, "Polygon": [{"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.***************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}]}, "Id": "386d14db-91dc-4bde-9fa3-ccbe04e59d33"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04430655762553215, "Height": 0.010324451141059399, "Left": 0.2732802927494049, "Top": 0.45380696654319763}, "Polygon": [{"X": 0.2732802927494049, "Y": 0.45380696654319763}, {"X": 0.31758683919906616, "Y": 0.45380717515945435}, {"X": 0.31758683919906616, "Y": 0.4641314148902893}, {"X": 0.2732802927494049, "Y": 0.4641312062740326}]}, "Id": "73471b66-d999-45c1-8dd9-3c8fc8ecfb30"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018115326762199402, "Height": 0.007581703830510378, "Left": 0.32226860523223877, "Top": 0.45651674270629883}, "Polygon": [{"X": 0.32226860523223877, "Y": 0.45651674270629883}, {"X": 0.340383917093277, "Y": 0.4565168023109436}, {"X": 0.340383917093277, "Y": 0.46409842371940613}, {"X": 0.32226860523223877, "Y": 0.46409836411476135}]}, "Id": "7e1ba0ba-b2c3-42da-9581-c0702c890409"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024199193343520164, "Height": 0.010519897565245628, "Left": 0.3449905514717102, "Top": 0.45377296209335327}, "Polygon": [{"X": 0.3449905514717102, "Y": 0.45377296209335327}, {"X": 0.3691897392272949, "Y": 0.45377305150032043}, {"X": 0.3691897392272949, "Y": 0.46429285407066345}, {"X": 0.3449905514717102, "Y": 0.4642927348613739}]}, "Id": "9d592dfd-2cf2-4956-a88a-9bbbd961b480"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "Certificate.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07930456846952438, "Height": 0.010311705060303211, "Left": 0.3741455078125, "Top": 0.4537476599216461}, "Polygon": [{"X": 0.3741455078125, "Y": 0.4537476599216461}, {"X": 0.453450083732605, "Y": 0.45374801754951477}, {"X": 0.453450083732605, "Y": 0.46405935287475586}, {"X": 0.3741455078125, "Y": 0.4640589952468872}]}, "Id": "1a5d7393-e844-4239-8224-239d58986467"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027313750237226486, "Height": 0.01022842712700367, "Left": 0.4581465721130371, "Top": 0.45389482378959656}, "Polygon": [{"X": 0.4581465721130371, "Y": 0.45389482378959656}, {"X": 0.4854603111743927, "Y": 0.4538949429988861}, {"X": 0.4854603111743927, "Y": 0.4641232490539551}, {"X": 0.4581465721130371, "Y": 0.46412310004234314}]}, "Id": "5a810f9f-e566-449b-b052-4cfbcf703d11"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04427348077297211, "Height": 0.010371210984885693, "Left": 0.49012112617492676, "Top": 0.45381954312324524}, "Polygon": [{"X": 0.49012112617492676, "Y": 0.45381954312324524}, {"X": 0.5343945622444153, "Y": 0.45381975173950195}, {"X": 0.5343946218490601, "Y": 0.4641907513141632}, {"X": 0.49012112617492676, "Y": 0.4641905426979065}]}, "Id": "d8b93118-af4c-439a-a396-c7ae4adc03b4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08626270294189453, "Height": 0.0103756757453084, "Left": 0.5390607714653015, "Top": 0.4537923038005829}, "Polygon": [{"X": 0.5390607714653015, "Y": 0.4537923038005829}, {"X": 0.625323474407196, "Y": 0.4537926912307739}, {"X": 0.625323474407196, "Y": 0.4641679525375366}, {"X": 0.5390607714653015, "Y": 0.4641675651073456}]}, "Id": "68ff224d-f97c-4434-a71f-40fd0dac00c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "shows", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045843400061130524, "Height": 0.010228505358099937, "Left": 0.6300129294395447, "Top": 0.45393386483192444}, "Polygon": [{"X": 0.6300129294395447, "Y": 0.45393386483192444}, {"X": 0.6758562922477722, "Y": 0.45393407344818115}, {"X": 0.675856351852417, "Y": 0.4641623795032501}, {"X": 0.6300129890441895, "Y": 0.4641621708869934}]}, "Id": "4ba6f198-9fbe-4af3-b92d-f71bf3f09ced"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "changes", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059165533632040024, "Height": 0.012762131169438362, "Left": 0.6802646517753601, "Top": 0.4539700150489807}, "Polygon": [{"X": 0.6802646517753601, "Y": 0.4539700150489807}, {"X": 0.7394301295280457, "Y": 0.4539702832698822}, {"X": 0.7394301891326904, "Y": 0.4667321443557739}, {"X": 0.6802647113800049, "Y": 0.46673187613487244}]}, "Id": "e772e79b-defb-47cc-9939-0faea45fbac7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0405450239777565, "Height": 0.010242722928524017, "Left": 0.7442159652709961, "Top": 0.45399606227874756}, "Polygon": [{"X": 0.7442159652709961, "Y": 0.45399606227874756}, {"X": 0.7847609519958496, "Y": 0.4539962708950043}, {"X": 0.7847610116004944, "Y": 0.4642387926578522}, {"X": 0.7442160248756409, "Y": 0.46423861384391785}]}, "Id": "f354b836-b9be-496f-a8e2-72ff43c7ff6d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015434826724231243, "Height": 0.009306092746555805, "Left": 0.7893536686897278, "Top": 0.45493775606155396}, "Polygon": [{"X": 0.7893536686897278, "Y": 0.45493775606155396}, {"X": 0.8047884106636047, "Y": 0.45493781566619873}, {"X": 0.8047884702682495, "Y": 0.4642438590526581}, {"X": 0.7893536686897278, "Y": 0.4642437696456909}]}, "Id": "50ded9e9-0045-428c-ab33-b572bcfbae0f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026759153231978416, "Height": 0.010465143248438835, "Left": 0.8092141151428223, "Top": 0.45379963517189026}, "Polygon": [{"X": 0.8092141151428223, "Y": 0.45379963517189026}, {"X": 0.****************, "Y": 0.4537997841835022}, {"X": 0.****************, "Y": 0.46426478028297424}, {"X": 0.809214174747467, "Y": 0.4642646610736847}]}, "Id": "579cc920-d0bd-4b15-a702-c26f3976d7b7"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "shipment.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07235146313905716, "Height": 0.01266644336283207, "Left": 0.08650197088718414, "Top": 0.4725324213504791}, "Polygon": [{"X": 0.08650200814008713, "Y": 0.4725324213504791}, {"X": 0.1588534265756607, "Y": 0.4725327491760254}, {"X": 0.15885339677333832, "Y": 0.48519885540008545}, {"X": 0.08650197088718414, "Y": 0.4851985275745392}]}, "Id": "84994e3c-9c0a-414d-be57-96ab72423455"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "If", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00993371568620205, "Height": 0.010472679510712624, "Left": 0.12913712859153748, "Top": 0.5438684821128845}, "Polygon": [{"X": 0.12913714349269867, "Y": 0.5438684821128845}, {"X": 0.13907083868980408, "Y": 0.5438684821128845}, {"X": 0.1390708088874817, "Y": 0.5543411374092102}, {"X": 0.12913712859153748, "Y": 0.5543410778045654}]}, "Id": "fc941e27-956a-417d-951e-92b7c345a213"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02698484994471073, "Height": 0.01017133891582489, "Left": 0.14215421676635742, "Top": 0.546848475933075}, "Polygon": [{"X": 0.1421542465686798, "Y": 0.546848475933075}, {"X": 0.1691390722990036, "Y": 0.5468485951423645}, {"X": 0.1691390424966812, "Y": 0.5570197701454163}, {"X": 0.14215421676635742, "Y": 0.5570196509361267}]}, "Id": "94396c65-a423-4841-99c7-689eb1bdd7ed"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "have", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034151993691921234, "Height": 0.010529684834182262, "Left": 0.17452538013458252, "Top": 0.5440994501113892}, "Polygon": [{"X": 0.1745253950357437, "Y": 0.5440994501113892}, {"X": 0.20867736637592316, "Y": 0.5440995693206787}, {"X": 0.20867735147476196, "Y": 0.5546291470527649}, {"X": 0.17452538013458252, "Y": 0.5546289682388306}]}, "Id": "633dabc9-46fd-446a-ae58-fbe5c3436f1b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026356834918260574, "Height": 0.01011052168905735, "Left": 0.21337628364562988, "Top": 0.5467739105224609}, "Polygon": [{"X": 0.21337629854679108, "Y": 0.5467739105224609}, {"X": 0.23973311483860016, "Y": 0.5467740297317505}, {"X": 0.23973311483860016, "Y": 0.5568844079971313}, {"X": 0.21337628364562988, "Y": 0.5568842887878418}]}, "Id": "b6ae26bd-3fa3-4d99-9470-b0e6263b4132"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "questions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07042603939771652, "Height": 0.012277865782380104, "Left": 0.24413760006427765, "Top": 0.5447422862052917}, "Polygon": [{"X": 0.24413761496543884, "Y": 0.5447422862052917}, {"X": 0.3145636320114136, "Y": 0.5447425842285156}, {"X": 0.3145636320114136, "Y": 0.5570201873779297}, {"X": 0.24413760006427765, "Y": 0.5570198893547058}]}, "Id": "1cb3448d-a4c2-4a80-a2f1-d4626c11de8d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "about", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04253309220075607, "Height": 0.010374876670539379, "Left": 0.3191348910331726, "Top": 0.5441262125968933}, "Polygon": [{"X": 0.319134920835495, "Y": 0.5441262125968933}, {"X": 0.3616679906845093, "Y": 0.5441263914108276}, {"X": 0.3616679906845093, "Y": 0.5545010566711426}, {"X": 0.3191348910331726, "Y": 0.5545008778572083}]}, "Id": "685de4d3-a89f-4aa9-a03f-89a4e5f72bf0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026375887915492058, "Height": 0.01039279717952013, "Left": 0.3659650683403015, "Top": 0.5441242456436157}, "Polygon": [{"X": 0.3659650683403015, "Y": 0.5441242456436157}, {"X": 0.3923409581184387, "Y": 0.5441243648529053}, {"X": 0.3923409581184387, "Y": 0.5545170307159424}, {"X": 0.3659650683403015, "Y": 0.5545169115066528}]}, "Id": "87020327-7841-47cc-a993-2544dcae8934"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "change", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05153653025627136, "Height": 0.01274249516427517, "Left": 0.39717966318130493, "Top": 0.5442270040512085}, "Polygon": [{"X": 0.39717966318130493, "Y": 0.5442270040512085}, {"X": 0.4487161934375763, "Y": 0.5442272424697876}, {"X": 0.4487161934375763, "Y": 0.5569695234298706}, {"X": 0.39717966318130493, "Y": 0.5569692850112915}]}, "Id": "cb09da28-d4fa-48e5-b42d-02f4752dfaea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "please", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0461089164018631, "Height": 0.012730257585644722, "Left": 0.45427238941192627, "Top": 0.5442695617675781}, "Polygon": [{"X": 0.45427238941192627, "Y": 0.5442695617675781}, {"X": 0.5003812909126282, "Y": 0.5442697405815125}, {"X": 0.5003812909126282, "Y": 0.5569998025894165}, {"X": 0.45427241921424866, "Y": 0.5569996237754822}]}, "Id": "f184fdf3-5adf-44ef-801f-2afb3bf87362"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "contact", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054610397666692734, "Height": 0.009400184266269207, "Left": 0.5051575899124146, "Top": 0.5453200340270996}, "Polygon": [{"X": 0.5051575899124146, "Y": 0.5453200340270996}, {"X": 0.5597679615020752, "Y": 0.5453202724456787}, {"X": 0.55976802110672, "Y": 0.5547202229499817}, {"X": 0.5051575899124146, "Y": 0.5547199845314026}]}, "Id": "08ceea53-a0cf-4ac9-91a9-d8a45b2a208a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02536441758275032, "Height": 0.007777479477226734, "Left": 0.5641263127326965, "Top": 0.5466934442520142}, "Polygon": [{"X": 0.5641263127326965, "Y": 0.5466934442520142}, {"X": 0.5894907116889954, "Y": 0.5466935634613037}, {"X": 0.5894907116889954, "Y": 0.554470956325531}, {"X": 0.5641263127326965, "Y": 0.5544708371162415}]}, "Id": "fe46a701-35e5-47c4-9140-e3798c34db4a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Customer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0711030513048172, "Height": 0.010195686481893063, "Left": 0.5937767624855042, "Top": 0.5444758534431458}, "Polygon": [{"X": 0.5937767624855042, "Y": 0.5444758534431458}, {"X": 0.6648797988891602, "Y": 0.5444761514663696}, {"X": 0.6648797988891602, "Y": 0.5546715259552002}, {"X": 0.5937767624855042, "Y": 0.5546712279319763}]}, "Id": "79a5400f-8f09-437e-a835-609d598bd81f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05229779705405235, "Height": 0.009918980300426483, "Left": 0.668879508972168, "Top": 0.5445112586021423}, "Polygon": [{"X": 0.668879508972168, "Y": 0.5445112586021423}, {"X": 0.7211772799491882, "Y": 0.5445114970207214}, {"X": 0.7211772799491882, "Y": 0.5544302463531494}, {"X": 0.6688795685768127, "Y": 0.5544300079345703}]}, "Id": "8f66842b-cd80-4b0d-bfd6-b0887efc8112"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Center", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048950713127851486, "Height": 0.01011731382459402, "Left": 0.7261142134666443, "Top": 0.544559895992279}, "Polygon": [{"X": 0.7261142134666443, "Y": 0.544559895992279}, {"X": 0.7750648856163025, "Y": 0.5445600748062134}, {"X": 0.7750649452209473, "Y": 0.5546771883964539}, {"X": 0.7261142730712891, "Y": 0.5546770095825195}]}, "Id": "58fb00bb-7b2b-421f-bdf1-4c4344e329c0"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014235839247703552, "Height": 0.009350384585559368, "Left": 0.7794321179389954, "Top": 0.545352041721344}, "Polygon": [{"X": 0.7794321179389954, "Y": 0.545352041721344}, {"X": 0.7936679124832153, "Y": 0.5453521013259888}, {"X": 0.7936679124832153, "Y": 0.5547024011611938}, {"X": 0.7794321179389954, "Y": 0.5547023415565491}]}, "Id": "459fbc1a-1d60-4ccd-9e4d-0bb08ee5d01d"}, {"BlockType": "WORD", "Confidence": 99.453125, "Text": "(800)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03753003105521202, "Height": 0.013037214986979961, "Left": 0.405710369348526, "Top": 0.5620744824409485}, "Polygon": [{"X": 0.405710369348526, "Y": 0.5620744824409485}, {"X": 0.4432404041290283, "Y": 0.5620746612548828}, {"X": 0.4432404041290283, "Y": 0.5751117467880249}, {"X": 0.405710369348526, "Y": 0.5751115679740906}]}, "Id": "303667fa-3104-42a6-addb-23cbe7a07015"}, {"BlockType": "WORD", "Confidence": 98.6328125, "Text": "726-6654", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06954428553581238, "Height": 0.009993589483201504, "Left": 0.4482134282588959, "Top": 0.5627960562705994}, "Polygon": [{"X": 0.4482134282588959, "Y": 0.5627960562705994}, {"X": 0.5177577137947083, "Y": 0.5627963542938232}, {"X": 0.5177577137947083, "Y": 0.5727896690368652}, {"X": 0.4482134282588959, "Y": 0.5727893710136414}]}, "Id": "31d87806-8d61-4642-9eb9-9141dfa11595"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "227d0807-b57c-43a7-bf99-730cef6ea71b", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "227d0807-b57c-43a7-bf99-730cef6ea71b", "content-type": "application/x-amz-json-1.1", "content-length": "64277", "date": "Thu, 18 Sep 2025 18:07:02 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "91f91c6f-3cac-47ed-8cb6-10349ff153a1", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 18:07:06 GMT", "content-type": "application/json", "content-length": "2364", "connection": "keep-alive", "x-amzn-requestid": "91f91c6f-3cac-47ed-8cb6-10349ff153a1"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. We need to classify. The content: \"Forward Air W & R Certificate Airbill #: ******** Date: 1/25/2024 Shipper: MRB LOGISTICS LLC ROAD ARMOR Origin: DFW Bill To: 2836616 Destination: ATL Class Adjustments Booked REWWeight Booked Class Adjusted Class NMFC Item P.C. F. Weight 100 345 300 1PC: 40*48*32 The above shipment was inspected by the Forward Air W & R department. The Airbill number, Shipper and Bill To account listed above on the Certificate. The above information shows changes made to this shipment.\"\n\nThis looks like a \"Certificate\" for weight and class adjustments. Which doc type? The enum has 'coa' for Certificate of Analysis. But this is a \"W & R Certificate\" likely stands for \"Weight & Reclass\" certificate. Not a certificate of analysis. It is a certificate from Forward Air, presumably a \"W&R Certificate\" for weight and reclass. The enum includes \"other\" or maybe \"combined_carrier_documents\"? But it's a single certificate.\n\nCheck if any doc type is \"combined_carrier_documents\" = multiple documents on one page. Not obviously multiple.\n\nCould be \"other\" because it's a certificate. But maybe \"coa\" for certificate of analysis? But the doc type \"coa\" is for measurement certificate, but this seems to be a weight and reclass certificate. The description: \"Forward Air W & R Certificate\". I think it's a \"W&R Certificate\" for weight and reclass. There's no specific enum for that. So classify as \"other\". But maybe it's \"combined_carrier_documents\" if it's a certificate of weight and reclass. But the definition: combined_carrier_documents is \"Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.)\". Not applicable.\n\nThus \"other\". But check that it's not a \"scale_ticket\" because weight details present but also \"Airbill #\". But scale ticket: weight, driver, truck, scale location. This is not.\n\nThus answer: page 1: doc_type = \"other\"."}}}, {"toolUse": {"toolUseId": "tooluse_oHwUnez_SzOXzGYe8ePsdw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "other"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2316, "outputTokens": 486, "totalTokens": 2802}, "metrics": {"latencyMs": 1986}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/24d1bf92_19_coa_2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nForward Air\nForward Air W & R Certificate\nAirbill #: ********\nDate:\n1/25/2024\nShipper: MRB LOGISTICS LLC ROAD ARMOR\nOrigin:\nDFW\nBill To: 2836616\nDestination:\nATL\nClass Adjustments\nBooked\nREWWeight\nBooked Class\nAdjusted Class\nNMFC Item\nP.C. F.\nWeight\n100\n345\n300\n1PC: 40*48*32\nThe above shipment was inspected by the Forward Air W & R department. The Airbill number, Shipper\nand Bill To account listed above on the Certificate. The above information shows changes made to this\nshipment.\nIf you have any questions about this change please contact our Customer Service Center at\n(800) 726-6654\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}