{"version": "0.2.0", "configurations": [{"name": "Debug Classification.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/llm/classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "subProcess": true}, {"name": "Debug Classification.py (Detailed)", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/llm/classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "subProcess": true, "logToFile": true, "debugOptions": ["RedirectOutput"]}, {"name": "Debug Current Python File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "subProcess": true}, {"name": "Debug with Arguments", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/llm/classification.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true, "subProcess": true}, {"name": "Attach to Process", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}], "justMyCode": true}]}