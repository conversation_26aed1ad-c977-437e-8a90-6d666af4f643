{"version": "2.0.0", "tasks": [{"label": "Run Classification.py", "type": "shell", "command": "${workspaceFolder}/venv/bin/python", "args": ["${workspaceFolder}/app/llm/classification.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}, {"label": "Run Classification Tests", "type": "shell", "command": "${workspaceFolder}/venv/bin/python", "args": ["-m", "pytest", "tests/test_classification.py", "-v"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "${workspaceFolder}/venv/bin/pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Activate Virtual Environment", "type": "shell", "command": "source", "args": ["${workspaceFolder}/venv/bin/activate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}