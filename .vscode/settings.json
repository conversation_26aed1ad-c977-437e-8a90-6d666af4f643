{"python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, ".pytest_cache": true, "*.egg-info": true}, "python.envFile": "${workspaceFolder}/.env", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}}